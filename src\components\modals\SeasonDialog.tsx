import { useState, useEffect } from "react";
import { createSeason } from "@/api/seasonApi";
import { useSeasonStore } from "@/store/useSeasonStore";

interface SeasonDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  season?: {
    id: number;
    name: string;
    start_date?: string;
    end_date?: string;
  };
}

export function SeasonDialog({ open, onOpenChange, clubId, season }: SeasonDialogProps) {
  const [name, setName] = useState(season?.name || "");
  const [startDate, setStartDate] = useState(season?.start_date || "");
  const [endDate, setEndDate] = useState(season?.end_date || "");
  const [loading, setLoading] = useState(false);
  const { fetchSeasons, setActiveSeason } = useSeasonStore();

  useEffect(() => {
    setName(season?.name || "");
    setStartDate(season?.start_date || "");
    setEndDate(season?.end_date || "");
  }, [season]);

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setLoading(true);
    try {
      let result;
      if (season?.id) {
        // update
        result = await fetch(`/api/seasons/${season.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ name, start_date: startDate, end_date: endDate })
        });
        setActiveSeason({
          id: season.id,
          club_id: season.club_id ?? clubId,
          name,
          start_date: startDate,
          end_date: endDate
        });
      } else {
        // create
        result = await createSeason(clubId, name, startDate, endDate);
        if (result && result.id) {
          setActiveSeason(result);
        }
      }
      await fetchSeasons(clubId);
      onOpenChange(false);
    } catch (err) {
      alert("Erro ao salvar temporada: " + (err as Error).message);
    } finally {
      setLoading(false);
    }
  }

  if (!open) return null;
  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 z-50 flex items-center justify-center">
      <form className="bg-white rounded shadow p-6 w-full max-w-xs flex flex-col gap-3" onSubmit={handleSubmit}>
        <h2 className="font-semibold text-lg mb-2">{season ? 'Editar Temporada' : 'Nova Temporada'}</h2>
        <input
          className="border rounded px-2 py-1"
          placeholder="Nome da temporada"
          value={name}
          onChange={e => setName(e.target.value)}
          required
        />
        <input
          className="border rounded px-2 py-1"
          type="date"
          value={startDate}
          onChange={e => setStartDate(e.target.value)}
          required
        />
        <input
          className="border rounded px-2 py-1"
          type="date"
          value={endDate}
          onChange={e => setEndDate(e.target.value)}
          required
        />
        <div className="flex gap-2 mt-2">
          <button type="button" className="flex-1 border rounded py-1" onClick={() => onOpenChange(false)} disabled={loading}>
            Cancelar
          </button>
          <button type="submit" className="flex-1 bg-blue-600 text-white rounded py-1" disabled={loading}>
            {loading ? "Salvando..." : "Salvar"}
          </button>
        </div>
      </form>
    </div>
  );
}
