import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "@/hooks/use-toast";
import { jsPDF } from "jspdf";
import autoTable from 'jspdf-autotable';
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { useCurrentClubId } from "@/context/ClubContext";
import { getClubInfo } from "@/api";
import { getAllPendingPayables, ConsolidatedPayable } from "@/api/financialReports";

interface AccountsPayableGeneralReportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// Interface for jsPDF with autoTable
interface jsPDFWithAutoTable extends jsPDF {
  autoTable: typeof autoTable;
  internal: {
    getNumberOfPages: () => number;
    pageSize: {
      width: number;
      height: number;
    };
  };
}

export function AccountsPayableGeneralReportDialog({
  open,
  onOpenChange
}: AccountsPayableGeneralReportDialogProps) {
  const [department, setDepartment] = useState("todos");
  const [category, setCategory] = useState("todos");
  const [loading, setLoading] = useState(false);
  const clubId = useCurrentClubId();

  const handleGenerate = async () => {
    if (!clubId) {
      toast({
        title: "Erro",
        description: "ID do clube não encontrado.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);

    try {
      // Get club info
      const clubInfo = await getClubInfo(clubId);

      // Get all pending payables
      const allPayables = await getAllPendingPayables(clubId);

      // Apply filters
      let filteredPayables = allPayables;

      if (department !== "todos") {
        filteredPayables = filteredPayables.filter(p => p.department === department);
      }

      if (category !== "todos") {
        filteredPayables = filteredPayables.filter(p => p.category === category);
      }

      // Group by department
      const payablesByDepartment: Record<string, ConsolidatedPayable[]> = {};
      filteredPayables.forEach(payable => {
        const dept = payable.department || 'Outros';
        if (!payablesByDepartment[dept]) {
          payablesByDepartment[dept] = [];
        }
        payablesByDepartment[dept].push(payable);
      });

      // Create PDF
      const doc = new jsPDF("portrait", "mm", "a4") as jsPDFWithAutoTable;

      // Add header
      const pageWidth = doc.internal.pageSize.width;
      doc.setFontSize(18);
      doc.text("Relatório Geral de Contas a Pagar", pageWidth / 2, 15, { align: "center" });

      // Add club info
      doc.setFontSize(12);
      doc.text(clubInfo.name, pageWidth / 2, 25, { align: "center" });
      if (clubInfo.address) {
        doc.setFontSize(10);
        doc.text(clubInfo.address, pageWidth / 2, 30, { align: "center" });
      }

      // Add generation date
      doc.setFontSize(10);
      doc.text(`Gerado em: ${format(new Date(), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}`, 15, 40);

      let currentY = 50;

      // Add summary
      const totalAmount = filteredPayables.reduce((sum, p) => sum + p.amount, 0);
      const totalCount = filteredPayables.length;

      doc.setFontSize(12);
      doc.text("Resumo Geral:", 15, currentY);
      currentY += 8;

      doc.setFontSize(10);
      doc.text(`Total de Contas: ${totalCount}`, 15, currentY);
      currentY += 5;
      doc.text(`Valor Total: R$ ${totalAmount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, 15, currentY);
      currentY += 15;

      // Generate tables by department
      const sortedDepartments = Object.keys(payablesByDepartment).sort();

      for (const dept of sortedDepartments) {
        const deptPayables = payablesByDepartment[dept];
        const deptTotal = deptPayables.reduce((sum, p) => sum + p.amount, 0);

        // Department header
        doc.setFontSize(14);
        doc.setFillColor(41, 128, 185);
        doc.rect(15, currentY, pageWidth - 30, 8, 'F');
        doc.setTextColor(255, 255, 255);
        doc.text(`${dept} (${deptPayables.length} itens - R$ ${deptTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 })})`, 17, currentY + 5);
        doc.setTextColor(0, 0, 0);
        currentY += 12;

        // Prepare table data
        const tableData = deptPayables.map(payable => [
          payable.name,
          payable.description,
          format(new Date(payable.due_date || payable.transaction_date), "dd/MM/yyyy", { locale: ptBR }),
          payable.pix_key || 'Não informado',
          payable.role || '-',
          `R$ ${payable.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`
        ]);

        // Add table
        autoTable(doc, {
          head: [["Nome", "Descrição", "Vencimento", "Chave PIX", "Função", "Valor"]],
          body: tableData,
          startY: currentY,
          styles: {
            fontSize: 8,
            cellPadding: 2,
          },
          headStyles: {
            fillColor: [52, 152, 219],
            textColor: 255,
            fontStyle: "bold",
          },
          alternateRowStyles: {
            fillColor: [240, 240, 240],
          },
          margin: { left: 15, right: 15 },
        });

        currentY = (doc as any).lastAutoTable.finalY + 10;

        // Check if we need a new page
        if (currentY > 250) {
          doc.addPage();
          currentY = 20;
        }
      }

      // Add page numbers
      const pageCount = doc.internal.getNumberOfPages();
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setFontSize(8);
        doc.text(`Página ${i} de ${pageCount}`, pageWidth / 2, doc.internal.pageSize.height - 10, { align: "center" });
      }

      // Save the PDF
      const reportTitle = `Contas_a_Pagar_Geral_${format(new Date(), "dd-MM-yyyy")}.pdf`;
      doc.save(reportTitle);

      toast({
        title: "Relatório gerado",
        description: "O relatório geral de contas a pagar foi gerado com sucesso.",
      });

      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao gerar relatório:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao gerar o relatório.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Get unique departments and categories for filters
  const [payables, setPayables] = useState<ConsolidatedPayable[]>([]);

  // Load payables for filter options
  React.useEffect(() => {
    if (open && clubId) {
      getAllPendingPayables(clubId).then(setPayables).catch(console.error);
    }
  }, [open, clubId]);

  const uniqueDepartments = Array.from(new Set(payables.map(p => p.department).filter(Boolean)));
  const uniqueCategories = Array.from(new Set(payables.map(p => p.category).filter(Boolean)));

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Relatório Geral de Contas a Pagar</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            <div>
              <Label htmlFor="department">Departamento</Label>
              <Select value={department} onValueChange={setDepartment}>
                <SelectTrigger id="department">
                  <SelectValue placeholder="Selecione o departamento" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos">Todos os Departamentos</SelectItem>
                  {uniqueDepartments.map((dept) => (
                    <SelectItem key={dept} value={dept}>
                      {dept}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="category">Categoria</Label>
              <Select value={category} onValueChange={setCategory}>
                <SelectTrigger id="category">
                  <SelectValue placeholder="Selecione a categoria" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos">Todas as Categorias</SelectItem>
                  {uniqueCategories.map((cat) => (
                    <SelectItem key={cat} value={cat}>
                      {cat}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleGenerate} disabled={loading}>
            {loading ? "Gerando..." : "Gerar Relatório"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
