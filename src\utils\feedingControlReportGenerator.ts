import { jsPDF } from "jspdf";
import 'jspdf-autotable';
import { ClubInfo } from "@/api/api";
import autoTable from "jspdf-autotable";
import { getClubPrimaryColorRgb } from '@/utils/themeUtils';

// Tipo para jsPDF com autoTable
type jsPDFWithAutoTable = jsPDF & {
  autoTable: typeof autoTable;
  lastAutoTable: {
    finalY: number;
  };
};

/**
 * Gera um relatório de controle de alimentação da base em PDF
 * @param players Lista de jogadores
 * @param supervisor Nome do supervisor responsável
 * @param mealType Tipo de refeição (café da manhã, almoço, etc.)
 * @param clubInfo Informações do clube
 * @param filename Nome do arquivo PDF
 */
export async function generateFeedingControlReport(
  players: any[],
  supervisor: string,
  mealType: string,
  clubInfo: ClubInfo,
  filename: string = 'controle-alimentacao.pdf'
): Promise<Blob> {
  // Criar um novo documento PDF
  const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

  // Adicionar título
  const title = 'Controle de Alimentação Base';
  doc.setFontSize(18);
  doc.text(title, 14, 22);

  // Adicionar informações do clube
  doc.setFontSize(12);
  doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, 14, 30);

  if (clubInfo.address) {
    doc.text(`Endereço: ${clubInfo.address}`, 14, 36);
  }

  if (clubInfo.phone) {
    doc.text(`Telefone: ${clubInfo.phone}`, 14, 42);
  }

  // Tentar adicionar o logo do clube, se disponível
  if (clubInfo.logo_url) {
    try {
      const img = new Image();

      // Criar uma promessa para lidar com o carregamento da imagem
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            // Calcular proporção para manter as proporções
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;

            // Adicionar a imagem ao PDF (alinhada à direita)
            doc.addImage(img, 'PNG', 170, 15, imgWidth, imgHeight);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });

      // Iniciar o carregamento da imagem
      img.src = clubInfo.logo_url;

      // Aguardar o carregamento e processamento da imagem
      await loadImage;
    } catch (logoError) {
      console.error("Erro ao adicionar logo ao PDF:", logoError);
    }
  }

  // Adicionar data e informações da refeição
  const currentDate = new Date().toLocaleDateString('pt-BR');
  doc.setFontSize(10);
  doc.text(`Data: ${currentDate}`, 170, 50, { align: 'right' });

  doc.setFontSize(12);
  doc.text(`Tipo de Alimentação: ${mealType}`, 14, 50);
  doc.text(`Supervisor Responsável: ${supervisor}`, 14, 56);

  // Ordenar jogadores por nome
  const sortedPlayers = [...players].sort((a, b) => a.name.localeCompare(b.name));

  // Preparar cabeçalhos da tabela
  const headers = ['Nome do Jogador', 'Assinatura'];

  // Preparar dados para a tabela
  const tableData = sortedPlayers.map(player => [
    player.name || '-',
    '' // Espaço para assinatura
  ]);

  // Adicionar a tabela ao PDF
  autoTable(doc, {
    startY: 65,
    head: [headers],
    body: tableData,
    theme: 'grid',
    headStyles: { fillColor: getClubPrimaryColorRgb(), textColor: [255, 255, 255], fontStyle: 'bold' },
    columnStyles: {
      0: { cellWidth: 80 },
      1: { cellWidth: 80 }
    },
    styles: {
      cellPadding: 5,
      minCellHeight: 15
    },
    didDrawPage: (data) => {
      // Adicionar cabeçalho em cada página
      if (data.pageNumber > 1) {
        doc.setFontSize(10);
        doc.text(title, 14, 10);
        doc.setFontSize(8);
        doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, 14, 15);
        doc.text(`Data: ${currentDate}`, 14, 20);
        doc.text(`Tipo de Alimentação: ${mealType}`, 14, 25);
      }
    }
  });

  // Adicionar campo para assinatura do supervisor
  const docWithTable = doc as jsPDFWithAutoTable;
  let yPosition = docWithTable.lastAutoTable.finalY + 20;

  doc.setFontSize(12);
  doc.text("Assinatura do Supervisor:", 14, yPosition);
  yPosition += 5;

  // Desenhar linha para assinatura
  doc.setDrawColor(0);
  doc.line(14, yPosition + 15, 100, yPosition + 15);

  // Adicionar nome do supervisor abaixo da linha
  doc.setFontSize(10);
  doc.text(supervisor, 14, yPosition + 25);

  // Adicionar campo para data
  doc.setFontSize(12);
  doc.text("Data:", 120, yPosition);
  yPosition += 5;

  // Desenhar linha para data
  doc.line(120, yPosition + 15, 180, yPosition + 15);

  // Adicionar rodapé
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.text(
      `Relatório gerado em ${currentDate} - Página ${i} de ${pageCount}`,
      14,
      doc.internal.pageSize.height - 10
    );
  }

  // Retornar o PDF como um Blob
  return doc.output('blob');
}
