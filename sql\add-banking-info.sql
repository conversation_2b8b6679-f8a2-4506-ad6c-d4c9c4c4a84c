-- Add banking information fields to players table
ALTER TABLE players
ADD COLUMN IF NOT EXISTS bank_name TEXT,
ADD COLUMN IF NOT EXISTS bank_account_number TEXT,
ADD COLUMN IF NOT EXISTS bank_branch TEXT,
ADD COLUMN IF NOT EXISTS bank_account_type TEXT,
ADD COLUMN IF NOT EXISTS bank_pix_key TEXT;

-- Add comment to explain the purpose of these fields
COMMENT ON COLUMN players.bank_name IS 'Nome do banco do jogador';
COMMENT ON COLUMN players.bank_account_number IS 'Número da conta bancária do jogador';
COMMENT ON COLUMN players.bank_branch IS 'Agência bancária do jogador';
COMMENT ON COLUMN players.bank_account_type IS 'Tipo de conta bancária (corrente, poupança, etc.)';
COMMENT ON COLUMN players.bank_pix_key IS 'Chave PIX do jogador';
