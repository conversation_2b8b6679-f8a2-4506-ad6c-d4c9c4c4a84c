import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { UserList } from "@/components/users/UserList";
import { UserPermissions } from "@/components/users/UserPermissions";
import { UserInvitations } from "@/components/users/UserInvitations";
import { PendingDocuments } from "@/components/documents/PendingDocuments";
import { Users, Mail, FileCheck } from "lucide-react";

interface ClubUser {
  id: string;
  name: string;
  email: string;
  role: string;
  profile_image?: string;
  created_at: string;
}

export default function Usuarios() {
  const [activeTab, setActiveTab] = useState("users");
  const [selectedUser, setSelectedUser] = useState<ClubUser | null>(null);
  
  // Função para gerenciar permissões de um usuário
  const handleManagePermissions = (user: ClubUser) => {
    setSelectedUser(user);
    setActiveTab("permissions");
  };
  
  // Função para voltar para a lista de usuários
  const handleBackToUsers = () => {
    setSelectedUser(null);
    setActiveTab("users");
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Usuários</h1>
        <p className="text-muted-foreground">
          Gerencie os usuários, permissões e convites do seu clube
        </p>
      </div>
      
      <Separator />
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="users" className="flex items-center gap-1">
            <Users className="h-4 w-4" />
            <span>Usuários</span>
          </TabsTrigger>
          <TabsTrigger value="invitations" className="flex items-center gap-1">
            <Mail className="h-4 w-4" />
            <span>Convites</span>
          </TabsTrigger>
          <TabsTrigger value="documents" className="flex items-center gap-1">
            <FileCheck className="h-4 w-4" />
            <span>Documentos Pendentes</span>
          </TabsTrigger>
          {selectedUser && (
            <TabsTrigger value="permissions" className="flex items-center gap-1">
              <span>Permissões: {selectedUser.name}</span>
            </TabsTrigger>
          )}
        </TabsList>
        
        <TabsContent value="users">
          <UserList onManagePermissions={handleManagePermissions} />
        </TabsContent>
        
        <TabsContent value="invitations">
          <UserInvitations />
        </TabsContent>
        
        <TabsContent value="documents">
          <PendingDocuments />
        </TabsContent>
        
        <TabsContent value="permissions">
          {selectedUser && (
            <div className="space-y-4">
              <div>
                <button
                  onClick={handleBackToUsers}
                  className="text-blue-600 hover:underline text-sm flex items-center gap-1"
                >
                  ← Voltar para a lista de usuários
                </button>
              </div>
              <UserPermissions
                userId={selectedUser.id}
                userName={selectedUser.name}
              />
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
