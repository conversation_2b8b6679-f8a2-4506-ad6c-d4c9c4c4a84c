import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Calendar } from "@/components/ui/calendar";
import {
  Bandage,
  FileText,
  MoreHorizontal,
  Plus,
  Activity,
  Calendar as CalendarIcon,
  Clock,
  UserPlus,
  CircleIcon,
  Stethoscope,
  ClipboardList,
  Pill,
  Trash2,
  AlertTriangle,
} from "lucide-react";
import { AgendarSessaoDialog } from "@/components/modals/AgendarSessaoDialog";
import { NovoProntuarioDialog } from "@/components/modals/NovoProntuarioDialog";
import { useMedicalRecordsStore } from "@/store/useMedicalRecordsStore";
import { useCurrentClubId } from "@/context/ClubContext";
import { usePlayersStore } from "@/store/usePlayersStore";
import { useRehabSessionsStore } from "@/store/useRehabSessionsStore";
import { useMedicalAppointmentsStore } from "@/store/useMedicalAppointmentsStore";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { MedicalProfessionalsList } from "@/components/medical/MedicalProfessionalsList";
import { MedicalInventoryView } from "@/components/medical/MedicalInventoryView";
import { MedicalInventoryRequestsView } from "@/components/medical/MedicalInventoryRequestsView";
import { AppointmentsList } from "@/components/medical/AppointmentsList";
import { TreatmentEvolutionList } from "@/components/medical/TreatmentEvolutionList";
import { MedicalExamList } from "@/components/medical/MedicalExamList";
import { useNavigate } from "react-router-dom";
import { updateMedicalRecord, createMedicalRecord, updateRehabSession } from "@/api/api"; // Importar funções necessárias
import { toast } from "@/components/ui/use-toast"; // Importar o toast
import { supabase } from "@/integrations/supabase/client";
import { format, parseISO } from "date-fns";
import { ptBR } from "date-fns/locale";

export default function Medico() {
  const [activeTab, setActiveTab] = useState("agenda");
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [novoProntuarioDialogOpen, setNovoProntuarioDialogOpen] = useState(false);
  const [agendarSessaoDialogOpen, setAgendarSessaoDialogOpen] = useState(false);
  const [editSession, setEditSession] = useState(null);
  const [editMedicalRecord, setEditMedicalRecord] = useState(null);
  const [viewMedicalRecord, setViewMedicalRecord] = useState(null);
  // Filtros de sessões de reabilitação
  const [rehabPlayerFilter, setRehabPlayerFilter] = useState("");
  const [rehabDateFilter, setRehabDateFilter] = useState("");
  // Estado para o diálogo de alta médica
  const [completeTreatmentDialogOpen, setCompleteTreatmentDialogOpen] = useState(false);
  const [selectedSession, setSelectedSession] = useState(null);
  const [treatmentDescription, setTreatmentDescription] = useState("");

  // Estado para o diálogo de exclusão de prontuário
  const [deleteRecordDialogOpen, setDeleteRecordDialogOpen] = useState(false);
  const [recordToDelete, setRecordToDelete] = useState(null);
  const [relatedDataCount, setRelatedDataCount] = useState({ evolutions: 0, exams: 0, prescriptions: 0 });
  // Zustand store
  const { medicalRecords, loading, error, fetchMedicalRecords, deleteMedicalRecord } = useMedicalRecordsStore();
  const { players, fetchPlayers, loading: loadingPlayers } = usePlayersStore();
  const { rehabSessions, loading: loadingRehab, error: errorRehab, fetchRehabSessions, deleteRehabSession } = useRehabSessionsStore();
  const { appointments, loading: loadingAppointments, fetchAppointments } = useMedicalAppointmentsStore();
  const clubId = useCurrentClubId();
  const navigate = useNavigate();

  useEffect(() => {
    async function fetchData() {
      fetchMedicalRecords(clubId);
      if (clubId && players.length === 0 && !loadingPlayers) {
        fetchPlayers(clubId, undefined, { includeInactive: false, includeLoaned: false });
      }
      if (clubId) {
        fetchRehabSessions(clubId);
        fetchAppointments(clubId);
      }
    }
    fetchData();
  }, [fetchMedicalRecords, clubId, fetchPlayers, players.length, loadingPlayers, fetchRehabSessions, fetchAppointments]);

  // Check for critical medical records and show notifications
  useEffect(() => {
    if (medicalRecords.length > 0 && players.length > 0) {
      const criticalRecords = medicalRecords.filter(record => record.severity === "critical");

      if (criticalRecords.length > 0) {
        criticalRecords.forEach(record => {
          const player = players.find(p => p.id === record.player_id);
          const playerName = player ? player.name : "Paciente";

          toast({
            title: "Atenção: Paciente em tratamento crítico",
            description: `${playerName} está com status "Mantém tratamento" e requer atenção.`,
            variant: "destructive",
          });
        });
      }
    }
  }, [medicalRecords, players]);

  const handleCancelSession = async (sessionId: number) => {
    await deleteRehabSession(clubId, sessionId);
    await fetchRehabSessions(clubId);
  };

  const handleCompleteTreatment = async () => {
    if (!selectedSession || !treatmentDescription.trim()) {
      toast({
        title: "Erro",
        description: "Por favor, descreva como foi o tratamento.",
        variant: "destructive",
      });
      return;
    }

    try {
      // 1. Update the rehab session status
      await updateRehabSession(clubId, selectedSession.id, {
        status: "Alta médica",
      });

      // 2. Create a medical record with the treatment description
      const player = players.find(p => p.id === selectedSession.player_id);

      await createMedicalRecord(clubId, {
        club_id: clubId,
        player_id: selectedSession.player_id,
        date: new Date().toISOString().split('T')[0],
        description: `Alta médica: ${treatmentDescription}`,
        doctor: selectedSession.professional,
        doctor_id: selectedSession.professional_id,
        status: "Alta médica",
        severity: "normal",
        completed: true,
      });

      // 3. Refresh data
      await fetchRehabSessions(clubId);
      await fetchMedicalRecords(clubId);

      // 4. Close dialog and reset state
      setCompleteTreatmentDialogOpen(false);
      setSelectedSession(null);
      setTreatmentDescription("");

      toast({
        title: "Tratamento concluído",
        description: `${player?.name || 'Paciente'} recebeu alta médica com sucesso.`,
        variant: "default",
      });
    } catch (error) {
      console.error("Erro ao concluir tratamento:", error);
      toast({
        title: "Erro",
        description: "Não foi possível concluir o tratamento. Tente novamente.",
        variant: "destructive",
      });
    }
  };

  // Verificar dados relacionados ao prontuário
  const checkRelatedData = async (recordId: number) => {
    try {
      // Verificar evoluções de tratamento
      const { data: evolutions } = await supabase
        .from("medical_treatment_evolution")
        .select("id")
        .eq("record_id", recordId);

      // Verificar exames médicos
      const { data: exams } = await supabase
        .from("medical_exams")
        .select("id")
        .eq("record_id", recordId);

      // Verificar prescrições médicas
      const { data: prescriptions } = await supabase
        .from("medical_prescriptions")
        .select("id")
        .eq("record_id", recordId);

      return {
        evolutions: evolutions?.length || 0,
        exams: exams?.length || 0,
        prescriptions: prescriptions?.length || 0,
      };
    } catch (error) {
      console.error("Erro ao verificar dados relacionados:", error);
      return { evolutions: 0, exams: 0, prescriptions: 0 };
    }
  };

  // Abrir diálogo de confirmação para exclusão
  const handleOpenDeleteDialog = async (record: any) => {
    const relatedData = await checkRelatedData(record.id);
    setRelatedDataCount(relatedData);
    setRecordToDelete(record);
    setDeleteRecordDialogOpen(true);
  };

  // Confirmar exclusão do prontuário
  const handleConfirmDeleteRecord = async () => {
    if (!recordToDelete) return;

    try {
      await deleteMedicalRecord(clubId, recordToDelete.id);
      await fetchMedicalRecords(clubId);

      toast({
        title: "Prontuário excluído",
        description: "O prontuário foi excluído com sucesso",
      });

      setDeleteRecordDialogOpen(false);
      setRecordToDelete(null);
      setRelatedDataCount({ evolutions: 0, exams: 0, prescriptions: 0 });
    } catch (error) {
      console.error("Erro ao excluir prontuário:", error);
      toast({
        title: "Erro",
        description: "Não foi possível excluir o prontuário",
        variant: "destructive",
      });
    }
  };

  const handleOpenNovoProntuarioDialog = (record: any = null) => {
    // Se for um encaminhamento não visualizado, marcar como visualizado
    if (record && record.is_referral && !record.viewed) {
      handleMarkAsViewed(record.id);
    }

    setEditMedicalRecord(record);
    setNovoProntuarioDialogOpen(true);
  };

  // Função para marcar um encaminhamento como visualizado
  const handleMarkAsViewed = async (recordId: number) => {
    try {
      // Mostrar feedback visual
      const recordElement = document.getElementById(`record-${recordId}`);
      if (recordElement) {
        recordElement.classList.add("bg-green-50");
        recordElement.classList.add("transition-colors");
        recordElement.classList.add("duration-500");
      }

      await updateMedicalRecord(clubId, recordId, {
        viewed: true,
        viewed_at: new Date().toISOString()
      });

      // Mostrar toast de confirmação
      toast({
        title: "Encaminhamento visualizado",
        description: "O encaminhamento foi marcado como visualizado com sucesso.",
        variant: "default",
      });

      // Atualizar a lista de prontuários
      await fetchMedicalRecords(clubId);
    } catch (error) {
      console.error("Erro ao marcar encaminhamento como visualizado:", error);
      alert("Erro ao marcar encaminhamento como visualizado. Por favor, tente novamente.");
    }
  };

  // Filtro de sessões de reabilitação
  const filteredRehabSessions = rehabSessions.filter((s) => {
    if (rehabPlayerFilter && s.player_id !== rehabPlayerFilter) return false;
    if (rehabDateFilter && s.date !== rehabDateFilter) return false;
    return true;
  });

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Departamento Médico</h1>
        <p className="text-muted-foreground">
          Gestão de saúde, lesões e reabilitação dos jogadores
        </p>
      </div>

      <div className="flex flex-col md:flex-row gap-6">
        <div className="w-full md:w-2/3 space-y-6">
          <Tabs defaultValue="agenda" onValueChange={setActiveTab} className="w-full">
            <TabsList className="mb-6">
              <TabsTrigger value="agenda">Agenda</TabsTrigger>
              <TabsTrigger value="reabilitacao">Reabilitação</TabsTrigger>
              <TabsTrigger value="prontuarios">Prontuário</TabsTrigger>
              <TabsTrigger value="profissionais">Profissionais</TabsTrigger>
              <TabsTrigger value="estoque">Farmácia</TabsTrigger>
            </TabsList>

            <TabsContent value="prontuarios">
              <Card>
                <CardHeader className="pb-0 flex flex-col sm:flex-row justify-between">
                  <div>
                    <CardTitle>Prontuários Médicos</CardTitle>
                    <CardDescription>
                      Histórico médico e acompanhamento de lesões
                    </CardDescription>
                  </div>
                  <div className="flex justify-end mb-4 gap-2">
                    <Button className="bg-team-blue hover:bg-blue-700" onClick={() => handleOpenNovoProntuarioDialog()}>
                      <Plus className="h-4 w-4 mr-2" />
                      Novo Prontuário
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Jogador</TableHead>
                          <TableHead>Descrição</TableHead>
                          <TableHead className="hidden md:table-cell">Data Início</TableHead>
                          <TableHead className="hidden lg:table-cell">Médico</TableHead>
                          <TableHead></TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {medicalRecords.map((record) => {
                          const player = players.find((p) => p.id === record.player_id);
                          const isUnviewedReferral = record.is_referral && !record.viewed;

                          return (
                            <TableRow
                              key={record.id}
                              id={`record-${record.id}`}
                              className={
                                isUnviewedReferral
                                  ? "bg-blue-50"
                                  : record.severity === "critical"
                                  ? "bg-red-50"
                                  : ""
                              }
                            >
                              <TableCell className="font-medium">
                                <div className="flex items-center gap-2">
                                  <Avatar className="h-8 w-8">
                                    <AvatarFallback className="bg-team-blue text-white">
                                      {player ? player.name.slice(0, 2).toUpperCase() : record.player_id.slice(0, 2).toUpperCase()}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div className="flex items-center gap-2">
                                    {player ? player.name : record.player_id}
                                    {isUnviewedReferral && (
                                      <Badge className="bg-blue-500 text-white">Novo</Badge>
                                    )}
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                {record.is_referral && (
                                  <div className="mb-1">
                                    <Badge variant="outline" className="border-blue-200 bg-blue-50 text-blue-800 mb-1">
                                      Encaminhamento
                                    </Badge>
                                    {record.referred_by && (
                                      <span className="text-xs text-muted-foreground ml-2">
                                        por {record.referred_by}
                                      </span>
                                    )}
                                  </div>
                                )}
                                {record.symptoms && record.is_referral ? (
                                  <div>
                                    <span className="font-medium">Sintomas: </span>
                                    {record.symptoms}
                                  </div>
                                ) : (
                                  record.description
                                )}
                              </TableCell>

                              <TableCell className="hidden md:table-cell">{record.date}</TableCell>
                              <TableCell className="hidden lg:table-cell">{record.doctor || "-"}</TableCell>
                              <TableCell>
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    {isUnviewedReferral && (
                                      <DropdownMenuItem onClick={() => handleMarkAsViewed(record.id)}>
                                        Marcar como visto
                                      </DropdownMenuItem>
                                    )}
                                    {!record.completed ? (
                                      <DropdownMenuItem onClick={() => handleOpenNovoProntuarioDialog(record)}>
                                        {record.is_referral && !record.viewed ? "Atender encaminhamento" : "Atualizar prontuário"}
                                      </DropdownMenuItem>
                                    ) : (
                                      <DropdownMenuItem disabled className="text-muted-foreground">
                                        Tratamento concluído
                                      </DropdownMenuItem>
                                    )}
                                    <DropdownMenuItem onClick={() => setViewMedicalRecord(record)}>Ver detalhes</DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem className="text-red-600" onClick={() => handleOpenDeleteDialog(record)}>
                                      <Trash2 className="mr-2 h-4 w-4" />
                                      Excluir prontuário
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>

              {viewMedicalRecord && (
                <div className="mt-6 space-y-6">
                  <TreatmentEvolutionList
                    recordId={viewMedicalRecord.id}
                    title="Evolução do Tratamento"
                    description="Acompanhamento da evolução do tratamento"
                  />

                  <MedicalExamList
                    recordId={viewMedicalRecord.id}
                    playerId={viewMedicalRecord.player_id}
                    title="Exames Médicos"
                    description="Exames solicitados para este paciente"
                  />
                </div>
              )}
            </TabsContent>

            <TabsContent value="agenda">
              <AppointmentsList
                title="Agenda de Atendimentos"
                description="Gerencie os agendamentos médicos"
                showFilters={true}
                showScheduleButton={true}
              />
            </TabsContent>

            <TabsContent value="reabilitacao">
              <Card>
                <CardHeader className="pb-0 flex flex-col sm:flex-row justify-between">
                  <div>
                    <CardTitle>Agenda de Reabilitação</CardTitle>
                    <CardDescription>
                      Sessões e atividades de recuperação programadas
                    </CardDescription>
                  </div>
                  <div className="flex flex-col md:flex-row gap-2 items-end mb-4">
                    <div className="flex gap-2">
                      <select
                        className="border rounded px-2 py-1"
                        value={rehabPlayerFilter}
                        onChange={e => setRehabPlayerFilter(e.target.value)}
                      >
                        <option value="">Todos os atletas</option>
                        {players.map((p) => (
                          <option key={p.id} value={p.id}>{p.name}</option>
                        ))}
                      </select>
                      <Input
                        type="date"
                        value={rehabDateFilter}
                        onChange={e => setRehabDateFilter(e.target.value)}
                        className="w-36"
                      />
                    </div>
                    <Button className="bg-team-green hover:bg-green-700" onClick={() => { setEditSession(null); setAgendarSessaoDialogOpen(true); }}>
                      <Plus className="h-4 w-4 mr-2" />
                      Agendar Sessão
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="pt-6">
                  {loadingRehab ? (
                    <div className="text-center py-8 text-muted-foreground">Carregando sessões...</div>
                  ) : errorRehab ? (
                    <div className="text-center py-8 text-red-600">{errorRehab}</div>
                  ) : filteredRehabSessions.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">Nenhuma sessão encontrada.</div>
                  ) : (
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Jogador</TableHead>
                            <TableHead>Atividade</TableHead>
                            <TableHead>Data</TableHead>
                            <TableHead className="hidden md:table-cell">Horário</TableHead>
                            <TableHead className="hidden lg:table-cell">Duração</TableHead>
                            <TableHead className="hidden lg:table-cell">Profissional</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead></TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredRehabSessions.map((session) => {
                            const player = players.find((p) => p.id === session.player_id);
                            return (
                              <TableRow key={session.id}>
                                <TableCell className="font-medium">
                                  <div className="flex items-center gap-2">
                                    <Avatar className="h-8 w-8">
                                      <AvatarFallback className="bg-team-blue text-white">
                                        {player ? player.name.slice(0, 2).toUpperCase() : session.player_id.slice(0, 2).toUpperCase()}
                                      </AvatarFallback>
                                    </Avatar>
                                    <span>{player ? player.name : session.player_id}</span>
                                  </div>
                                </TableCell>
                                <TableCell>{session.activity}</TableCell>
                                <TableCell>{session.date}</TableCell>
                                <TableCell className="hidden md:table-cell">{session.time}</TableCell>
                                <TableCell className="hidden lg:table-cell">{session.duration} min</TableCell>
                                <TableCell className="hidden lg:table-cell">{session.professional}</TableCell>
                                <TableCell>
                                  {session.status === "Alta médica" ? (
                                    <Badge className="bg-green-500">Alta médica</Badge>
                                  ) : session.status === "Em tratamento" ? (
                                    <Badge className="bg-amber-500">Em tratamento</Badge>
                                  ) : session.status === "Treina e joga" ? (
                                    <Badge className="bg-blue-500">Treina e joga</Badge>
                                  ) : (
                                    <Badge variant="outline">{session.status}</Badge>
                                  )}
                                </TableCell>
                                <TableCell>
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                        <MoreHorizontal className="h-4 w-4" />
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                      <DropdownMenuItem onClick={() => { setEditSession(session); setAgendarSessaoDialogOpen(true); }}>Editar sessão</DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => { setEditSession(session); setAgendarSessaoDialogOpen(true); }}>Reagendar</DropdownMenuItem>
                                      <DropdownMenuSeparator />
                                      {session.status !== "Alta médica" && (
                                        <DropdownMenuItem
                                          onClick={() => {
                                            setSelectedSession(session);
                                            setTreatmentDescription("");
                                            setCompleteTreatmentDialogOpen(true);
                                          }}
                                        >
                                          Dar alta médica
                                        </DropdownMenuItem>
                                      )}
                                      <DropdownMenuSeparator />
                                      <DropdownMenuItem className="text-red-600" onClick={() => handleCancelSession(session.id)}>Cancelar sessão</DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </TableCell>
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="profissionais">
              <MedicalProfessionalsList onEdit={(professional) => navigate(`/medicos/editar/${professional.id}`)} />
            </TabsContent>

            <TabsContent value="estoque">
              <div className="space-y-6">
                <MedicalInventoryView clubId={clubId} />
                <MedicalInventoryRequestsView clubId={clubId} />
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <div className="w-full md:w-1/3">
          <Card>
            <CardHeader>
              <CardTitle>Visão Geral</CardTitle>
              <CardDescription>Resumo do departamento médico</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-amber-50 p-4 rounded-lg flex flex-col">
                    <div className="flex items-center gap-2 text-amber-800">
                      <Bandage className="h-5 w-5" />
                      <span className="font-medium">Em Tratamento</span>
                    </div>
                    <span className="text-2xl font-bold text-amber-800 mt-2">{medicalRecords.filter(r => r.status === "Em tratamento").length}</span>
                  </div>
                  <div className="bg-primary/10 p-4 rounded-lg flex flex-col">
                    <div className="flex items-center gap-2 text-primary">
                      <Activity className="h-5 w-5" />
                      <span className="font-medium">Reabilitação</span>
                    </div>
                    <span className="text-2xl font-bold text-primary mt-2">{rehabSessions.length}</span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-green-50 p-4 rounded-lg flex flex-col">
                    <div className="flex items-center gap-2 text-green-800">
                      <Stethoscope className="h-5 w-5" />
                      <span className="font-medium">Liberados</span>
                    </div>
                    <span className="text-2xl font-bold text-green-800 mt-2">
                      {medicalRecords.filter(r => r.status === "Liberado" || r.status === "Alta do dep médico").length}
                    </span>
                  </div>
                  <div className="bg-red-50 p-4 rounded-lg flex flex-col">
                    <div className="flex items-center gap-2 text-red-800">
                      <ClipboardList className="h-5 w-5" />
                      <span className="font-medium">Treina/Trata</span>
                    </div>
                    <span className="text-2xl font-bold text-red-800 mt-2">
                      {medicalRecords.filter(r => r.status === "Treina e trata" || r.status === "Treinando / tratando").length}
                    </span>
                  </div>
                </div>

                <div className="mt-6">
                  <h3 className="font-medium text-sm mb-2 flex items-center gap-2">
                    <CalendarIcon className="h-4 w-4" />
                    Calendário
                  </h3>
                  <Calendar
                    mode="single"
                    selected={date}
                    onSelect={setDate}
                    className="border rounded-md"
                  />
                </div>

                <div className="mt-4">
                  <h3 className="font-medium text-sm mb-2 flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Próximos Atendimentos
                  </h3>
                  <div className="space-y-3">
                    {appointments.filter(a => a.status === "Agendada").slice(0, 2).map((appointment, idx) => {
                      const player = players.find((p) => p.id === appointment.player_id);
                      return (
                        <div key={idx} className="border rounded-md p-3 flex justify-between">
                          <div>
                            <p className="font-medium text-sm">{appointment.appointment_type}</p>
                            <p className="text-xs text-muted-foreground">
                              {player ? player.name : appointment.player_id} · {format(parseISO(appointment.appointment_date), "dd/MM/yyyy", { locale: ptBR })}
                            </p>
                          </div>
                          <div className="text-xs font-medium bg-gray-100 h-fit px-2 py-1 rounded">
                            {appointment.appointment_time.substring(0, 5)}
                          </div>
                        </div>
                      );
                    })}

                    {appointments.filter(a => a.status === "Agendada").length === 0 && (
                      <div className="text-sm text-muted-foreground text-center py-2">
                        Nenhum agendamento próximo
                      </div>
                    )}
                  </div>
                </div>

                <div className="mt-4">
                  <h3 className="font-medium text-sm mb-2 flex items-center gap-2">
                    <Pill className="h-4 w-4" />
                    Status de Saúde
                  </h3>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                      <span className="text-sm">Alta médica</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-amber-500"></div>
                      <span className="text-sm">Em tratamento</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                      <span className="text-sm">Treina e joga</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      <NovoProntuarioDialog open={novoProntuarioDialogOpen} onOpenChange={(open) => {
        setNovoProntuarioDialogOpen(open);
        if (!open) setEditMedicalRecord(null);
      }} clubId={clubId} editRecord={editMedicalRecord} />
      <AgendarSessaoDialog open={agendarSessaoDialogOpen} onOpenChange={setAgendarSessaoDialogOpen} clubId={clubId} session={editSession} />

      {/* Dialog for completing treatment */}
      <Dialog open={completeTreatmentDialogOpen} onOpenChange={setCompleteTreatmentDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Dar Alta Médica</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {selectedSession && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="player">Paciente</Label>
                  <Input
                    id="player"
                    value={players.find(p => p.id === selectedSession.player_id)?.name || selectedSession.player_id}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="activity">Atividade</Label>
                  <Input id="activity" value={selectedSession.activity} readOnly className="bg-gray-50" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="treatment-description">Descreva como foi o tratamento*</Label>
                  <Textarea
                    id="treatment-description"
                    placeholder="Descreva detalhes sobre o tratamento realizado e o motivo da alta médica..."
                    value={treatmentDescription}
                    onChange={(e) => setTreatmentDescription(e.target.value)}
                    rows={4}
                  />
                  <p className="text-xs text-muted-foreground">
                    Esta descrição será registrada no prontuário médico do paciente.
                  </p>
                </div>
              </>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setCompleteTreatmentDialogOpen(false)}>Cancelar</Button>
            <Button onClick={handleCompleteTreatment}>Confirmar Alta Médica</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      {viewMedicalRecord && (
        <Dialog open={!!viewMedicalRecord} onOpenChange={() => setViewMedicalRecord(null)}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>
                {viewMedicalRecord.is_referral ? "Detalhes do Encaminhamento" : "Detalhes do Prontuário"}
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-3">
              <div><b>Paciente:</b> {players.find(p => p.id === viewMedicalRecord.player_id)?.name || viewMedicalRecord.player_id}</div>
              <div><b>Data:</b> {viewMedicalRecord.date}</div>

              {viewMedicalRecord.completed && (
                <div className="mt-1">
                  <Badge variant="outline" className="border-green-200 bg-green-50 text-green-800">
                    Tratamento concluído
                  </Badge>
                </div>
              )}

              {viewMedicalRecord.severity && (
                <div className="flex items-center gap-2">
                  <b>Situação:</b>
                  <div className="flex items-center gap-1">
                    <CircleIcon
                      className={`h-4 w-4 ${
                        viewMedicalRecord.severity === "normal"
                          ? "text-green-500"
                          : viewMedicalRecord.severity === "warning"
                          ? "text-yellow-500"
                          : "text-red-500"
                      }`}
                    />
                    <span>
                      {viewMedicalRecord.severity === "normal"
                        ? "Liberado"
                        : viewMedicalRecord.severity === "warning"
                        ? "Em transição"
                        : "Mantém tratamento"}
                    </span>
                  </div>
                </div>
              )}

              {viewMedicalRecord.is_referral && (
                <>
                  {viewMedicalRecord.referred_by && (
                    <div><b>Encaminhado por:</b> {viewMedicalRecord.referred_by}</div>
                  )}
                  {viewMedicalRecord.symptoms && (
                    <div><b>Sintomas:</b> {viewMedicalRecord.symptoms}</div>
                  )}
                </>
              )}

              <div><b>{viewMedicalRecord.is_referral ? "Observações:" : "Descrição:"}</b> {viewMedicalRecord.description}</div>
              <div><b>Médico:</b> {viewMedicalRecord.doctor}</div>

              {viewMedicalRecord.is_referral && (
                <div>
                  <b>Status de visualização:</b> {viewMedicalRecord.viewed ?
                    `Visualizado em ${new Date(viewMedicalRecord.viewed_at).toLocaleString()}` :
                    "Não visualizado"}
                </div>
              )}
            </div>
            <DialogFooter>
              {viewMedicalRecord.is_referral && !viewMedicalRecord.viewed && (
                <Button
                  variant="secondary"
                  onClick={() => {
                    handleMarkAsViewed(viewMedicalRecord.id);
                    setViewMedicalRecord(null);
                  }}
                >
                  Marcar como visto
                </Button>
              )}
              {!viewMedicalRecord.completed ? (
                <Button
                  onClick={() => {
                    handleOpenNovoProntuarioDialog(viewMedicalRecord);
                    setViewMedicalRecord(null);
                  }}
                >
                  {viewMedicalRecord.is_referral && !viewMedicalRecord.viewed ?
                    "Atender encaminhamento" :
                    "Editar"}
                </Button>
              ) : (
                <Button disabled>Tratamento concluído</Button>
              )}
              <Button variant="outline" onClick={() => setViewMedicalRecord(null)}>Fechar</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Delete Medical Record Dialog */}
      {recordToDelete && (
        <Dialog open={deleteRecordDialogOpen} onOpenChange={setDeleteRecordDialogOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                Excluir Prontuário Médico
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-800 font-medium mb-2">
                  ⚠️ Atenção: Esta ação não pode ser desfeita!
                </p>
                <p className="text-sm text-red-700">
                  Você está prestes a excluir permanentemente o prontuário médico de{" "}
                  <strong>{players.find(p => p.id === recordToDelete.player_id)?.name || recordToDelete.player_id}</strong>.
                </p>
              </div>

              {(relatedDataCount.evolutions > 0 || relatedDataCount.exams > 0 || relatedDataCount.prescriptions > 0) && (
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                  <p className="text-sm text-yellow-800 font-medium mb-2">
                    📋 Dados relacionados que serão excluídos:
                  </p>
                  <ul className="text-sm text-yellow-700 space-y-1">
                    {relatedDataCount.evolutions > 0 && (
                      <li>• {relatedDataCount.evolutions} evolução(ões) de tratamento</li>
                    )}
                    {relatedDataCount.exams > 0 && (
                      <li>• {relatedDataCount.exams} exame(s) médico(s)</li>
                    )}
                    {relatedDataCount.prescriptions > 0 && (
                      <li>• {relatedDataCount.prescriptions} prescrição(ões) médica(s)</li>
                    )}
                  </ul>
                </div>
              )}

              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  <strong>Prontuário:</strong> {recordToDelete.description}
                </p>
                <p className="text-sm text-muted-foreground">
                  <strong>Data:</strong> {recordToDelete.date}
                </p>
                {recordToDelete.doctor && (
                  <p className="text-sm text-muted-foreground">
                    <strong>Médico:</strong> {recordToDelete.doctor}
                  </p>
                )}
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setDeleteRecordDialogOpen(false)}>
                Cancelar
              </Button>
              <Button variant="destructive" onClick={handleConfirmDeleteRecord}>
                <Trash2 className="mr-2 h-4 w-4" />
                Excluir Permanentemente
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
