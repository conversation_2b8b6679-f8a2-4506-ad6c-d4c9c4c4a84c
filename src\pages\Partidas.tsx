import { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  ActivityIcon,
  Plus,
  CalendarIcon,
  Clock,
  MapPin,
  Check,
  Trash2,
  Loader2,
  ChevronDown,
  ChevronRight,
  AlertTriangle,
  Lightbulb,
  Play,
  Pause,
  StopCircle,
  RefreshCw,
  Download,
  FileText,
  Square,
  Target
} from "lucide-react";
import { NovaTarefaDialog } from "@/components/modals/NovaTarefaDialog";
import { AdicionarJogoDialog } from "@/components/modals/AdicionarJogoDialog";
import { FinalizarPartidaDialog } from "@/components/modals/FinalizarPartidaDialog";
import { useGamesStore } from "@/store/useGamesStore";
import { useMatchHistoryStore } from "@/store/useMatchHistoryStore";
import { useTasksStore } from "@/store/useTasksStore";
import { useSeasonStore } from "@/store/useSeasonStore";
import { useClubInfoStore } from "@/store/useClubInfoStore";
import type { UpcomingMatch, MatchHistory, Player, MatchEvent, GoalEvent, CardEvent, SubstitutionEvent, NoteEvent, Opponent, Competition } from "@/api/api";
import { getPlayers, getCategoryPlayers, getMatchEvents, createGoalEvent, createCardEvent, createSubstitutionEvent, createNoteEvent, deleteMatchEvent, getOpponents, getCompetitions, createUpcomingMatch, savePlayerMatchStatistics, getPlayerMatchStatistics } from "@/api/api";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/components/ui/use-toast";
import { migrateMatchResults } from "@/utils/migrateMatchResults";

// Funções utilitárias para cálculo dinâmico (corrigidas para clubId numérico)
function calcularHistoricoContraAdversario(historicoPartidas: MatchHistory[], adversario?: string, clubId?: string | number) {
  let vitorias = 0, empates = 0, derrotas = 0;
  if (!adversario || !clubId) return { vitorias, empates, derrotas };
  const clubIdNumber = typeof clubId === "string" ? Number(clubId) : clubId;

  historicoPartidas.forEach(partida => {
    if (partida.opponent === adversario) {
      // Verificar o resultado diretamente pelo campo result
      if (partida.result === "win") {
        vitorias++;
      } else if (partida.result === "draw") {
        empates++;
      } else if (partida.result === "loss") {
        derrotas++;
      }
    }
  });

  return { vitorias, empates, derrotas };
}

function calcularEstatisticasRecentes(historicoPartidas: MatchHistory[], maxJogos = 5, clubId?: string | number) {
  const ultimosJogos = historicoPartidas.slice(0, maxJogos);
  let golsMarcados = 0, golsSofridos = 0, vitorias = 0, empates = 0, derrotas = 0;
  if (!clubId) return { golsMarcados, golsSofridos, aproveitamento: 0 };

  ultimosJogos.forEach(partida => {
    // Somar gols marcados e sofridos
    golsMarcados += partida.score_home ?? 0;
    golsSofridos += partida.score_away ?? 0;

    // Contar vitórias, empates e derrotas
    if (partida.result === "win") vitorias++;
    else if (partida.result === "draw") empates++;
    else if (partida.result === "loss") derrotas++;
  });

  const total = ultimosJogos.length;
  const aproveitamento = total > 0 ? Math.round(((vitorias * 3 + empates) / (total * 3)) * 100) : 0;
  return { golsMarcados, golsSofridos, aproveitamento };
}

// Função utilitária para status real do resultado
function getResultadoStatus(partida: MatchHistory, clubId?: string | number) {
  if (!clubId) return { letra: "-", cor: "bg-gray-400" };

  // Usar diretamente o campo result
  if (partida.result === "win") return { letra: "V", cor: "bg-emerald-500" };
  if (partida.result === "draw") return { letra: "E", cor: "bg-gray-400" };
  if (partida.result === "loss") return { letra: "D", cor: "bg-red-500" };

  // Fallback para caso o campo result não esteja definido
  return { letra: "-", cor: "bg-gray-400" };
}

// Tipo para o clubInfo
type ClubInfo = {
  name: string;
  [key: string]: unknown;
};

// Função utilitária para obter o nome do time com base no tipo de partida
function getTeamName(partida: UpcomingMatch | null | undefined, clubInfo: ClubInfo | null | undefined, isHome: boolean) {
  if (!partida) return "";
  if (isHome) {
    return partida.type === "casa" ? clubInfo?.name : partida.opponent;
  } else {
    return partida.type === "casa" ? partida.opponent : clubInfo?.name;
  }
}

export default function Partidas() {
  const [novaTarefaDialogOpen, setNovaTarefaDialogOpen] = useState(false);
  const [adicionarJogoDialogOpen, setAdicionarJogoDialogOpen] = useState(false);
  const [finalizarDialogOpen, setFinalizarDialogOpen] = useState(false);
  const [partidaParaFinalizar, setPartidaParaFinalizar] = useState<UpcomingMatch | null>(null);
  // Estados para a gestão de partidas
  const [partidaSelecionada, setPartidaSelecionada] = useState<string>("");
  const [gameState, setGameState] = useState({
    homeScore: 0,
    awayScore: 0,
    timer: "00:00",
    isLive: false,
    isPaused: false,
    startTime: 0,
    elapsedTime: 0,
    timerInterval: null as NodeJS.Timeout | null
  });
  const [activeTab, setActiveTab] = useState("nota");
  const [gameNote, setGameNote] = useState("");
  const [analiseAdversarioOpen, setAnaliseAdversarioOpen] = useState(false);
  const [adversarioSelecionado, setAdversarioSelecionado] = useState("");
  const [competicaoSelecionada, setCompeticaoSelecionada] = useState("");
  const [dataPartida, setDataPartida] = useState("");
  const [horarioPartida, setHorarioPartida] = useState("");
  const [localPartida, setLocalPartida] = useState("");
  const [tipoJogo, setTipoJogo] = useState<"casa" | "fora" | "">("");
  const [idaVolta, setIdaVolta] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isCreatingMatch, setIsCreatingMatch] = useState(false);
  const [analiseData, setAnaliseData] = useState({
    tecnico: "",
    formacao: "",
    pontosFortes: [""],
    pontosFracos: [""],
    estrategias: [""]
  });
  const [sectionsVisible, setSectionsVisible] = useState({
    pontosFortes: true,
    pontosFracos: true,
    estrategias: true
  });

  // Estados para jogadores e eventos da partida
  const [players, setPlayers] = useState<Player[]>([]);
  const [playerStats, setPlayerStats] = useState<Record<string, any>>({});
  const [matchEvents, setMatchEvents] = useState<MatchEvent[]>([]);
  const [loadingPlayers, setLoadingPlayers] = useState(false);
  const [loadingEvents, setLoadingEvents] = useState(false);
  const [opponents, setOpponents] = useState<Opponent[]>([]);
  const [loadingOpponents, setLoadingOpponents] = useState(false);
  const [competitions, setCompetitions] = useState<Competition[]>([]);
  const [loadingCompetitions, setLoadingCompetitions] = useState(false);
  const [selectedPlayer, setSelectedPlayer] = useState("");
  const [selectedAssistPlayer, setSelectedAssistPlayer] = useState("");
  const [selectedCardType, setSelectedCardType] = useState<"amarelo" | "vermelho" | "segundo-amarelo" | "">("");
  const [selectedGoalType, setSelectedGoalType] = useState<"normal" | "penalti" | "falta" | "contra" | "">("");
  const [goalTeam, setGoalTeam] = useState<"casa" | "away" | "">("");
  const [selectedSubType, setSelectedSubType] = useState<"tatica" | "lesao" | "desempenho" | "disciplinar" | "">("");
  const [selectedPlayerOut, setSelectedPlayerOut] = useState("");
  const [selectedPlayerIn, setSelectedPlayerIn] = useState("");
  const [cardReason, setCardReason] = useState("");
  const [cardDescription, setCardDescription] = useState("");
  const [subReason, setSubReason] = useState("");

  const navigate = useNavigate();
  // Zustand stores
  const { games: proximasPartidas, fetchGames } = useGamesStore();
  const { matchHistory: historicoPartidas, fetchMatchHistory } = useMatchHistoryStore();
  const { tasks, loading: loadingTasks, error: errorTasks, fetchTasks, updateTask, deleteTask } = useTasksStore();
  const { activeSeason } = useSeasonStore();
  const { clubInfo } = useClubInfoStore();
  const clubId = useCurrentClubId();
  const { user } = useUser();

  // Encontrar a partida selecionada
  const getPartidaSelecionada = useCallback(() => {
    const match = proximasPartidas.find(partida => partida.id === partidaSelecionada);
    console.log("getPartidaSelecionada - partidaSelecionada:", partidaSelecionada);
    console.log("getPartidaSelecionada - match encontrado:", match);
    return match || proximasPartidas[0];
  }, [proximasPartidas, partidaSelecionada]);

  // Função para mapear os tipos de gol do frontend para a API
  const mapGoalType = (type: string): "normal" | "penalty" | "free_kick" | "own_goal" => {
    switch (type) {
      case "penalti":
        return "penalty";
      case "falta":
        return "free_kick";
      case "contra":
        return "own_goal";
      case "normal":
      default:
        return "normal";
    }
  };

  // Função para mapear os tipos de cartão do frontend para a API
  const mapCardType = (type: string): "yellow" | "red" | "second_yellow" => {
    switch (type) {
      case "amarelo":
        return "yellow";
      case "vermelho":
        return "red";
      case "segundo-amarelo":
        return "second_yellow";
      default:
        return "yellow";
    }
  };

  // Funções para manipular eventos do jogo
  const formatTime = (timeInSeconds: number): string => {
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = timeInSeconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const updateTimer = () => {
    setGameState(prev => {
      if (!prev.isLive || prev.isPaused) return prev;

      const now = Date.now();
      const newElapsedTime = prev.elapsedTime + Math.floor((now - prev.startTime) / 1000);

      return {
        ...prev,
        timer: formatTime(newElapsedTime),
        elapsedTime: newElapsedTime,
        startTime: now
      };
    });
  };

  const handleIniciarJogo = () => {
    // Se o jogo já está em andamento, não faz nada
    if (gameState.isLive) return;

    // Limpar qualquer intervalo existente
    if (gameState.timerInterval) {
      clearInterval(gameState.timerInterval);
    }

    // Iniciar o timer
    const interval = setInterval(updateTimer, 1000);

    setGameState(prev => ({
      ...prev,
      isLive: true,
      isPaused: false,
      startTime: Date.now(),
      timerInterval: interval
    }));
  };

  const handlePausarJogo = () => {
    if (!gameState.isLive || gameState.isPaused) return;

    // Atualizar o timer uma última vez antes de pausar
    updateTimer();

    // Limpar o intervalo
    if (gameState.timerInterval) {
      clearInterval(gameState.timerInterval);
    }

    setGameState(prev => ({
      ...prev,
      isPaused: true,
      timerInterval: null
    }));
  };

  const handleRetomarJogo = () => {
    if (!gameState.isLive || !gameState.isPaused) return;

    // Iniciar um novo intervalo
    const interval = setInterval(updateTimer, 1000);

    setGameState(prev => ({
      ...prev,
      isPaused: false,
      startTime: Date.now(),
      timerInterval: interval
    }));
  };

  const handleFinalizarJogo = async () => {
    // Atualizar o timer uma última vez
    updateTimer();

    // Limpar o intervalo
    if (gameState.timerInterval) {
      clearInterval(gameState.timerInterval);
    }

    setGameState(prev => ({
      ...prev,
      isLive: false,
      isPaused: false,
      timerInterval: null
    }));

    // Salvar o resultado do jogo
    const currentMatch = getPartidaSelecionada();
    if (!clubId || !currentMatch?.id) return;

    try {
      // Determinar o resultado (win, loss, draw) com base no placar
      let result: "win" | "loss" | "draw";

      // Se o jogo é em casa
      if (currentMatch.type === "casa") {
        if (gameState.homeScore > gameState.awayScore) {
          result = "win";
        } else if (gameState.homeScore < gameState.awayScore) {
          result = "loss";
        } else {
          result = "draw";
        }
      }
      // Se o jogo é fora
      else {
        if (gameState.homeScore < gameState.awayScore) {
          result = "win";
        } else if (gameState.homeScore > gameState.awayScore) {
          result = "loss";
        } else {
          result = "draw";
        }
      }

      // Atualizar a partida com o resultado final
      const { error } = await supabase
        .from("matches")
        .update({
          result: result, // Usar "win", "loss" ou "draw"
          score_home: gameState.homeScore,
          score_away: gameState.awayScore
        })
        .eq("id", currentMatch.id)
        .eq("club_id", clubId);

      if (error) throw error;

      // Recarregar as partidas
      if (activeSeason) {
        fetchGames(clubId, activeSeason.id);
        fetchMatchHistory(clubId, activeSeason.id);
      }

      // Mostrar mensagem de sucesso
      toast({
        title: "Partida finalizada com sucesso!",
        description: `Resultado final: ${gameState.homeScore} x ${gameState.awayScore}`,
        variant: "default"
      });
    } catch (error) {
      console.error("Erro ao finalizar partida:", error);
      toast({
        title: "Erro ao finalizar partida",
        description: "Não foi possível salvar o resultado da partida.",
        variant: "destructive"
      });
    }
  };

  // Limpar o intervalo quando o componente for desmontado
  useEffect(() => {
    return () => {
      if (gameState.timerInterval) {
        clearInterval(gameState.timerInterval);
      }
    };
  }, []);

  const handleAdicionarNota = () => {
    if (!gameNote.trim()) return;
    // Adicionar nota ao jogo
    handleAddNote();
  };

  // Esta função apenas atualiza o placar na interface
  const handleAddGoal = (isHome: boolean) => {
    setGameState(prev => ({
      ...prev,
      homeScore: isHome ? prev.homeScore + 1 : prev.homeScore,
      awayScore: !isHome ? prev.awayScore + 1 : prev.awayScore
    }));
  };

  // Esta função é chamada pelo botão de registrar gol
  const handleRegisterGoal = () => {
    if (!goalTeam) {
      toast({
        title: "Time não selecionado",
        description: "Selecione o time que marcou o gol",
        variant: "destructive"
      });
      return;
    }

    const isHome = goalTeam === 'casa';
    const goalTypeElements = document.querySelectorAll('input[name="gol-tipo"]:checked') as NodeListOf<HTMLInputElement>;
    // Removido a referência a descriptionElement pois não está sendo usado

    // Verificar se o gol é do nosso time ou do adversário
    // Se o jogo é em casa e o gol é do time da casa, ou se o jogo é fora e o gol é do time visitante, então é do nosso time
    const isOurTeamGoal = (partidaAtual?.type === "casa" && goalTeam === "casa") ||
                          (partidaAtual?.type === "fora" && goalTeam === "away");

    if (isOurTeamGoal) {
      // Se for gol do nosso time, precisamos de um jogador
      const playerElement = document.getElementById('gol-jogador') as HTMLSelectElement;
      const assistElement = document.getElementById('gol-assistencia') as HTMLSelectElement;

      // Definir os valores nos estados
      setSelectedPlayer(playerElement?.value || "");
      setSelectedAssistPlayer(assistElement?.value || "");

      // Chamar a função para adicionar o gol
      if (playerElement?.value) {
        setSelectedGoalType((goalTypeElements[0]?.value as "normal" | "penalti" | "falta" | "contra" | "") || "normal");
        // Removido setGoalDescription pois não está sendo usado
        handleAddGoalEvent(isHome);
      } else {
        toast({
          title: "Jogador não selecionado",
          description: "Selecione um jogador para registrar o gol",
          variant: "destructive"
        });
      }
    } else {
      // Para gol do adversário, não precisamos de jogador
      setSelectedPlayer("adversario"); // Valor especial para indicar que é um gol do adversário
      setSelectedAssistPlayer("");
      setSelectedGoalType((goalTypeElements[0]?.value as "normal" | "penalti" | "falta" | "contra" | "") || "normal");
      // Removido setGoalDescription pois não está sendo usado
      handleAddGoalEvent(isHome);
    }
  };

  // Esta função é chamada pelo botão de registrar cartão
  const handleRegisterCard = () => {
    const playerElement = document.getElementById('cartao-jogador') as HTMLSelectElement;
    const cardTypeElements = document.querySelectorAll('input[name="cartao-tipo"]:checked') as NodeListOf<HTMLInputElement>;
    const reasonElement = document.getElementById('cartao-motivo') as HTMLSelectElement;
    const descriptionElement = document.getElementById('cartao-descricao') as HTMLTextAreaElement;

    // Definir os valores nos estados
    setSelectedPlayer(playerElement?.value || "");
    setSelectedCardType((cardTypeElements[0]?.value as "amarelo" | "vermelho" | "segundo-amarelo" | "") || "");
    setCardReason(reasonElement?.value || "");
    setCardDescription(descriptionElement?.value || "");

    // Chamar a função para adicionar o cartão
    if (playerElement?.value && cardTypeElements.length > 0) {
      handleAddCard();
    }
  };

  // Esta função é chamada pelo botão de registrar substituição
  const handleRegisterSubstitution = () => {
    const playerOutElement = document.getElementById('jogador-sai') as HTMLSelectElement;
    const playerInElement = document.getElementById('jogador-entra') as HTMLSelectElement;
    const typeElement = document.getElementById('sub-tipo') as HTMLSelectElement;
    const reasonElement = document.getElementById('sub-motivo') as HTMLTextAreaElement;

    // Definir os valores nos estados
    setSelectedPlayerOut(playerOutElement?.value || "");
    setSelectedPlayerIn(playerInElement?.value || "");
    setSelectedSubType((typeElement?.value as "tatica" | "lesao" | "desempenho" | "disciplinar" | "") || "");
    setSubReason(reasonElement?.value || "");

    // Chamar a função para adicionar a substituição
    if (playerOutElement?.value && playerInElement?.value) {
      handleAddSubstitution();
    }
  };

  const handleOpenAnaliseModal = () => {
    setAnaliseAdversarioOpen(true);
  };

  const handleAdversarioChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const value = event.target.value;
    setAdversarioSelecionado(value);

    // Carregar dados do adversário selecionado
    const selectedOpponent = opponents.find(opponent => opponent.id === value);

    if (selectedOpponent) {
      // Aqui você pode carregar dados reais de análise do adversário do banco de dados
      // Por enquanto, vamos usar dados de exemplo
      setAnaliseData({
        tecnico: "Técnico do " + selectedOpponent.name,
        formacao: "4-3-3",
        pontosFortes: [
          "Ataque rápido pelos flancos",
          "Posse de bola dominante",
          "Excelente na transição ofensiva"
        ],
        pontosFracos: [
          "Vulnerabilidade em contra-ataques",
          "Defesa alta pode deixar espaços",
          "Dificuldade contra times que jogam fechados"
        ],
        estrategias: [
          "Explorar contra-ataques rápidos",
          "Marcar de forma compacta no meio-campo",
          "Pressionar os laterais para limitar cruzamentos"
        ]
      });
    } else {
      setAnaliseData({
        tecnico: "",
        formacao: "",
        pontosFortes: [""],
        pontosFracos: [""],
        estrategias: [""]
      });
    }
  };

  // Função para criar uma nova partida
  const handleCriarPartida = async () => {
    if (!activeSeason) {
      setErrorMessage("Selecione uma temporada antes de adicionar o jogo.");
      return;
    }
    if (!adversarioSelecionado) {
      setErrorMessage("O adversário é obrigatório.");
      return;
    }
    if (!competicaoSelecionada) {
      setErrorMessage("A competição é obrigatória.");
      return;
    }
    if (!dataPartida) {
      setErrorMessage("A data é obrigatória.");
      return;
    }
    if (!horarioPartida) {
      setErrorMessage("O horário é obrigatório.");
      return;
    }
    if (!localPartida.trim()) {
      setErrorMessage("O local é obrigatório.");
      return;
    }
    if (!tipoJogo) {
      setErrorMessage("O tipo de jogo é obrigatório.");
      return;
    }

    // Encontrar o adversário e a competição selecionados
    const selectedOpponent = opponents.find(o => o.id === adversarioSelecionado);
    const selectedCompetition = competitions.find(c => c.id === competicaoSelecionada);

    if (!selectedOpponent || !selectedCompetition) {
      setErrorMessage("Adversário ou competição inválidos.");
      return;
    }

    setErrorMessage("");
    setIsCreatingMatch(true);
    try {
      await createUpcomingMatch(
        clubId,
        {
          club_id: clubId,
          opponent: selectedOpponent.name,
          opponent_id: adversarioSelecionado,
          competition: selectedCompetition.name,
          competition_id: competicaoSelecionada,
          date: dataPartida,
          time: horarioPartida,
          location: localPartida,
          type: tipoJogo as "casa" | "fora",
          season_id: activeSeason.id,
          ida_volta: idaVolta,
          escalacao: undefined,
          formation: ""
        },
        user?.id
      );

      toast({
        title: "Partida criada com sucesso!",
        description: "A nova partida foi adicionada à lista.",
        variant: "default"
      });

      // Limpar o formulário
      setAdversarioSelecionado("");
      setCompeticaoSelecionada("");
      setDataPartida("");
      setHorarioPartida("");
      setLocalPartida("");
      setTipoJogo("");
      setIdaVolta(false);

      // Recarregar a lista de partidas
      if (clubId && activeSeason) {
        fetchGames(clubId, activeSeason.id);
      }
    } catch (e) {
      console.error("Erro ao criar partida:", e);
      setErrorMessage("Erro ao criar partida.");
      toast({
        title: "Erro ao criar partida",
        description: "Não foi possível adicionar a nova partida.",
        variant: "destructive"
      });
    } finally {
      setIsCreatingMatch(false);
    }
  };

  // Funções para alternar a visibilidade das seções
  const toggleSection = (section: 'pontosFortes' | 'pontosFracos' | 'estrategias') => {
    setSectionsVisible(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);

    // Limpar estados específicos quando mudar de aba
    if (tab === "gol") {
      setGoalTeam("");
    }
  };

  // Função para selecionar uma partida
  const handleSelectPartida = (id: string) => {
    // Encontrar a partida selecionada
    const match = proximasPartidas.find(p => p.id === id);

    // Atualizar o estado
    setPartidaSelecionada(id);
    setActiveTab("detalhes");

    // Carregar eventos da partida
    if (id) {
      fetchMatchEventData();
      fetchPlayerStats(id);

      // Recarregar jogadores com base na categoria da partida
      // Importante: Chamamos fetchPlayerData com um pequeno delay
      // para garantir que getPartidaSelecionada() retorne o valor correto
      setTimeout(() => {
        fetchPlayerData();
      }, 100);
    }
  };

  // Função para carregar estatísticas dos jogadores para a partida selecionada
  const fetchPlayerStats = async (matchId: string) => {
    if (!clubId) return;

    try {
      const stats = await getPlayerMatchStatistics(clubId, matchId);

      // Converter para um objeto com player_id como chave
      const statsMap: Record<string, any> = {};
      stats.forEach(stat => {
        statsMap[stat.player_id] = stat;
      });

      setPlayerStats(statsMap);
    } catch (error) {
      console.error("Erro ao carregar estatísticas dos jogadores:", error);
    }
  };

  // Nota: Usamos getPartidaSelecionada() diretamente

  // Função para carregar jogadores
  const fetchPlayerData = useCallback(async () => {
    if (!clubId) return;

    try {
      setLoadingPlayers(true);

      // Verificar se a partida atual tem uma categoria associada
      const currentMatch = getPartidaSelecionada();

      if (currentMatch?.category_id) {
        // Se a partida tem categoria, buscar apenas jogadores dessa categoria
        try {
          const data = await getCategoryPlayers(clubId, currentMatch.category_id, { includeInactive: false });

          if (data && data.length > 0) {
            setPlayers(data);
          } else {
            // Fallback para todos os jogadores se não há jogadores na categoria
            const allPlayers = await getPlayers(clubId, undefined, { includeInactive: false });
            setPlayers(allPlayers);
          }
        } catch (categoryError) {
          console.error("Erro ao buscar jogadores da categoria:", categoryError);
          // Fallback para todos os jogadores em caso de erro
          const allPlayers = await getPlayers(clubId, undefined, { includeInactive: false });
          setPlayers(allPlayers);
        }
      } else {
        // Se não tem categoria, buscar todos os jogadores ativos
        const data = await getPlayers(clubId, undefined, { includeInactive: false });
        setPlayers(data);
      }
    } catch (error) {
      console.error("Erro ao carregar jogadores:", error);
    } finally {
      setLoadingPlayers(false);
    }
  }, [clubId, getPartidaSelecionada, partidaSelecionada]);

  // Função para carregar adversários
  const fetchOpponentsData = useCallback(async () => {
    if (!clubId) return;

    try {
      setLoadingOpponents(true);
      const data = await getOpponents(clubId);
      setOpponents(data);
    } catch (error) {
      console.error("Erro ao carregar adversários:", error);
    } finally {
      setLoadingOpponents(false);
    }
  }, [clubId]);

  // Função para carregar competições
  const fetchCompetitionsData = useCallback(async () => {
    if (!clubId || !activeSeason?.id) return;

    try {
      setLoadingCompetitions(true);
      const data = await getCompetitions(clubId, activeSeason.id);
      setCompetitions(data);
    } catch (error) {
      console.error("Erro ao carregar competições:", error);
    } finally {
      setLoadingCompetitions(false);
    }
  }, [clubId, activeSeason]);

  // Função para carregar eventos da partida
  const fetchMatchEventData = useCallback(async () => {
    const currentMatch = getPartidaSelecionada();
    if (!clubId || !currentMatch?.id) return;

    try {
      setLoadingEvents(true);
      const data = await getMatchEvents(clubId, currentMatch.id);
      setMatchEvents(data);
    } catch (error) {
      console.error("Erro ao carregar eventos da partida:", error);
    } finally {
      setLoadingEvents(false);
    }
  }, [clubId, getPartidaSelecionada]);

  // Função para adicionar uma nota
  const handleAddNote = async () => {
    const currentMatch = getPartidaSelecionada();
    if (!clubId || !currentMatch?.id || !gameNote.trim()) return;

    try {
      await createNoteEvent(clubId, currentMatch.id, {
        minute: gameState.timer,
        note: gameNote
      });

      // Recarregar eventos
      fetchMatchEventData();
      setGameNote("");
    } catch (error) {
      console.error("Erro ao adicionar nota:", error);
    }
  };

  // Função para adicionar um cartão
  const handleAddCard = async () => {
    const currentMatch = getPartidaSelecionada();
    if (!clubId || !currentMatch?.id || !selectedPlayer || !selectedCardType) return;

    try {
      await createCardEvent(clubId, currentMatch.id, {
        player_id: selectedPlayer,
        minute: gameState.timer,
        card_type: mapCardType(selectedCardType),
        reason: cardReason || cardDescription
      });

      // Recarregar eventos
      fetchMatchEventData();
      setSelectedPlayer("");
      setSelectedCardType("");
      setCardReason("");
      setCardDescription("");
    } catch (error) {
      console.error("Erro ao adicionar cartão:", error);
    }
  };

  // Função para adicionar uma substituição
  const handleAddSubstitution = async () => {
    const currentMatch = getPartidaSelecionada();
    if (!clubId || !currentMatch?.id || !selectedPlayerOut || !selectedPlayerIn) return;

    try {
      await createSubstitutionEvent(clubId, currentMatch.id, {
        player_out_id: selectedPlayerOut,
        player_in_id: selectedPlayerIn,
        minute: gameState.timer,
        reason: subReason
      });

      // Recarregar eventos
      fetchMatchEventData();
      setSelectedPlayerOut("");
      setSelectedPlayerIn("");
      setSelectedSubType("");
      setSubReason("");
    } catch (error) {
      console.error("Erro ao adicionar substituição:", error);
    }
  };

  // Função para adicionar um gol
  const handleAddGoalEvent = async (isHome: boolean) => {
    const currentMatch = getPartidaSelecionada();
    if (!clubId || !currentMatch?.id) return;

    // Verificar se o gol é do nosso time ou do adversário
    // Se o jogo é em casa e o gol é do time da casa, ou se o jogo é fora e o gol é do time visitante, então é do nosso time
    const isOurTeamGoal = (currentMatch.type === "casa" && isHome) ||
                          (currentMatch.type === "fora" && !isHome);

    // Para gols do adversário, usamos um valor especial "adversario"
    if (!isOurTeamGoal && selectedPlayer !== "adversario") {
      setSelectedPlayer("adversario");
    }

    if (!selectedPlayer) return;

    try {
      // Se for gol do adversário, não precisamos de um player_id
      // Vamos modificar a API para aceitar null para player_id em gols do adversário
      if (selectedPlayer === "adversario") {
        // Atualizar diretamente a tabela match_events
        const { error } = await supabase
          .from("match_events")
          .insert({
            match_id: currentMatch.id,
            club_id: clubId,
            event_type: "goal",
            minute: gameState.timer,
            player_id: null, // Usar null para jogador adversário
            event_data: {
              type: mapGoalType(selectedGoalType),
              team: isHome ? "home" : "away"
            }
          });

        if (error) throw error;

        // Calcular o novo placar
        const newHomeScore = isHome ? gameState.homeScore + 1 : gameState.homeScore;
        const newAwayScore = !isHome ? gameState.awayScore + 1 : gameState.awayScore;

        // Determinar o resultado (win, loss, draw) com base no placar
        let result: "win" | "loss" | "draw";

        // Se o jogo é em casa
        if (currentMatch.type === "casa") {
          if (newHomeScore > newAwayScore) {
            result = "win";
          } else if (newHomeScore < newAwayScore) {
            result = "loss";
          } else {
            result = "draw";
          }
        }
        // Se o jogo é fora
        else {
          if (newHomeScore < newAwayScore) {
            result = "win";
          } else if (newHomeScore > newAwayScore) {
            result = "loss";
          } else {
            result = "draw";
          }
        }

        // Atualizar o placar e o resultado na tabela matches
        await supabase
          .from("matches")
          .update({
            score_home: newHomeScore,
            score_away: newAwayScore,
            result: result // Usar "win", "loss" ou "draw"
          })
          .eq("id", currentMatch.id)
          .eq("club_id", clubId);
      } else {
        // Para gols do nosso time, usamos a função normal
        await createGoalEvent(clubId, currentMatch.id, {
          player_id: selectedPlayer,
          minute: gameState.timer,
          assist_player_id: selectedAssistPlayer || undefined,
          type: mapGoalType(selectedGoalType),
          team: isHome ? "home" : "away"
        });

        // Calcular o novo placar
        const newHomeScore = isHome ? gameState.homeScore + 1 : gameState.homeScore;
        const newAwayScore = !isHome ? gameState.awayScore + 1 : gameState.awayScore;

        // Determinar o resultado (win, loss, draw) com base no placar
        let result: "win" | "loss" | "draw";

        // Se o jogo é em casa
        if (currentMatch.type === "casa") {
          if (newHomeScore > newAwayScore) {
            result = "win";
          } else if (newHomeScore < newAwayScore) {
            result = "loss";
          } else {
            result = "draw";
          }
        }
        // Se o jogo é fora
        else {
          if (newHomeScore < newAwayScore) {
            result = "win";
          } else if (newHomeScore > newAwayScore) {
            result = "loss";
          } else {
            result = "draw";
          }
        }

        // Atualizar o placar e o resultado na tabela matches
        await supabase
          .from("matches")
          .update({
            score_home: newHomeScore,
            score_away: newAwayScore,
            result: result // Usar "win", "loss" ou "draw"
          })
          .eq("id", currentMatch.id)
          .eq("club_id", clubId);
      }

      // Atualizar o placar na interface
      handleAddGoal(isHome);

      // Recarregar eventos
      fetchMatchEventData();
      setSelectedPlayer("");
      setSelectedAssistPlayer("");
      setSelectedGoalType("");

      toast({
        title: "Gol registrado com sucesso!",
        description: `Gol do ${isHome ? getTeamName(currentMatch, clubInfo, true) : getTeamName(currentMatch, clubInfo, false)}`,
        variant: "default"
      });
    } catch (error) {
      console.error("Erro ao adicionar gol:", error);
      toast({
        title: "Erro ao registrar gol",
        description: "Não foi possível registrar o gol",
        variant: "destructive"
      });
    }
  };

  // Função para carregar informações do clube
  const fetchClubInfo = useCallback(async () => {
    if (!clubId) return;

    try {
      await useClubInfoStore.getState().fetchClubInfo(clubId);
    } catch (error) {
      console.error("Erro ao carregar informações do clube:", error);
    }
  }, [clubId]);

  useEffect(() => {
    if (clubId && activeSeason) {
      // Migrar resultados antigos para o novo formato
      migrateMatchResults(clubId).then(() => {
        // Após a migração, buscar os dados atualizados
        fetchGames(clubId, activeSeason.id);
        fetchMatchHistory(clubId, activeSeason.id);
      });

      fetchTasks(clubId);
      fetchPlayerData();
      fetchOpponentsData();
      fetchCompetitionsData();
      fetchClubInfo();
    }
  }, [clubId, activeSeason?.id]);

  // Carregar eventos e estatísticas quando a partida selecionada mudar
  useEffect(() => {
    if (partidaSelecionada) {
      fetchMatchEventData();
      fetchPlayerStats(partidaSelecionada);

      // Importante: Chamamos fetchPlayerData com um pequeno delay
      // para garantir que getPartidaSelecionada() retorne o valor correto
      setTimeout(() => {
        fetchPlayerData();
      }, 100);
    }
  }, [partidaSelecionada]);

  const proximaPartida = proximasPartidas[0];
  const partidaAtual = getPartidaSelecionada();
  const historicoVsAdversario = calcularHistoricoContraAdversario(historicoPartidas, proximaPartida?.opponent, clubId);
  const estatisticasRecentes = calcularEstatisticasRecentes(historicoPartidas, 5, clubId);

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Gestão de Partidas</h1>
        <p className="text-muted-foreground">
          Acompanhe jogos, gerencie análises táticas e desempenho
        </p>
      </div>

      {/* Próxima partida */}
      <Card className="border-none" style={{ background: `linear-gradient(to right, var(--color-primary), var(--color-secondary))` }}>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-white">
            <div className="md:col-span-2">
              <h3 className="text-lg font-medium mb-1">Próxima Partida</h3>
              <h2 className="text-2xl font-bold mb-2 flex items-center gap-2">
                {proximaPartida?.type === "casa"
                  ? clubInfo?.name
                  : proximaPartida?.opponent} vs {proximaPartida?.type === "casa"
                  ? proximaPartida?.opponent
                  : clubInfo?.name}
              </h2>
              <div className="flex flex-wrap gap-4 mb-4">
                <Badge variant="outline" className="text-white border-white/40 bg-white/10">
                  {proximaPartida?.competition}
                </Badge>
                <div className="flex items-center gap-1">
                  <CalendarIcon className="h-4 w-4" />
                  <span>{proximaPartida?.date}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  <span>{proximaPartida?.time}</span>
                </div>
                <div className="flex items-center gap-1">
                  <MapPin className="h-4 w-4" />
                  <span>{proximaPartida?.location}</span>
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  className="bg-white hover:bg-white/90"
                  style={{ color: 'var(--color-primary)' }}
                >
                  Análise do Adversário
                </Button>
                <Button
                  variant="outline"
                  className="text-black border-white/40 hover:bg-white/20 hover:border-white/60"
                  onClick={() => navigate('/escalacao')}
                >
                  Planejar Táticas
                </Button>
              </div>
            </div>
            <div className="flex items-center justify-center">
              <div className="bg-white/10 p-6 rounded-lg text-center">
                <h3 className="text-lg font-medium mb-3">Histórico vs {proximaPartida?.opponent}</h3>
                <div className="flex gap-2 justify-center">
                  <div className="bg-emerald-500 w-8 h-8 rounded-full flex items-center justify-center text-white">{historicoVsAdversario.vitorias}</div>
                  <div className="bg-gray-400 w-8 h-8 rounded-full flex items-center justify-center text-white">{historicoVsAdversario.empates}</div>
                  <div className="bg-red-500 w-8 h-8 rounded-full flex items-center justify-center text-white">{historicoVsAdversario.derrotas}</div>
                </div>
                <div className="flex justify-between text-xs mt-2">
                  <span>Vitórias</span>
                  <span>Empates</span>
                  <span>Derrotas</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Estatísticas e planejamento */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Últimos Resultados</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-1">
              {historicoPartidas.slice(0, 5).map((partida) => {
                const resultado = getResultadoStatus(partida, clubId);
                return (
                  <div
                    key={partida.id}
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-white ${resultado.cor}`}
                  >
                    {resultado.letra}
                  </div>
                );
              })}
            </div>
            <div className="mt-4">
              <div className="flex justify-between items-center text-sm">
                <span className="text-muted-foreground">Aproveitamento:</span>
                <span className="font-medium">{estatisticasRecentes.aproveitamento}%</span>
              </div>
              <div className="flex justify-between items-center text-sm">
                <span className="text-muted-foreground">Gols marcados:</span>
                <span className="font-medium">{estatisticasRecentes.golsMarcados}</span>
              </div>
              <div className="flex justify-between items-center text-sm">
                <span className="text-muted-foreground">Gols sofridos:</span>
                <span className="font-medium">{estatisticasRecentes.golsSofridos}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Próximos Confrontos</CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div>
              {proximasPartidas.slice(0, 3).map((partida, idx) => (
                <div key={partida.id} className={`p-3 flex justify-between items-center ${idx !== proximasPartidas.slice(0, 3).length - 1 ? 'border-b' : ''}`}>
                  <div>
                    <p className="font-medium">{partida.opponent}</p>
                    <p className="text-xs text-muted-foreground">{partida.competition}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm">{partida.date}</p>
                    <p className="text-xs text-muted-foreground">{partida.location}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center gap-2">
              <ActivityIcon className="w-5 h-5 text-amber-500" /> Tarefas do Jogo
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loadingTasks ? (
              <div className="flex items-center gap-2 text-muted-foreground"><Loader2 className="animate-spin w-4 h-4" /> Carregando tarefas...</div>
            ) : tasks.length === 0 ? (
              <div className="text-muted-foreground">Nenhuma tarefa cadastrada.</div>
            ) : (
              <ul className="space-y-3">
                {tasks.map(task => (
                  <li key={task.id} className="flex items-center justify-between bg-gray-50 rounded px-3 py-2">
                    <div className="flex items-center gap-2">
                      <Button
                        size="icon"
                        variant={task.completed ? "outline" : "outline"}
                        className={`h-7 w-7 ${task.completed ? 'bg-emerald-100 border-emerald-400 text-emerald-600' : ''}`}
                        title={task.completed ? "Concluída" : "Marcar como concluída"}
                        onClick={() => updateTask(clubId, task.id, {
                          status: !task.completed ? 'concluída' : 'pendente',
                          completed: !task.completed,
                        })}
                      >
                        {task.completed ? <Check className="w-4 h-4" /> : <ActivityIcon className="w-4 h-4" />}
                      </Button>
                      <div>
                        <div className={`font-medium ${task.completed ? "line-through text-gray-400" : ""}`}>{task.title}</div>
                        {task.description && <div className="text-xs text-muted-foreground">{task.description}</div>}
                      </div>
                    </div>
                    <Button size="icon" variant="ghost" className="h-7 w-7 text-rose-500 hover:bg-rose-100" title="Excluir tarefa" onClick={() => deleteTask(clubId, task.id)}>
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </li>
                ))}
              </ul>
            )}
            {errorTasks && <div className="text-red-500 text-sm mt-2">{errorTasks}</div>}
            <Button className="w-full mt-4" onClick={() => setNovaTarefaDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Nova Tarefa
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Gestão de Partidas */}
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-bold">Análise pré-jogo, monitoramento em tempo real e relatórios pós-jogo</h2>
        <Button onClick={() => setAdicionarJogoDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Adicionar Jogo
        </Button>
      </div>

      {/* Seletor de partida */}
      <Card className="mb-6">
        <CardContent className="py-4">
          <div className="flex flex-col gap-2">
            <label htmlFor="match-selector" className="text-sm font-medium">Selecione uma partida</label>
            <select
              id="match-selector"
              className="w-full p-2 border rounded-md"
              value={partidaSelecionada}
              onChange={(e) => handleSelectPartida(e.target.value)}
            >
              <option value="">Selecione uma partida</option>
              {proximasPartidas.map((partida) => (
                <option key={partida.id} value={partida.id}>
                  {partida.type === "casa" ? clubInfo?.name : partida.opponent} vs {partida.type === "casa" ? partida.opponent : clubInfo?.name} - {partida.date}
                </option>
              ))}
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Tabs principais */}
      <Tabs defaultValue="pre-jogo">
        <TabsList className="w-full mb-6">
          <TabsTrigger value="pre-jogo" className="flex-1">Pré-Jogo</TabsTrigger>
          <TabsTrigger value="durante-jogo" className="flex-1">Durante o Jogo</TabsTrigger>
          <TabsTrigger value="pos-jogo" className="flex-1">Pós-Jogo</TabsTrigger>
        </TabsList>

        {/* Tab Pré-Jogo */}
        <TabsContent value="pre-jogo">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Formulário de Próxima Partida */}
            <Card>
              <CardContent className="p-6">
                <div>
                  <h3 className="text-xl font-bold mb-4">Criar Nova Partida</h3>
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="adversario" className="block text-sm font-medium mb-1">Adversário*</label>
                      <select
                        id="adversario"
                        className="w-full p-2 border rounded-md"
                        value={adversarioSelecionado}
                        onChange={handleAdversarioChange}
                      >
                        <option value="">Selecione o adversário</option>
                        {loadingOpponents ? (
                          <option value="" disabled>Carregando adversários...</option>
                        ) : opponents.length === 0 ? (
                          <option value="" disabled>Nenhum adversário cadastrado</option>
                        ) : (
                          opponents.map(opponent => (
                            <option key={opponent.id} value={opponent.id}>
                              {opponent.name}
                            </option>
                          ))
                        )}
                      </select>
                      <div className="flex justify-end mt-1">
                        <Button
                          type="button"
                          variant="link"
                          className="text-xs p-0 h-auto"
                          onClick={() => window.open('/adversarios', '_blank')}
                        >
                          Gerenciar adversários
                        </Button>
                      </div>
                    </div>

                    <div>
                      <label htmlFor="competicao" className="block text-sm font-medium mb-1">Competição*</label>
                      <select
                        id="competicao"
                        className="w-full p-2 border rounded-md"
                        value={competicaoSelecionada}
                        onChange={(e) => setCompeticaoSelecionada(e.target.value)}
                      >
                        <option value="">Selecione a competição</option>
                        {loadingCompetitions ? (
                          <option value="" disabled>Carregando competições...</option>
                        ) : competitions.length === 0 ? (
                          <option value="" disabled>Nenhuma competição cadastrada</option>
                        ) : (
                          competitions.map(competition => (
                            <option key={competition.id} value={competition.id}>
                              {competition.name}
                            </option>
                          ))
                        )}
                      </select>
                      <div className="flex justify-end mt-1">
                        <Button
                          type="button"
                          variant="link"
                          className="text-xs p-0 h-auto"
                          onClick={() => window.open('/competicoes', '_blank')}
                        >
                          Gerenciar competições
                        </Button>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="data-partida" className="block text-sm font-medium mb-1">Data da Partida*</label>
                        <input
                          type="date"
                          id="data-partida"
                          className="w-full p-2 border rounded-md"
                          value={dataPartida}
                          onChange={(e) => setDataPartida(e.target.value)}
                        />
                      </div>

                      <div>
                        <label htmlFor="horario-partida" className="block text-sm font-medium mb-1">Horário*</label>
                        <input
                          type="time"
                          id="horario-partida"
                          className="w-full p-2 border rounded-md"
                          value={horarioPartida}
                          onChange={(e) => setHorarioPartida(e.target.value)}
                        />
                      </div>
                    </div>

                    <div>
                      <label htmlFor="estadio" className="block text-sm font-medium mb-1">Local*</label>
                      <input
                        type="text"
                        id="estadio"
                        className="w-full p-2 border rounded-md"
                        placeholder="Estádio, cidade"
                        value={localPartida}
                        onChange={(e) => setLocalPartida(e.target.value)}
                      />
                    </div>

                    <div>
                      <label htmlFor="tipo-jogo" className="block text-sm font-medium mb-1">Tipo de jogo*</label>
                      <div className="flex gap-4">
                        <label className="flex items-center gap-2 p-3 border rounded-md flex-1 cursor-pointer hover:bg-primary/10">
                          <input
                            type="radio"
                            name="tipo-jogo"
                            value="casa"
                            checked={tipoJogo === "casa"}
                            onChange={() => setTipoJogo("casa")}
                            className="h-4 w-4"
                          />
                          <span>Casa</span>
                        </label>

                        <label className="flex items-center gap-2 p-3 border rounded-md flex-1 cursor-pointer hover:bg-primary/10">
                          <input
                            type="radio"
                            name="tipo-jogo"
                            value="fora"
                            checked={tipoJogo === "fora"}
                            onChange={() => setTipoJogo("fora")}
                            className="h-4 w-4"
                          />
                          <span>Fora</span>
                        </label>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="idaVolta"
                        checked={idaVolta}
                        onChange={(e) => setIdaVolta(e.target.checked)}
                        className="h-4 w-4"
                      />
                      <label htmlFor="idaVolta">Partida de ida/volta?</label>
                    </div>

                    {errorMessage && <div className="text-red-500 text-sm mt-1">{errorMessage}</div>}

                    <Button
                      className="w-full mt-2"
                      onClick={handleCriarPartida}
                      disabled={isCreatingMatch}
                    >
                      {isCreatingMatch && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Criar Partida
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Análise do Adversário */}
            <Card>
              <CardContent className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-xl font-bold">Análise do Adversário: {adversarioSelecionado ? opponents.find(o => o.id === adversarioSelecionado)?.name || "Carregando..." : "Selecione um adversário"}</h3>
                  <Button variant="outline" size="sm" onClick={handleOpenAnaliseModal}>
                    <Plus className="h-4 w-4 mr-1" /> Editar Análise
                  </Button>
                </div>

                <div className="space-y-4">
                  <div className="flex justify-between">
                    <div>
                      <span className="text-sm text-gray-500">Técnico:</span>
                    </div>
                    <div>
                      <span className="font-medium">{analiseData.tecnico || "Não informado"}</span>
                    </div>
                  </div>

                  <div className="flex justify-between">
                    <div>
                      <span className="text-sm text-gray-500">Formação típica:</span>
                    </div>
                    <div>
                      <span className="font-medium">{analiseData.formacao || "Não informada"}</span>
                    </div>
                  </div>

                  <div className="border rounded-md overflow-hidden">
                    <div className="bg-gray-50 p-3 flex items-center">
                      <div className="h-5 w-5 rounded-full bg-green-500 flex items-center justify-center text-white mr-2">
                        <Check className="h-3 w-3" />
                      </div>
                      <span className="font-medium">Pontos fortes</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ml-auto"
                        onClick={() => toggleSection('pontosFortes')}
                      >
                        {sectionsVisible.pontosFortes ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    {sectionsVisible.pontosFortes && (
                      <div className="p-3">
                        <ul className="space-y-2 pl-6 list-disc">
                          {analiseData.pontosFortes.length > 0 && analiseData.pontosFortes[0] !== "" ? (
                            analiseData.pontosFortes.map((ponto, index) => (
                              <li key={`forte-view-${index}`}>{ponto}</li>
                            ))
                          ) : (
                            <li>Nenhum ponto forte registrado</li>
                          )}
                        </ul>
                      </div>
                    )}
                  </div>

                  <div className="border rounded-md overflow-hidden">
                    <div className="bg-gray-50 p-3 flex items-center">
                      <div className="h-5 w-5 rounded-full bg-amber-500 flex items-center justify-center text-white mr-2">
                        <AlertTriangle className="h-3 w-3" />
                      </div>
                      <span className="font-medium">Pontos fracos</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ml-auto"
                        onClick={() => toggleSection('pontosFracos')}
                      >
                        {sectionsVisible.pontosFracos ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    {sectionsVisible.pontosFracos && (
                      <div className="p-3">
                        <ul className="space-y-2 pl-6 list-disc">
                          {analiseData.pontosFracos.length > 0 && analiseData.pontosFracos[0] !== "" ? (
                            analiseData.pontosFracos.map((ponto, index) => (
                              <li key={`fraco-view-${index}`}>{ponto}</li>
                            ))
                          ) : (
                            <li>Nenhum ponto fraco registrado</li>
                          )}
                        </ul>
                      </div>
                    )}
                  </div>

                  <div className="border rounded-md overflow-hidden">
                    <div className="bg-gray-50 p-3 flex items-center">
                      <div className="h-5 w-5 rounded-full bg-primary flex items-center justify-center text-white mr-2">
                        <Lightbulb className="h-3 w-3" />
                      </div>
                      <span className="font-medium">Estratégias recomendadas</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ml-auto"
                        onClick={() => toggleSection('estrategias')}
                      >
                        {sectionsVisible.estrategias ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    {sectionsVisible.estrategias && (
                      <div className="p-3">
                        <ul className="space-y-2 pl-6 list-disc">
                          {analiseData.estrategias.length > 0 && analiseData.estrategias[0] !== "" ? (
                            analiseData.estrategias.map((estrategia, index) => (
                              <li key={`estrategia-view-${index}`}>{estrategia}</li>
                            ))
                          ) : (
                            <li>Nenhuma estratégia registrada</li>
                          )}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Tab Durante o Jogo */}
        <TabsContent value="durante-jogo">
          <Card>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="md:col-span-2">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-xl font-bold">Partida ao Vivo</h3>
                    <div className="flex items-center gap-2">
                      <div className="bg-gray-100 text-gray-800 px-2 py-1 rounded-md text-sm font-medium">
                        {partidaAtual?.competition || "Amistoso"}
                      </div>
                      <div className="bg-gray-100 text-gray-800 px-2 py-1 rounded-md text-sm font-medium">
                        {partidaAtual?.location || "Estádio"}
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-gray-900 to-gray-800 rounded-lg p-6 mb-6 text-white shadow-lg">
                    <div className="flex justify-between items-center mb-4">
                      <div className="flex flex-col items-center">
                        <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mb-2">
                          <span className="text-gray-800 font-bold text-lg">{clubInfo?.name?.substring(0, 3) || "HOM"}</span>
                        </div>
                        <div className="text-lg font-bold">{getTeamName(partidaAtual, clubInfo, true)}</div>
                      </div>

                      <div className="flex flex-col items-center">
                        <div className="flex items-center gap-4 mb-2">
                          <div className="text-4xl font-bold">{gameState.homeScore}</div>
                          <div className="text-2xl">-</div>
                          <div className="text-4xl font-bold">{gameState.awayScore}</div>
                        </div>
                        <div className="bg-white text-gray-800 px-4 py-1 rounded-full font-mono font-bold text-xl">
                          {gameState.timer}
                        </div>
                      </div>

                      <div className="flex flex-col items-center">
                        <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mb-2">
                          <span className="text-gray-800 font-bold text-lg">{partidaAtual?.opponent?.substring(0, 3) || "VIS"}</span>
                        </div>
                        <div className="text-lg font-bold">{getTeamName(partidaAtual, clubInfo, false)}</div>
                      </div>
                    </div>

                    <div className="flex justify-center gap-3 mt-4">
                      {!gameState.isLive ? (
                        <Button
                          className="bg-green-600 hover:bg-green-700 text-white"
                          onClick={handleIniciarJogo}
                        >
                          <Play className="h-4 w-4 mr-2" /> Iniciar Partida
                        </Button>
                      ) : gameState.isPaused ? (
                        <Button
                          className="bg-green-600 hover:bg-green-700 text-white"
                          onClick={handleRetomarJogo}
                        >
                          <Play className="h-4 w-4 mr-2" /> Retomar
                        </Button>
                      ) : (
                        <Button
                          className="bg-amber-600 hover:bg-amber-700 text-white"
                          onClick={handlePausarJogo}
                        >
                          <Pause className="h-4 w-4 mr-2" /> Pausar
                        </Button>
                      )}

                      {gameState.isLive && (
                        <Button
                          className="bg-red-600 hover:bg-red-700 text-white"
                          onClick={handleFinalizarJogo}
                        >
                          <StopCircle className="h-4 w-4 mr-2" /> Finalizar
                        </Button>
                      )}
                    </div>
                  </div>

                  <div className="mb-6">
                    <h3 className="text-xl font-bold mb-4">Registrar Eventos</h3>
                    <div className="bg-white rounded-lg shadow-md overflow-hidden border">
                      <div className="flex border-b">
                        <button
                          className={`flex-1 py-3 px-4 flex items-center justify-center gap-2 font-medium transition-colors ${activeTab === "nota" ? "bg-blue-50 text-blue-700 border-b-2 border-blue-500" : "text-gray-600 hover:bg-gray-50"}`}
                          onClick={() => handleTabChange("nota")}
                        >
                          <FileText className="h-4 w-4" /> Nota
                        </button>
                        <button
                          className={`flex-1 py-3 px-4 flex items-center justify-center gap-2 font-medium transition-colors ${activeTab === "substituicao" ? "bg-blue-50 text-blue-700 border-b-2 border-blue-500" : "text-gray-600 hover:bg-gray-50"}`}
                          onClick={() => handleTabChange("substituicao")}
                        >
                          <RefreshCw className="h-4 w-4" /> Substituição
                        </button>
                        <button
                          className={`flex-1 py-3 px-4 flex items-center justify-center gap-2 font-medium transition-colors ${activeTab === "cartao" ? "bg-blue-50 text-blue-700 border-b-2 border-blue-500" : "text-gray-600 hover:bg-gray-50"}`}
                          onClick={() => handleTabChange("cartao")}
                        >
                          <Square className="h-4 w-4 text-yellow-500" /> Cartão
                        </button>
                        <button
                          className={`flex-1 py-3 px-4 flex items-center justify-center gap-2 font-medium transition-colors ${activeTab === "gol" ? "bg-blue-50 text-blue-700 border-b-2 border-blue-500" : "text-gray-600 hover:bg-gray-50"}`}
                          onClick={() => handleTabChange("gol")}
                        >
                          <Target className="h-4 w-4" /> Gol
                        </button>
                      </div>
                    </div>
                  </div>

                  {activeTab === "nota" && (
                    <div className="bg-white rounded-lg shadow-md p-6 border">
                      <div className="mb-4">
                        <label htmlFor="nota-minuto" className="block text-sm font-medium mb-1">Minuto</label>
                        <input
                          type="text"
                          id="nota-minuto"
                          className="w-full p-2 border rounded-md"
                          value={gameState.timer}
                          readOnly
                        />
                      </div>

                      <div className="mb-4">
                        <label htmlFor="nota-texto" className="block text-sm font-medium mb-1">Observação</label>
                        <textarea
                          id="nota-texto"
                          className="w-full p-3 border rounded-md"
                          rows={4}
                          placeholder="Adicione uma nota sobre o jogo..."
                          value={gameNote}
                          onChange={(e) => setGameNote(e.target.value)}
                        ></textarea>
                      </div>

                      <div className="flex justify-end">
                        <Button
                          className="bg-blue-600 hover:bg-blue-700 text-white"
                          onClick={handleAdicionarNota}
                        >
                          <FileText className="h-4 w-4 mr-2" /> Registrar Nota
                        </Button>
                      </div>
                    </div>
                  )}

                  {activeTab === "substituicao" && (
                    <div className="bg-white rounded-lg shadow-md p-6 border">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <label htmlFor="sub-minuto" className="block text-sm font-medium mb-1">Minuto</label>
                          <input
                            type="text"
                            id="sub-minuto"
                            className="w-full p-2 border rounded-md"
                            value={gameState.timer}
                            readOnly
                          />
                        </div>

                        <div>
                          <label htmlFor="sub-tipo" className="block text-sm font-medium mb-1">Tipo</label>
                          <select id="sub-tipo" className="w-full p-2 border rounded-md">
                            <option value="tatica">Tática</option>
                            <option value="lesao">Lesão</option>
                            <option value="desempenho">Desempenho</option>
                            <option value="disciplinar">Disciplinar</option>
                          </select>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <label htmlFor="jogador-sai" className="block text-sm font-medium mb-1">Jogador que sai</label>
                          <select id="jogador-sai" className="w-full p-2 border rounded-md">
                            <option value="">Selecione o jogador</option>
                            {loadingPlayers ? (
                              <option value="" disabled>Carregando jogadores...</option>
                            ) : !players || players.length === 0 ? (
                              <option value="" disabled>Nenhum jogador encontrado</option>
                            ) : (
                              players
                                .filter(player => player.status !== "inativo")
                                .map(player => (
                                  <option key={player.id} value={player.id}>
                                    {player.name} ({player.position})
                                  </option>
                                ))
                            )}
                          </select>
                        </div>

                        <div>
                          <label htmlFor="jogador-entra" className="block text-sm font-medium mb-1">Jogador que entra</label>
                          <select id="jogador-entra" className="w-full p-2 border rounded-md">
                            <option value="">Selecione o jogador</option>
                            {loadingPlayers ? (
                              <option value="" disabled>Carregando jogadores...</option>
                            ) : !players || players.length === 0 ? (
                              <option value="" disabled>Nenhum jogador encontrado</option>
                            ) : (
                              players
                                .filter(player => player.status !== "inativo")
                                .map(player => (
                                  <option key={player.id} value={player.id}>
                                    {player.name} ({player.position})
                                  </option>
                                ))
                            )}
                          </select>
                        </div>
                      </div>

                      <div className="mb-4">
                        <label htmlFor="sub-motivo" className="block text-sm font-medium mb-1">Motivo (opcional)</label>
                        <textarea
                          id="sub-motivo"
                          className="w-full p-3 border rounded-md"
                          rows={3}
                          placeholder="Motivo da substituição..."
                        ></textarea>
                      </div>

                      <div className="flex justify-end">
                        <Button
                          className="bg-blue-600 hover:bg-blue-700 text-white"
                          onClick={handleRegisterSubstitution}
                        >
                          <RefreshCw className="h-4 w-4 mr-2" /> Registrar Substituição
                        </Button>
                      </div>
                    </div>
                  )}

                  {activeTab === "cartao" && (
                    <div className="bg-white rounded-lg shadow-md p-6 border">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <label htmlFor="cartao-minuto" className="block text-sm font-medium mb-1">Minuto</label>
                          <input
                            type="text"
                            id="cartao-minuto"
                            className="w-full p-2 border rounded-md"
                            value={gameState.timer}
                            readOnly
                          />
                        </div>

                        <div>
                          <label htmlFor="cartao-jogador" className="block text-sm font-medium mb-1">Jogador</label>
                          <select id="cartao-jogador" className="w-full p-2 border rounded-md">
                            <option value="">Selecione o jogador</option>
                            {loadingPlayers ? (
                              <option value="" disabled>Carregando jogadores...</option>
                            ) : !players || players.length === 0 ? (
                              <option value="" disabled>Nenhum jogador encontrado</option>
                            ) : (
                              players
                                .filter(player => player.status !== "inativo")
                                .map(player => (
                                  <option key={player.id} value={player.id}>
                                    {player.name} ({player.position})
                                  </option>
                                ))
                            )}
                          </select>
                        </div>
                      </div>

                      <div className="mb-4">
                        <label htmlFor="cartao-tipo" className="block text-sm font-medium mb-1">Tipo de Cartão</label>
                        <div className="flex gap-4">
                          <label className="flex items-center gap-2 p-3 border rounded-md flex-1 cursor-pointer hover:bg-yellow-50">
                            <input type="radio" name="cartao-tipo" value="amarelo" className="h-4 w-4" />
                            <div className="h-6 w-4 bg-yellow-500 rounded-sm"></div>
                            <span>Amarelo</span>
                          </label>

                          <label className="flex items-center gap-2 p-3 border rounded-md flex-1 cursor-pointer hover:bg-red-50">
                            <input type="radio" name="cartao-tipo" value="vermelho" className="h-4 w-4" />
                            <div className="h-6 w-4 bg-red-500 rounded-sm"></div>
                            <span>Vermelho</span>
                          </label>

                          <label className="flex items-center gap-2 p-3 border rounded-md flex-1 cursor-pointer hover:bg-yellow-50">
                            <input type="radio" name="cartao-tipo" value="segundo-amarelo" className="h-4 w-4" />
                            <div className="flex gap-1">
                              <div className="h-6 w-4 bg-yellow-500 rounded-sm"></div>
                              <div className="h-6 w-4 bg-red-500 rounded-sm"></div>
                            </div>
                            <span>2º Amarelo</span>
                          </label>
                        </div>
                      </div>

                      <div className="mb-4">
                        <label htmlFor="cartao-motivo" className="block text-sm font-medium mb-1">Motivo</label>
                        <select id="cartao-motivo" className="w-full p-2 border rounded-md mb-2">
                          <option value="">Selecione o motivo</option>
                          <option value="falta">Falta dura</option>
                          <option value="reclamacao">Reclamação</option>
                          <option value="simulacao">Simulação</option>
                          <option value="retardar">Retardar o jogo</option>
                          <option value="conduta">Conduta antidesportiva</option>
                        </select>

                        <textarea
                          id="cartao-descricao"
                          className="w-full p-3 border rounded-md"
                          rows={2}
                          placeholder="Descrição adicional..."
                        ></textarea>
                      </div>

                      <div className="flex justify-end">
                        <Button
                          className="bg-blue-600 hover:bg-blue-700 text-white"
                          onClick={handleRegisterCard}
                        >
                          <Square className="h-4 w-4 mr-2 text-yellow-500" /> Registrar Cartão
                        </Button>
                      </div>
                    </div>
                  )}

                  {activeTab === "gol" && (
                    <div className="bg-white rounded-lg shadow-md p-6 border">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <label htmlFor="gol-minuto" className="block text-sm font-medium mb-1">Minuto</label>
                          <input
                            type="text"
                            id="gol-minuto"
                            className="w-full p-2 border rounded-md"
                            value={gameState.timer}
                            readOnly
                          />
                        </div>

                        <div>
                          <label htmlFor="gol-time" className="block text-sm font-medium mb-1">Time</label>
                          <div className="flex gap-2">
                            <label className="flex items-center gap-2 p-3 border rounded-md flex-1 cursor-pointer hover:bg-blue-50">
                              <input
                                type="radio"
                                name="gol-time"
                                value="casa"
                                className="h-4 w-4"
                                checked={goalTeam === "casa"}
                                onChange={() => {
                                  setGoalTeam("casa");
                                  // Limpar o campo de jogador quando mudar o time
                                  const playerElement = document.getElementById('gol-jogador') as HTMLSelectElement;
                                  const assistElement = document.getElementById('gol-assistencia') as HTMLSelectElement;
                                  if (playerElement) playerElement.value = "";
                                  if (assistElement) assistElement.value = "";
                                }}
                              />
                              <span>{getTeamName(partidaAtual, clubInfo, true)}</span>
                            </label>

                            <label className="flex items-center gap-2 p-3 border rounded-md flex-1 cursor-pointer hover:bg-red-50">
                              <input
                                type="radio"
                                name="gol-time"
                                value="away"
                                className="h-4 w-4"
                                checked={goalTeam === "away"}
                                onChange={() => {
                                  setGoalTeam("away");
                                  // Limpar o campo de jogador quando mudar o time
                                  const playerElement = document.getElementById('gol-jogador') as HTMLSelectElement;
                                  const assistElement = document.getElementById('gol-assistencia') as HTMLSelectElement;
                                  if (playerElement) playerElement.value = "";
                                  if (assistElement) assistElement.value = "";
                                }}
                              />
                              <span>{getTeamName(partidaAtual, clubInfo, false)}</span>
                            </label>
                          </div>
                        </div>
                      </div>

                      {/* Campos de jogador e assistência (apenas visíveis para gols do nosso time) */}
                      {(partidaAtual?.type === "casa" && goalTeam === "casa") || (partidaAtual?.type === "fora" && goalTeam === "away") ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                          <div>
                            <label htmlFor="gol-jogador" className="block text-sm font-medium mb-1">Jogador</label>
                            <select id="gol-jogador" className="w-full p-2 border rounded-md">
                              <option value="">Selecione o jogador</option>
                              {loadingPlayers ? (
                                <option value="" disabled>Carregando jogadores...</option>
                              ) : (
                                players
                                  .filter(player => player.status !== "inativo")
                                  .map(player => (
                                    <option key={player.id} value={player.id}>
                                      {player.name} ({player.position})
                                    </option>
                                  ))
                              )}
                            </select>
                          </div>

                          <div>
                            <label htmlFor="gol-assistencia" className="block text-sm font-medium mb-1">Assistência (opcional)</label>
                            <select id="gol-assistencia" className="w-full p-2 border rounded-md">
                              <option value="">Selecione o jogador</option>
                              {loadingPlayers ? (
                                <option value="" disabled>Carregando jogadores...</option>
                              ) : (
                                players
                                  .filter(player => player.status !== "inativo")
                                  .map(player => (
                                    <option key={player.id} value={player.id}>
                                      {player.name} ({player.position})
                                    </option>
                                  ))
                              )}
                            </select>
                          </div>
                        </div>
                      ) : null}

                      <div className="mb-4">
                        <label htmlFor="gol-tipo" className="block text-sm font-medium mb-1">Tipo de Gol</label>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                          <label className="flex items-center gap-2 p-3 border rounded-md cursor-pointer hover:bg-gray-50">
                            <input type="radio" name="gol-tipo" value="normal" className="h-4 w-4" />
                            <span>Normal</span>
                          </label>

                          <label className="flex items-center gap-2 p-3 border rounded-md cursor-pointer hover:bg-gray-50">
                            <input type="radio" name="gol-tipo" value="penalti" className="h-4 w-4" />
                            <span>Pênalty</span>
                          </label>

                          <label className="flex items-center gap-2 p-3 border rounded-md cursor-pointer hover:bg-gray-50">
                            <input type="radio" name="gol-tipo" value="falta" className="h-4 w-4" />
                            <span>Falta</span>
                          </label>

                          <label className="flex items-center gap-2 p-3 border rounded-md cursor-pointer hover:bg-gray-50">
                            <input type="radio" name="gol-tipo" value="contra" className="h-4 w-4" />
                            <span>Gol Contra</span>
                          </label>
                        </div>
                      </div>

                      <div className="mb-4">
                        <label htmlFor="gol-descricao" className="block text-sm font-medium mb-1">Descrição (opcional)</label>
                        <textarea
                          id="gol-descricao"
                          className="w-full p-3 border rounded-md"
                          rows={2}
                          placeholder="Descrição do gol..."
                        ></textarea>
                      </div>

                      <div className="flex justify-end">
                        <Button
                          className="bg-blue-600 hover:bg-blue-700 text-white"
                          onClick={handleRegisterGoal}
                        >
                          <Target className="h-4 w-4 mr-2" /> Registrar Gol
                        </Button>
                      </div>
                    </div>
                  )}
                </div>

                <div className="bg-white rounded-lg shadow-md p-4">
                  <h3 className="text-xl font-bold mb-4 flex items-center">
                    <Clock className="h-5 w-5 mr-2 text-blue-600" /> Linha do Tempo
                  </h3>

                  <div className="border-l-2 border-blue-500 pl-4 space-y-6 max-h-[500px] overflow-y-auto pr-2">
                    <div className="relative">
                      <div className="absolute -left-[9px] w-4 h-4 rounded-full bg-blue-500 border-2 border-white"></div>
                      <div className="flex items-center">
                        <span className="text-sm font-mono bg-blue-100 text-blue-800 px-2 py-0.5 rounded mr-2">00:00</span>
                        <span className="font-medium">Início da partida</span>
                      </div>
                    </div>

                    {gameState.isLive && matchEvents.length > 0 ? (
                      matchEvents.map((event) => {
                        // Encontrar o jogador pelo ID
                        const player = players.find(p => p.id === event.player_id);
                        const playerIn = players.find(p => p.id === event.player_in_id);

                        // Determinar a cor e o estilo com base no tipo de evento
                        let bgColor = "bg-blue-500";
                        let textBgColor = "bg-blue-100 text-blue-800";
                        let title = "";
                        let description = "";

                        if (event.event_type === "goal") {
                          bgColor = "bg-green-500";
                          textBgColor = "bg-green-100 text-green-800";
                          title = `Gol: ${player?.name || 'Jogador'}`;

                          const assistPlayer = players.find(p => p.id === event.event_data?.assist_player_id);
                          if (assistPlayer) {
                            description = `Assistência: ${assistPlayer.name}`;
                          }

                          if (event.event_data?.type && event.event_data.type !== "normal") {
                            const goalType = {
                              penalty: "Pênalty",
                              free_kick: "Falta",
                              own_goal: "Gol Contra"
                            }[event.event_data.type] || "";

                            if (goalType) {
                              description += description ? ` | ${goalType}` : goalType;
                            }
                          }
                        } else if (event.event_type === "card") {
                          const cardType = event.event_data?.card_type;

                          if (cardType === "yellow" || cardType === "second_yellow") {
                            bgColor = "bg-yellow-500";
                            textBgColor = "bg-yellow-100 text-yellow-800";
                            title = `Cartão Amarelo: ${player?.name || 'Jogador'}`;

                            if (cardType === "second_yellow") {
                              title = `2º Cartão Amarelo: ${player?.name || 'Jogador'}`;
                            }
                          } else if (cardType === "red") {
                            bgColor = "bg-red-500";
                            textBgColor = "bg-red-100 text-red-800";
                            title = `Cartão Vermelho: ${player?.name || 'Jogador'}`;
                          }

                          description = event.event_data?.reason || "";
                        } else if (event.event_type === "substitution") {
                          bgColor = "bg-purple-500";
                          textBgColor = "bg-purple-100 text-purple-800";
                          title = "Substituição";
                          description = `Sai: ${player?.name || 'Jogador'} | Entra: ${playerIn?.name || 'Jogador'}`;

                          if (event.event_data?.reason) {
                            description += ` | Motivo: ${event.event_data.reason}`;
                          }
                        } else if (event.event_type === "note") {
                          bgColor = "bg-gray-500";
                          textBgColor = "bg-gray-100 text-gray-800";
                          title = "Nota";
                          description = event.event_data?.note || "";
                        }

                        return (
                          <div key={event.id} className="relative">
                            <div className={`absolute -left-[9px] w-4 h-4 rounded-full ${bgColor} border-2 border-white`}></div>
                            <div className="flex items-center">
                              <span className={`text-sm font-mono ${textBgColor} px-2 py-0.5 rounded mr-2`}>{event.minute || "--:--"}</span>
                              <span className="font-medium">{title}</span>
                            </div>
                            {description && <p className="text-sm text-gray-600 mt-1">{description}</p>}
                          </div>
                        );
                      })
                    ) : gameState.isLive ? (
                      <div className="text-gray-500 text-center py-4">
                        Nenhum evento registrado ainda.
                      </div>
                    ) : null}
                  </div>

                  {gameState.isLive && (
                    <div className="mt-4 flex justify-between">
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-xs"
                        onClick={fetchMatchEventData}
                      >
                        <RefreshCw className="h-3 w-3 mr-1" /> Atualizar
                      </Button>
                      <Button variant="outline" size="sm" className="text-xs">
                        <Download className="h-3 w-3 mr-1" /> Exportar
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tab Pós-Jogo */}
        <TabsContent value="pos-jogo">
          <Tabs defaultValue="estatisticas">
            <TabsList className="w-full mb-4">
              <TabsTrigger value="estatisticas" className="flex-1">Estatísticas</TabsTrigger>
              <TabsTrigger value="analise-tecnica" className="flex-1">Análise Técnica</TabsTrigger>
              <TabsTrigger value="avaliacao-individual" className="flex-1">Avaliação Individual</TabsTrigger>
            </TabsList>

            <TabsContent value="estatisticas">
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold mb-4">Estatísticas da Partida</h3>
                  <div className="text-sm text-gray-500 mb-4">
                    {partidaAtual ? (
                      `${getTeamName(partidaAtual, clubInfo, true)} ${gameState.homeScore} x ${gameState.awayScore} ${getTeamName(partidaAtual, clubInfo, false)}`
                    ) : (
                      "Selecione uma partida"
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold mb-2">Posse de Bola</h4>
                      <div className="flex items-center">
                        <div className="bg-blue-600 h-6 rounded-l-md" style={{ width: '65%' }}></div>
                        <div className="bg-red-600 h-6 rounded-r-md" style={{ width: '35%' }}></div>
                      </div>
                      <div className="flex justify-between text-sm mt-1">
                        <span>{getTeamName(partidaAtual, clubInfo, true)} 65%</span>
                        <span>{getTeamName(partidaAtual, clubInfo, false)} 35%</span>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-semibold mb-2">Estatísticas Detalhadas</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>Finalizações</span>
                          <div>
                            <span className="font-semibold">16</span> - <span className="font-semibold">7</span>
                          </div>
                        </div>
                        <div className="flex justify-between">
                          <span>Finalizações no gol</span>
                          <div>
                            <span className="font-semibold">8</span> - <span className="font-semibold">3</span>
                          </div>
                        </div>
                        <div className="flex justify-between">
                          <span>Escanteios</span>
                          <div>
                            <span className="font-semibold">7</span> - <span className="font-semibold">3</span>
                          </div>
                        </div>
                        <div className="flex justify-between">
                          <span>Impedimentos</span>
                          <div>
                            <span className="font-semibold">2</span> - <span className="font-semibold">1</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-8">
                    <h4 className="font-semibold mb-4">Estatísticas Individuais dos Jogadores</h4>

                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="bg-gray-100">
                            <th className="border p-2 text-left">Jogador</th>
                            <th className="border p-2 text-center">Min</th>
                            <th className="border p-2 text-center">Gols</th>
                            <th className="border p-2 text-center">Assist</th>
                            <th className="border p-2 text-center">Chutes</th>
                            <th className="border p-2 text-center">Chutes no Gol</th>
                            <th className="border p-2 text-center">Passes</th>
                            <th className="border p-2 text-center">Passes Comp.</th>
                            <th className="border p-2 text-center">Desarmes</th>
                            <th className="border p-2 text-center">CA</th>
                            <th className="border p-2 text-center">CV</th>
                            <th className="border p-2 text-center">Ações</th>
                          </tr>
                        </thead>
                        <tbody>
                          {players
                            .filter(player => player.status !== "inativo") // Filtrar jogadores inativos
                            .map(player => (
                            <tr key={player.id} id={`player-stats-row-${player.id}`} className="hover:bg-gray-50">
                              <td className="border p-2">{player.name}</td>
                              <td className="border p-2 text-center">
                                <input
                                  type="number"
                                  min="0"
                                  max="120"
                                  className="w-12 p-1 text-center border rounded"
                                  defaultValue={playerStats[player.id]?.minutes_played || "0"}
                                />
                              </td>
                              <td className="border p-2 text-center">
                                <input
                                  type="number"
                                  min="0"
                                  className="w-12 p-1 text-center border rounded"
                                  defaultValue={playerStats[player.id]?.goals || "0"}
                                />
                              </td>
                              <td className="border p-2 text-center">
                                <input
                                  type="number"
                                  min="0"
                                  className="w-12 p-1 text-center border rounded"
                                  defaultValue={playerStats[player.id]?.assists || "0"}
                                />
                              </td>
                              <td className="border p-2 text-center">
                                <input
                                  type="number"
                                  min="0"
                                  className="w-12 p-1 text-center border rounded"
                                  defaultValue={playerStats[player.id]?.shots || "0"}
                                />
                              </td>
                              <td className="border p-2 text-center">
                                <input
                                  type="number"
                                  min="0"
                                  className="w-12 p-1 text-center border rounded"
                                  defaultValue={playerStats[player.id]?.shots_on_target || "0"}
                                />
                              </td>
                              <td className="border p-2 text-center">
                                <input
                                  type="number"
                                  min="0"
                                  className="w-12 p-1 text-center border rounded"
                                  defaultValue={playerStats[player.id]?.passes || "0"}
                                />
                              </td>
                              <td className="border p-2 text-center">
                                <input
                                  type="number"
                                  min="0"
                                  className="w-12 p-1 text-center border rounded"
                                  defaultValue={playerStats[player.id]?.passes_completed || "0"}
                                />
                              </td>
                              <td className="border p-2 text-center">
                                <input
                                  type="number"
                                  min="0"
                                  className="w-12 p-1 text-center border rounded"
                                  defaultValue={playerStats[player.id]?.tackles || "0"}
                                />
                              </td>
                              <td className="border p-2 text-center">
                                <input
                                  type="number"
                                  min="0"
                                  className="w-12 p-1 text-center border rounded"
                                  defaultValue={playerStats[player.id]?.yellow_cards || "0"}
                                />
                              </td>
                              <td className="border p-2 text-center">
                                <input
                                  type="number"
                                  min="0"
                                  className="w-12 p-1 text-center border rounded"
                                  defaultValue={playerStats[player.id]?.red_cards || "0"}
                                />
                              </td>
                              <td className="border p-2 text-center">
                                <button
                                  className="bg-blue-600 text-white px-2 py-1 rounded text-xs"
                                  onClick={() => {
                                    // Obter os valores dos inputs para este jogador
                                    const row = document.getElementById(`player-stats-row-${player.id}`);
                                    if (!row) return;

                                    const inputs = row.querySelectorAll('input[type="number"]');
                                    const stats = {
                                      minutes_played: parseInt((inputs[0] as HTMLInputElement).value) || 0,
                                      goals: parseInt((inputs[1] as HTMLInputElement).value) || 0,
                                      assists: parseInt((inputs[2] as HTMLInputElement).value) || 0,
                                      shots: parseInt((inputs[3] as HTMLInputElement).value) || 0,
                                      shots_on_target: parseInt((inputs[4] as HTMLInputElement).value) || 0,
                                      passes: parseInt((inputs[5] as HTMLInputElement).value) || 0,
                                      passes_completed: parseInt((inputs[6] as HTMLInputElement).value) || 0,
                                      tackles: parseInt((inputs[7] as HTMLInputElement).value) || 0,
                                      interceptions: parseInt((inputs[7] as HTMLInputElement).value) / 2 || 0, // Estimativa de interceptações baseada em desarmes
                                      yellow_cards: parseInt((inputs[8] as HTMLInputElement).value) || 0,
                                      red_cards: parseInt((inputs[9] as HTMLInputElement).value) || 0
                                    };

                                    // Salvar as estatísticas
                                    if (partidaAtual && user?.id) {
                                      savePlayerMatchStatistics(
                                        clubId,
                                        partidaAtual.id,
                                        player.id,
                                        stats,
                                        user.id
                                      )
                                      .then(() => {
                                        toast({
                                          title: "Estatísticas salvas",
                                          description: `Estatísticas de ${player.name} salvas com sucesso.`,
                                        });
                                      })
                                      .catch(error => {
                                        console.error("Erro ao salvar estatísticas:", error);
                                        toast({
                                          title: "Erro",
                                          description: "Não foi possível salvar as estatísticas do jogador.",
                                          variant: "destructive",
                                        });
                                      });
                                    }
                                  }}
                                >
                                  Salvar
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>

                    <div className="flex justify-end mt-4">
                      <Button
                        className="bg-green-600 hover:bg-green-700"
                        onClick={async () => {
                          if (!partidaAtual || !user?.id) return;

                          // Obter todos os jogadores ativos
                          const activePlayers = players.filter(player => player.status !== "inativo");

                          // Para cada jogador, salvar suas estatísticas
                          const promises = activePlayers.map(player => {
                            const row = document.getElementById(`player-stats-row-${player.id}`);
                            if (!row) return Promise.resolve();

                            const inputs = row.querySelectorAll('input[type="number"]');
                            const stats = {
                              minutes_played: parseInt((inputs[0] as HTMLInputElement).value) || 0,
                              goals: parseInt((inputs[1] as HTMLInputElement).value) || 0,
                              assists: parseInt((inputs[2] as HTMLInputElement).value) || 0,
                              shots: parseInt((inputs[3] as HTMLInputElement).value) || 0,
                              shots_on_target: parseInt((inputs[4] as HTMLInputElement).value) || 0,
                              passes: parseInt((inputs[5] as HTMLInputElement).value) || 0,
                              passes_completed: parseInt((inputs[6] as HTMLInputElement).value) || 0,
                              tackles: parseInt((inputs[7] as HTMLInputElement).value) || 0,
                              interceptions: parseInt((inputs[7] as HTMLInputElement).value) / 2 || 0, // Estimativa de interceptações
                              yellow_cards: parseInt((inputs[8] as HTMLInputElement).value) || 0,
                              red_cards: parseInt((inputs[9] as HTMLInputElement).value) || 0
                            };

                            return savePlayerMatchStatistics(
                              clubId,
                              partidaAtual.id,
                              player.id,
                              stats,
                              user.id
                            );
                          });

                          try {
                            await Promise.all(promises);
                            toast({
                              title: "Estatísticas salvas",
                              description: "Todas as estatísticas foram salvas com sucesso.",
                            });
                          } catch (error) {
                            console.error("Erro ao salvar estatísticas:", error);
                            toast({
                              title: "Erro",
                              description: "Ocorreu um erro ao salvar as estatísticas.",
                              variant: "destructive",
                            });
                          }
                        }}
                      >
                        Salvar Todas as Estatísticas
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analise-tecnica">
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold mb-4">Análise Tática da Partida</h3>
                  <div className="text-sm text-gray-500 mb-4">
                    {partidaAtual ? (
                      `${getTeamName(partidaAtual, clubInfo, true)} ${gameState.homeScore} x ${gameState.awayScore} ${getTeamName(partidaAtual, clubInfo, false)} | ${partidaAtual.date}`
                    ) : (
                      "Selecione uma partida"
                    )}
                  </div>

                  <textarea
                    className="w-full p-3 border rounded-md mb-4"
                    rows={8}
                    placeholder="Faça uma análise detalhada da partida..."
                  ></textarea>

                  <div className="mb-6">
                    <h4 className="font-semibold mb-2">Estatísticas da Equipe</h4>

                    {Object.keys(playerStats).length === 0 ? (
                      <div className="text-center p-4 text-gray-500">
                        Nenhuma estatística disponível para esta partida
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <h5 className="text-sm font-medium mb-2">Estatísticas Ofensivas</h5>
                          <ul className="space-y-2">
                            <li className="flex justify-between items-center">
                              <span>Total de Gols</span>
                              <span className="font-medium">
                                {Object.values(playerStats).reduce((sum, stat) => sum + ((stat as any).goals || 0), 0)}
                              </span>
                            </li>
                            <li className="flex justify-between items-center">
                              <span>Total de Assistências</span>
                              <span className="font-medium">
                                {Object.values(playerStats).reduce((sum, stat) => sum + ((stat as any).assists || 0), 0)}
                              </span>
                            </li>
                            <li className="flex justify-between items-center">
                              <span>Finalizações</span>
                              <span className="font-medium">
                                {Object.values(playerStats).reduce((sum, stat) => sum + ((stat as any).shots || 0), 0)}
                              </span>
                            </li>
                            <li className="flex justify-between items-center">
                              <span>Finalizações no Gol</span>
                              <span className="font-medium">
                                {Object.values(playerStats).reduce((sum, stat) => sum + ((stat as any).shots_on_target || 0), 0)}
                              </span>
                            </li>
                          </ul>
                        </div>

                        <div>
                          <h5 className="text-sm font-medium mb-2">Estatísticas Defensivas</h5>
                          <ul className="space-y-2">
                            <li className="flex justify-between items-center">
                              <span>Desarmes</span>
                              <span className="font-medium">
                                {Object.values(playerStats).reduce((sum, stat) => sum + ((stat as any).tackles || 0), 0)}
                              </span>
                            </li>
                            <li className="flex justify-between items-center">
                              <span>Interceptações</span>
                              <span className="font-medium">
                                {Object.values(playerStats).reduce((sum, stat) => sum + ((stat as any).interceptions || 0), 0)}
                              </span>
                            </li>
                            <li className="flex justify-between items-center">
                              <span>Cartões Amarelos</span>
                              <span className="font-medium">
                                {Object.values(playerStats).reduce((sum, stat) => sum + ((stat as any).yellow_cards || 0), 0)}
                              </span>
                            </li>
                            <li className="flex justify-between items-center">
                              <span>Cartões Vermelhos</span>
                              <span className="font-medium">
                                {Object.values(playerStats).reduce((sum, stat) => sum + ((stat as any).red_cards || 0), 0)}
                              </span>
                            </li>
                          </ul>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="mb-6">
                    <h4 className="font-semibold mb-2">Pontos a Melhorar</h4>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <input type="text" className="flex-1 p-2 border rounded-md" placeholder="Adicionar ponto a melhorar..." />
                        <button className="bg-blue-600 text-white p-2 rounded-md">
                          <Plus className="h-4 w-4" />
                        </button>
                      </div>

                      {Object.keys(playerStats).length > 0 && (
                        <ul className="space-y-2 mt-3">
                          {(() => {
                            // Analisar estatísticas para sugerir pontos a melhorar
                            const suggestions = [];

                            // Verificar precisão de finalização
                            const totalShots = Object.values(playerStats).reduce((sum, stat) => sum + ((stat as any).shots || 0), 0);
                            const shotsOnTarget = Object.values(playerStats).reduce((sum, stat) => sum + ((stat as any).shots_on_target || 0), 0);
                            const shotAccuracy = totalShots > 0 ? (shotsOnTarget / totalShots) * 100 : 0;

                            if (shotAccuracy < 40 && totalShots > 5) {
                              suggestions.push(
                                <li key="shot-accuracy" className="flex items-center gap-2">
                                  <span className="h-3 w-3 rounded-full bg-red-500"></span>
                                  <span>Melhorar precisão nas finalizações ({shotAccuracy.toFixed(1)}%)</span>
                                </li>
                              );
                            }

                            // Verificar precisão de passes
                            const totalPasses = Object.values(playerStats).reduce((sum, stat) => sum + ((stat as any).passes || 0), 0);
                            const completedPasses = Object.values(playerStats).reduce((sum, stat) => sum + ((stat as any).passes_completed || 0), 0);
                            const passAccuracy = totalPasses > 0 ? (completedPasses / totalPasses) * 100 : 0;

                            if (passAccuracy < 70 && totalPasses > 50) {
                              suggestions.push(
                                <li key="pass-accuracy" className="flex items-center gap-2">
                                  <span className="h-3 w-3 rounded-full bg-orange-500"></span>
                                  <span>Melhorar precisão nos passes ({passAccuracy.toFixed(1)}%)</span>
                                </li>
                              );
                            }

                            // Verificar cartões
                            const yellowCards = Object.values(playerStats).reduce((sum, stat) => sum + ((stat as any).yellow_cards || 0), 0);
                            const redCards = Object.values(playerStats).reduce((sum, stat) => sum + ((stat as any).red_cards || 0), 0);

                            if (yellowCards > 2 || redCards > 0) {
                              suggestions.push(
                                <li key="discipline" className="flex items-center gap-2">
                                  <span className="h-3 w-3 rounded-full bg-yellow-500"></span>
                                  <span>Melhorar disciplina tática ({yellowCards} cartões amarelos, {redCards} cartões vermelhos)</span>
                                </li>
                              );
                            }

                            return suggestions.length > 0 ? suggestions : (
                              <li className="text-gray-500">Adicione pontos a melhorar com base na sua análise</li>
                            );
                          })()}
                        </ul>
                      )}
                    </div>
                  </div>

                  <div className="flex justify-end gap-2">
                    <button className="border border-gray-300 px-4 py-2 rounded-md flex items-center gap-1">
                      <span>💾</span> Salvar como Rascunho
                    </button>
                    <button className="bg-blue-600 text-white px-4 py-2 rounded-md">
                      Salvar Análise
                    </button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="avaliacao-individual">
              <Card>
                <CardContent className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h3 className="text-xl font-bold mb-4">Avaliação de Desempenho Individual</h3>

                      {loadingPlayers ? (
                        <div className="flex items-center justify-center p-6">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        </div>
                      ) : !players || players.length === 0 ? (
                        <div className="text-center p-6 text-gray-500">
                          Nenhum jogador encontrado
                        </div>
                      ) : (
                        <div className="space-y-3">
                          {players
                            .filter(player => player.status !== "inativo")
                            .slice(0, 5)
                            .map(player => {
                              // Calcular pontuação baseada nas estatísticas reais
                              const stats = playerStats[player.id] || {};
                              const totalActions = (stats.minutes_played || 0) +
                                                  (stats.goals || 0) * 10 +
                                                  (stats.assists || 0) * 5 +
                                                  (stats.shots_on_target || 0) * 2 +
                                                  (stats.passes_completed || 0) * 0.1 +
                                                  (stats.tackles || 0) * 2;

                              // Calcular uma pontuação de 0 a 10
                              const score = Math.min(10, totalActions / 20);
                              const scoreFormatted = score.toFixed(1);
                              const scorePercentage = Math.min(100, score * 10);

                              return (
                                <div key={player.id}>
                                  <div className="flex justify-between items-center mb-1">
                                    <span>{player.name}</span>
                                    <span className="font-semibold">{scoreFormatted}</span>
                                  </div>
                                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                                    <div
                                      className="bg-blue-600 h-2.5 rounded-full"
                                      style={{ width: `${scorePercentage}%` }}
                                    ></div>
                                  </div>
                                </div>
                              );
                            })
                          }
                        </div>
                      )}
                    </div>

                    <div>
                      <h3 className="text-xl font-bold mb-4">Feedback Individual</h3>

                      <div className="mb-4">
                        <label htmlFor="jogador-feedback" className="block text-sm font-medium mb-1">Selecione um jogador</label>
                        <select
                          id="jogador-feedback"
                          className="w-full p-2 border rounded-md mb-3"
                        >
                          <option value="">Selecione um jogador</option>
                          {loadingPlayers ? (
                            <option value="" disabled>Carregando jogadores...</option>
                          ) : !players || players.length === 0 ? (
                            <option value="" disabled>Nenhum jogador encontrado</option>
                          ) : (
                            players
                              .filter(player => player.status !== "inativo")
                              .map(player => (
                                <option key={player.id} value={player.id}>
                                  {player.name} ({player.position})
                                </option>
                              ))
                          )}
                        </select>

                        <textarea
                          className="w-full p-3 border rounded-md mb-3"
                          rows={6}
                          placeholder="Escreva um feedback para este jogador..."
                        ></textarea>

                        <button className="bg-blue-600 text-white px-4 py-2 rounded-md w-full">
                          Enviar Feedback
                        </button>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6">
                    <h3 className="text-xl font-bold mb-4">Destaques da Partida</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {/* Melhor em Campo - baseado em gols e assistências */}
                      <div className="bg-gray-100 p-4 rounded-md">
                        <h4 className="text-center text-sm text-gray-500 mb-2">Melhor em Campo</h4>
                        {loadingPlayers ? (
                          <div className="flex justify-center p-2">
                            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                          </div>
                        ) : (
                          <>
                            {(() => {
                              // Encontrar o jogador com mais gols e assistências
                              const bestPlayer = Object.entries(playerStats)
                                .map(([playerId, stats]) => {
                                  const player = players.find(p => p.id === playerId);
                                  if (!player || player.status === "inativo") return null;
                                  return {
                                    player,
                                    score: ((stats as any).goals || 0) * 2 + ((stats as any).assists || 0)
                                  };
                                })
                                .filter(Boolean)
                                .sort((a, b) => b!.score - a!.score)[0];

                              if (!bestPlayer) {
                                return (
                                  <div className="text-center">
                                    <h5 className="text-center font-bold mb-1">Sem dados</h5>
                                    <div className="text-sm">Nenhuma estatística disponível</div>
                                  </div>
                                );
                              }

                              const stats = playerStats[bestPlayer.player.id];
                              return (
                                <>
                                  <h5 className="text-center font-bold mb-1">{bestPlayer.player.name}</h5>
                                  <div className="flex justify-center items-center gap-2">
                                    <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
                                      {bestPlayer.score.toFixed(1)}
                                    </span>
                                    <span className="text-sm">
                                      {stats.goals || 0} gols, {stats.assists || 0} assistências
                                    </span>
                                  </div>
                                </>
                              );
                            })()}
                          </>
                        )}
                      </div>

                      {/* Melhor Passe - baseado em passes completados e precisão */}
                      <div className="bg-gray-100 p-4 rounded-md">
                        <h4 className="text-center text-sm text-gray-500 mb-2">Melhor Passe</h4>
                        {loadingPlayers ? (
                          <div className="flex justify-center p-2">
                            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                          </div>
                        ) : (
                          <>
                            {(() => {
                              // Encontrar o jogador com melhor precisão de passes
                              const bestPasser = Object.entries(playerStats)
                                .map(([playerId, stats]) => {
                                  const player = players.find(p => p.id === playerId);
                                  if (!player || player.status === "inativo") return null;
                                  const passes = (stats as any).passes || 0;
                                  const passesCompleted = (stats as any).passes_completed || 0;
                                  const accuracy = passes > 0 ? (passesCompleted / passes) * 100 : 0;
                                  return {
                                    player,
                                    accuracy,
                                    passesCompleted
                                  };
                                })
                                .filter(Boolean)
                                .filter(item => item!.passesCompleted > 10) // Mínimo de 10 passes para considerar
                                .sort((a, b) => b!.accuracy - a!.accuracy)[0];

                              if (!bestPasser) {
                                return (
                                  <div className="text-center">
                                    <h5 className="text-center font-bold mb-1">Sem dados</h5>
                                    <div className="text-sm">Nenhuma estatística disponível</div>
                                  </div>
                                );
                              }

                              return (
                                <>
                                  <h5 className="text-center font-bold mb-1">{bestPasser.player.name}</h5>
                                  <div className="flex justify-center items-center gap-2">
                                    <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
                                      {bestPasser.accuracy.toFixed(1)}% precisão
                                    </span>
                                    <span className="text-sm">
                                      {bestPasser.passesCompleted} passes completados
                                    </span>
                                  </div>
                                </>
                              );
                            })()}
                          </>
                        )}
                      </div>

                      {/* Melhor Defensor - baseado em desarmes */}
                      <div className="bg-gray-100 p-4 rounded-md">
                        <h4 className="text-center text-sm text-gray-500 mb-2">Melhor Defensor</h4>
                        {loadingPlayers ? (
                          <div className="flex justify-center p-2">
                            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                          </div>
                        ) : (
                          <>
                            {(() => {
                              // Encontrar o jogador com mais desarmes
                              const bestDefender = Object.entries(playerStats)
                                .map(([playerId, stats]) => {
                                  const player = players.find(p => p.id === playerId);
                                  if (!player || player.status === "inativo") return null;
                                  return {
                                    player,
                                    tackles: (stats as any).tackles || 0,
                                    interceptions: (stats as any).interceptions || 0
                                  };
                                })
                                .filter(Boolean)
                                .sort((a, b) => (b!.tackles + b!.interceptions) - (a!.tackles + a!.interceptions))[0];

                              if (!bestDefender) {
                                return (
                                  <div className="text-center">
                                    <h5 className="text-center font-bold mb-1">Sem dados</h5>
                                    <div className="text-sm">Nenhuma estatística disponível</div>
                                  </div>
                                );
                              }

                              return (
                                <>
                                  <h5 className="text-center font-bold mb-1">{bestDefender.player.name}</h5>
                                  <div className="flex justify-center items-center gap-2">
                                    <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
                                      {bestDefender.tackles + bestDefender.interceptions}
                                    </span>
                                    <span className="text-sm">
                                      {bestDefender.tackles} desarmes, {bestDefender.interceptions} interceptações
                                    </span>
                                  </div>
                                </>
                              );
                            })()}
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </TabsContent>
      </Tabs>
      <NovaTarefaDialog open={novaTarefaDialogOpen} onOpenChange={setNovaTarefaDialogOpen} clubId={clubId}/>
      <AdicionarJogoDialog open={adicionarJogoDialogOpen} onOpenChange={setAdicionarJogoDialogOpen} clubId={clubId}/>
      {partidaParaFinalizar && (
        <FinalizarPartidaDialog
          open={finalizarDialogOpen}
          onOpenChange={(open) => {
            setFinalizarDialogOpen(open);
            if (!open) setPartidaParaFinalizar(null);
          }}
          match={partidaParaFinalizar}
          clubId={clubId}
        />
      )}

      {/* Modal de Análise do Adversário */}
      <Dialog open={analiseAdversarioOpen} onOpenChange={setAnaliseAdversarioOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Análise do Adversário</DialogTitle>
            <DialogDescription>
              Adicione informações detalhadas sobre o adversário para ajudar no planejamento tático.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="tecnico" className="block text-sm font-medium mb-1">Técnico</label>
                <input
                  id="tecnico"
                  className="w-full p-2 border rounded-md"
                  value={analiseData.tecnico}
                  onChange={(e) => setAnaliseData({...analiseData, tecnico: e.target.value})}
                />
              </div>

              <div>
                <label htmlFor="formacao" className="block text-sm font-medium mb-1">Formação Típica</label>
                <input
                  id="formacao"
                  className="w-full p-2 border rounded-md"
                  value={analiseData.formacao}
                  onChange={(e) => setAnaliseData({...analiseData, formacao: e.target.value})}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Pontos Fortes</label>
              {analiseData.pontosFortes.map((ponto, index) => (
                <div key={`forte-${index}`} className="flex gap-2 mb-2">
                  <input
                    className="flex-1 p-2 border rounded-md"
                    value={ponto}
                    onChange={(e) => {
                      const newPontos = [...analiseData.pontosFortes];
                      newPontos[index] = e.target.value;
                      setAnaliseData({...analiseData, pontosFortes: newPontos});
                    }}
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      const newPontos = analiseData.pontosFortes.filter((_, i) => i !== index);
                      setAnaliseData({...analiseData, pontosFortes: newPontos});
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setAnaliseData({...analiseData, pontosFortes: [...analiseData.pontosFortes, ""]});
                }}
              >
                <Plus className="h-4 w-4 mr-1" /> Adicionar Ponto Forte
              </Button>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Pontos Fracos</label>
              {analiseData.pontosFracos.map((ponto, index) => (
                <div key={`fraco-${index}`} className="flex gap-2 mb-2">
                  <input
                    className="flex-1 p-2 border rounded-md"
                    value={ponto}
                    onChange={(e) => {
                      const newPontos = [...analiseData.pontosFracos];
                      newPontos[index] = e.target.value;
                      setAnaliseData({...analiseData, pontosFracos: newPontos});
                    }}
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      const newPontos = analiseData.pontosFracos.filter((_, i) => i !== index);
                      setAnaliseData({...analiseData, pontosFracos: newPontos});
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setAnaliseData({...analiseData, pontosFracos: [...analiseData.pontosFracos, ""]});
                }}
              >
                <Plus className="h-4 w-4 mr-1" /> Adicionar Ponto Fraco
              </Button>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Estratégias Recomendadas</label>
              {analiseData.estrategias.map((estrategia, index) => (
                <div key={`estrategia-${index}`} className="flex gap-2 mb-2">
                  <input
                    className="flex-1 p-2 border rounded-md"
                    value={estrategia}
                    onChange={(e) => {
                      const newEstrategias = [...analiseData.estrategias];
                      newEstrategias[index] = e.target.value;
                      setAnaliseData({...analiseData, estrategias: newEstrategias});
                    }}
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      const newEstrategias = analiseData.estrategias.filter((_, i) => i !== index);
                      setAnaliseData({...analiseData, estrategias: newEstrategias});
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setAnaliseData({...analiseData, estrategias: [...analiseData.estrategias, ""]});
                }}
              >
                <Plus className="h-4 w-4 mr-1" /> Adicionar Estratégia
              </Button>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setAnaliseAdversarioOpen(false)}>Cancelar</Button>
            <Button onClick={() => setAnaliseAdversarioOpen(false)}>Salvar Análise</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
