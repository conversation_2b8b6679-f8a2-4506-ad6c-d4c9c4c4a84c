// Função para converter hex para HSL
export function hexToHsl(hex: string): string {
  // Remove o # se presente
  hex = hex.replace('#', '');
  
  // Converte para RGB
  const r = parseInt(hex.substr(0, 2), 16) / 255;
  const g = parseInt(hex.substr(2, 2), 16) / 255;
  const b = parseInt(hex.substr(4, 2), 16) / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  let s = 0;
  const l = (max + min) / 2;

  if (max === min) {
    h = s = 0; // achromatic
  } else {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }

  return `${Math.round(h * 360)} ${Math.round(s * 100)}% ${Math.round(l * 100)}%`;
}

// Função para converter hex para RGB array (para uso em PDFs)
export function hexToRgbArray(hex: string): [number, number, number] {
  // Remove o # se presente
  hex = hex.replace('#', '');
  
  // Converte para RGB
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);

  return [r, g, b];
}

// Função para obter a cor primária do clube atual
export function getClubPrimaryColor(): string {
  // Primeiro tenta obter do CSS custom property
  const primaryColor = getComputedStyle(document.documentElement)
    .getPropertyValue('--color-primary')
    .trim();
  
  if (primaryColor) {
    return primaryColor;
  }
  
  // Fallback para azul padrão
  return '#1e40af';
}

// Função para obter a cor primária do clube como RGB array
export function getClubPrimaryColorRgb(): [number, number, number] {
  const primaryColor = getClubPrimaryColor();
  return hexToRgbArray(primaryColor);
}

// Função para criar uma versão mais clara da cor (para relatórios que precisam de preenchimento manual)
export function getLighterClubColor(opacity: number = 0.3): [number, number, number] {
  const [r, g, b] = getClubPrimaryColorRgb();
  
  // Mistura com branco para criar uma versão mais clara
  const lighterR = Math.round(r + (255 - r) * (1 - opacity));
  const lighterG = Math.round(g + (255 - g) * (1 - opacity));
  const lighterB = Math.round(b + (255 - b) * (1 - opacity));
  
  return [lighterR, lighterG, lighterB];
}
