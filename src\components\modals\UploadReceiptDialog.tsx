import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { FinancialAccount } from "@/api/api";
import { useFinancialAccountsStore } from "@/store/useFinancialAccountsStore";
import { toast } from "@/hooks/use-toast";
import { Upload } from "lucide-react";

interface UploadReceiptDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  account: FinancialAccount | null;
  clubId: number;
}

export function UploadReceiptDialog({ open, onOpenChange, account, clubId }: UploadReceiptDialogProps) {
  const [file, setFile] = useState<File | null>(null);
  const { uploadReceipt, loading } = useFinancialAccountsStore();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  const handleUpload = async () => {
    if (!account) {
      toast({
        title: "Erro",
        description: "Nenhuma conta selecionada.",
        variant: "destructive",
      });
      return;
    }

    if (!file) {
      toast({
        title: "Erro",
        description: "Selecione um arquivo para upload.",
        variant: "destructive",
      });
      return;
    }

    try {
      await uploadReceipt(clubId, account.id, file);
      toast({
        title: "Comprovante enviado",
        description: "O comprovante foi enviado com sucesso.",
      });
      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao fazer upload do comprovante:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao fazer upload do comprovante.",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Upload de Comprovante</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {account && (
            <div className="space-y-2">
              <p className="text-sm font-medium">
                Conta: <span className="font-normal">{account.description}</span>
              </p>
              <p className="text-sm font-medium">
                Valor: <span className="font-normal">R$ {account.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</span>
              </p>
              <p className="text-sm font-medium">
                Fornecedor/Cliente: <span className="font-normal">{account.supplier_client}</span>
              </p>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="receipt">Selecione o arquivo do comprovante</Label>
            <div className="flex items-center gap-2">
              <Input
                id="receipt"
                type="file"
                onChange={handleFileChange}
                accept=".pdf,.jpg,.jpeg,.png"
              />
            </div>
            {file && (
              <p className="text-sm text-muted-foreground">
                Arquivo selecionado: {file.name} ({(file.size / 1024).toFixed(2)} KB)
              </p>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleUpload} disabled={!file || loading}>
            <Upload className="h-4 w-4 mr-2" />
            Enviar Comprovante
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
