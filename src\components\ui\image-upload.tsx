import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Upload, X, Image as ImageIcon } from "lucide-react";

interface ImageUploadProps {
  value?: string;
  onChange: (value: string | null, file?: File) => void;
  className?: string;
  disabled?: boolean;
  maxSize?: number; // em MB
  aspectRatio?: number; // largura / altura
  shape?: "square" | "circle";
  previewSize?: number; // tamanho em pixels
  placeholder?: string;
}

export function ImageUpload({
  value,
  onChange,
  className = "",
  disabled = false,
  maxSize = 2, // 2MB por padrão
  aspectRatio,
  shape = "square",
  previewSize = 150,
  placeholder = "Selecione uma imagem",
}: ImageUploadProps) {
  const [dragActive, setDragActive] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const validateFile = (file: File): boolean => {
    // Verificar se é uma imagem
    if (!file.type.startsWith("image/")) {
      setError("O arquivo deve ser uma imagem");
      return false;
    }

    // Verificar tamanho
    const maxSizeBytes = maxSize * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      setError(`A imagem deve ter no máximo ${maxSize}MB`);
      return false;
    }

    setError(null);
    return true;
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];
      if (validateFile(file)) {
        handleFileChange(file);
      }
    }
  };

  const handleFileChange = (file: File) => {
    // Criar URL para preview
    const fileUrl = URL.createObjectURL(file);
    // Passar tanto a URL para preview quanto o arquivo para upload
    onChange(fileUrl, file);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      if (validateFile(file)) {
        handleFileChange(file);
      }
    }
  };

  const handleRemove = () => {
    onChange(null);
    if (inputRef.current) {
      inputRef.current.value = "";
    }
  };

  const containerStyle: React.CSSProperties = {
    width: previewSize,
    height: aspectRatio ? previewSize / aspectRatio : previewSize,
    borderRadius: shape === "circle" ? "50%" : "8px",
  };

  return (
    <div className={`flex flex-col items-center ${className}`}>
      <div
        className={`relative border-2 ${
          dragActive ? "border-primary" : "border-dashed border-gray-300"
        } ${disabled ? "opacity-50" : ""} overflow-hidden`}
        style={containerStyle}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        {value ? (
          <>
            <img
              src={value}
              alt="Preview"
              className="w-full h-full object-cover"
            />
            {!disabled && (
              <button
                type="button"
                className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1"
                onClick={handleRemove}
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </>
        ) : (
          <div className="flex flex-col items-center justify-center w-full h-full p-4 text-center">
            <ImageIcon className="h-10 w-10 text-gray-400 mb-2" />
            <p className="text-sm text-gray-500">{placeholder}</p>
            <p className="text-xs text-gray-400 mt-1">
              Arraste ou clique para selecionar
            </p>
          </div>
        )}

        <input
          ref={inputRef}
          type="file"
          accept="image/*"
          className="hidden"
          onChange={handleInputChange}
          disabled={disabled}
        />
      </div>

      {!value && !disabled && (
        <Button
          type="button"
          variant="outline"
          size="sm"
          className="mt-2"
          onClick={() => inputRef.current?.click()}
        >
          <Upload className="h-4 w-4 mr-2" />
          Selecionar imagem
        </Button>
      )}

      {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
    </div>
  );
}
