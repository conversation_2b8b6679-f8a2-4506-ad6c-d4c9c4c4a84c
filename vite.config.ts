import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  // Ensure environment variables are properly loaded
  define: {
    'import.meta.env.VITE_BREVO_API_KEY': JSON.stringify(process.env.VITE_BREVO_API_KEY || ''),
    'import.meta.env.VITE_BREVO_SENDER_NAME': JSON.stringify(process.env.VITE_BREVO_SENDER_NAME || 'clubeFut'),
    'import.meta.env.VITE_BREVO_SENDER_EMAIL': JSON.stringify(process.env.VITE_BREVO_SENDER_EMAIL || '<EMAIL>'),
  },
}));
