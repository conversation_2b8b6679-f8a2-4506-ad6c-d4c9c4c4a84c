import { jsPDF } from "jspdf";
import 'jspdf-autotable';
import { ClubInfo } from "@/api/api";
import autoTable from "jspdf-autotable";
import { getLighterClubColor } from '@/utils/themeUtils';

// Ordem das posições para o relatório
const POSITION_ORDER = [
  "Goleiro",
  "Zagueiro",
  "Lateral",
  "Volante",
  "Meio-campista",
  "Extremo",
  "Atacante",
  "Outro"
];

// Tipo para jsPDF com autoTable
type jsPDFWithAutoTable = jsPDF & {
  autoTable: typeof autoTable;
  lastAutoTable: {
    finalY: number;
  };
};

/**
 * Gera um relatório de rouparia com colunas para preenchimento manual
 * @param clothingData Dados de rouparia por categoria
 * @param clubInfo Informações do clube
 * @param filename Nome do arquivo PDF
 */
export async function generateClothingReport(
  clothingData: {
    category: string;
    players: {
      id: string;
      name: string;
      nickname?: string;
      birthdate?: string;
      position: string;
      registration_number?: string;
    }[];
  }[],
  clubInfo: ClubInfo,
  filename: string = 'relatorio-rouparia.pdf'
): Promise<void> {
  // Criar um novo documento PDF
  const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

  // Configurações de página
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const margin = 10;

  // Adicionar título
  const title = 'Relatório de Rouparia';
  doc.setFontSize(18);
  doc.text(title, pageWidth / 2, 20, { align: 'center' });

  // Adicionar informações do clube
  doc.setFontSize(12);
  doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, margin, 30);

  // Tentar adicionar o logo do clube, se disponível
  if (clubInfo.logo_url) {
    try {
      const img = new Image();

      // Criar uma promessa para lidar com o carregamento da imagem
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            // Calcular proporção para manter as proporções
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;

            // Adicionar a imagem ao PDF (alinhada à direita)
            doc.addImage(img, 'PNG', pageWidth - margin - imgWidth, 10, imgWidth, imgHeight);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });

      // Iniciar o carregamento da imagem
      img.src = clubInfo.logo_url;

      // Aguardar o carregamento e processamento da imagem
      await loadImage;
    } catch (logoError) {
      console.error("Erro ao adicionar logo ao PDF:", logoError);
    }
  }

  // Adicionar data do relatório (lado esquerdo para não sobrepor o logo)
  const currentDate = new Date().toLocaleDateString('pt-BR');
  doc.setFontSize(10);
  doc.text(`Data: ${currentDate}`, margin, 40);

  // Posição Y inicial para o conteúdo
  let yPosition = 50;

  // Para cada categoria, mostrar os jogadores
  clothingData.forEach((categoryData, index) => {
    // Título da categoria
    doc.setFontSize(14);
    doc.setTextColor(0, 0, 0);
    doc.setFillColor(242, 242, 242);
    doc.rect(margin, yPosition, pageWidth - 2 * margin, 8, 'F');
    doc.text(`Categoria: ${categoryData.category}`, margin + 2, yPosition + 5);
    yPosition += 12;

    // Ordenar jogadores primeiro por posição, depois por nome
    const sortedPlayers = [...categoryData.players].sort((a, b) => {
      const posA = POSITION_ORDER.indexOf(a.position);
      const posB = POSITION_ORDER.indexOf(b.position);

      // Se ambas as posições estão na lista, ordenar conforme a lista
      if (posA !== -1 && posB !== -1) {
        if (posA !== posB) {
          return posA - posB;
        }
      }

      // Se apenas uma posição está na lista, ela vem primeiro
      if (posA !== -1 && posB === -1) return -1;
      if (posA === -1 && posB !== -1) return 1;

      // Se nenhuma posição está na lista ou são da mesma posição, ordenar alfabeticamente por nome
      return a.name.localeCompare(b.name);
    });

    // Preparar cabeçalhos da tabela - 7 colunas vazias sem texto
    const headers = ['Posição', 'Nome', 'Apelido', 'Data Nasc.', 'NC', '', '', '', '', '', '', ''];

    // Preparar dados para a tabela
    const tableData = sortedPlayers.map(player => [
      player.position || '-',
      player.name || '-',
      player.nickname || '-',
      player.birthdate ? new Date(player.birthdate).toLocaleDateString('pt-BR') : '-',
      '', // NC - campo vazio para preenchimento manual
      '', '', '', '', '', '', '' // 7 colunas vazias para preenchimento manual
    ]);

    // Adicionar a tabela ao PDF
    autoTable(doc, {
      startY: yPosition,
      head: [headers],
      body: tableData,
      theme: 'grid', // Mudança para 'grid' para ter bordas mais visíveis
      headStyles: {
        fillColor: getLighterClubColor(0.7),
        textColor: [255, 255, 255],
        lineWidth: 0.5,
        lineColor: [0, 0, 0]
      },
      bodyStyles: {
        lineWidth: 0.5,
        lineColor: [0, 0, 0]
      },
      margin: { left: margin, right: margin },
      styles: {
        fontSize: 8,
        cellPadding: 3,
        lineWidth: 0.5,
        lineColor: [0, 0, 0]
      },
      columnStyles: {
        0: { cellWidth: 20, halign: 'center' }, // Posição
        1: { cellWidth: 'auto' }, // Nome
        2: { cellWidth: 'auto' }, // Apelido
        3: { cellWidth: 20, halign: 'center' }, // Data Nasc.
        4: { cellWidth: 15, halign: 'center', lineWidth: 0.8, lineColor: [0, 0, 0] }, // NC - borda mais forte
        5: { cellWidth: 12, halign: 'center', lineWidth: 0.8, lineColor: [0, 0, 0] }, // Coluna vazia 1
        6: { cellWidth: 12, halign: 'center', lineWidth: 0.8, lineColor: [0, 0, 0] }, // Coluna vazia 2
        7: { cellWidth: 12, halign: 'center', lineWidth: 0.8, lineColor: [0, 0, 0] }, // Coluna vazia 3
        8: { cellWidth: 12, halign: 'center', lineWidth: 0.8, lineColor: [0, 0, 0] }, // Coluna vazia 4
        9: { cellWidth: 12, halign: 'center', lineWidth: 0.8, lineColor: [0, 0, 0] }, // Coluna vazia 5
        10: { cellWidth: 12, halign: 'center', lineWidth: 0.8, lineColor: [0, 0, 0] }, // Coluna vazia 6
        11: { cellWidth: 12, halign: 'center', lineWidth: 0.8, lineColor: [0, 0, 0] }  // Coluna vazia 7
      },
      didDrawPage: (data) => {
        // Adicionar cabeçalho em cada página (exceto a primeira)
        if (data.pageNumber > 1) {
          // Salvar o estado atual
          const currentFontSize = doc.getFontSize();
          const currentTextColor = doc.getTextColor();

          // Configurar cabeçalho
          doc.setFontSize(12);
          doc.setTextColor(0, 0, 0);
          doc.text(title, pageWidth / 2, 15, { align: 'center' });

          doc.setFontSize(10);
          doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, margin, 25);
          doc.text(`Data: ${currentDate}`, margin, 30);

          // Adicionar linha separadora
          doc.setDrawColor(200, 200, 200);
          doc.line(margin, 35, pageWidth - margin, 35);

          // Restaurar o estado anterior
          doc.setFontSize(currentFontSize);
          doc.setTextColor(currentTextColor);
        }
      }
    });

    // Atualizar a posição Y para o próximo conteúdo
    const docWithTable = doc as jsPDFWithAutoTable;
    yPosition = docWithTable.lastAutoTable.finalY + 15;

    // Adicionar linha divisória entre categorias (exceto a última)
    if (index < clothingData.length - 1) {
      doc.setDrawColor(200, 200, 200);
      doc.setLineDashPattern([3, 3], 0);
      doc.line(margin, yPosition, pageWidth - margin, yPosition);
      doc.setLineDashPattern([], 0); // Resetar para linha sólida
      yPosition += 10;
    }
  });

  // Adicionar rodapé
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.text(
      `© ${new Date().getFullYear()} ${clubInfo.name} - Todos os direitos reservados`,
      pageWidth / 2,
      pageHeight - 10,
      { align: 'center' }
    );
    doc.text(
      `Página ${i} de ${pageCount}`,
      pageWidth - margin,
      pageHeight - 10,
      { align: 'right' }
    );
  }

  // Salvar o PDF
  doc.save(filename);
}
