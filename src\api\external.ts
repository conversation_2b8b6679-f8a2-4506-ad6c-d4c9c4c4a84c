/**
 * Serviço para integração com APIs externas
 */

/**
 * Busca endereço pelo CEP usando a API ViaCEP
 * @param cep CEP a ser consultado (apenas números)
 * @returns Dados do endereço
 */
export async function getAddressByCEP(cep: string): Promise<{
  cep: string;
  logradouro: string;
  complemento: string;
  bairro: string;
  localidade: string;
  uf: string;
  ibge: string;
  gia: string;
  ddd: string;
  siafi: string;
}> {
  try {
    // Remover caracteres não numéricos
    const cleanCEP = cep.replace(/\D/g, "");

    // Validar CEP
    if (cleanCEP.length !== 8) {
      throw new Error("CEP inválido. Deve conter 8 dígitos.");
    }

    // Fazer requisição à API ViaCEP
    const response = await fetch(`https://viacep.com.br/ws/${cleanCEP}/json/`);
    const data = await response.json();

    // Verificar se houve erro
    if (data.erro) {
      throw new Error("CEP não encontrado");
    }

    return data;
  } catch (error: any) {
    console.error("Erro ao buscar endereço por CEP:", error);
    throw new Error(error.message || "Erro ao buscar endereço por CEP");
  }
}

/**
 * Formata um CEP adicionando a máscara
 * @param cep CEP a ser formatado
 * @returns CEP formatado
 */
export function formatCEP(cep: string): string {
  // Remover caracteres não numéricos
  const cleanCEP = cep.replace(/\D/g, "");

  // Aplicar máscara
  return cleanCEP.replace(/^(\d{5})(\d{3})$/, "$1-$2");
}

/**
 * Formata um CPF adicionando a máscara
 * @param cpf CPF a ser formatado
 * @returns CPF formatado
 */
export function formatCPF(cpf: string): string {
  // Remover caracteres não numéricos
  const cleanCPF = cpf.replace(/\D/g, "");

  // Aplicar máscara
  return cleanCPF.replace(/^(\d{3})(\d{3})(\d{3})(\d{2})$/, "$1.$2.$3-$4");
}

/**
 * Valida um CPF
 * @param cpf CPF a ser validado
 * @returns true se o CPF for válido
 */
export function validateCPF(cpf: string): boolean {
  // Remover caracteres não numéricos
  const cleanCPF = cpf.replace(/\D/g, "");

  // Verificar se tem 11 dígitos
  if (cleanCPF.length !== 11) {
    return false;
  }

  // Verificar se todos os dígitos são iguais
  if (/^(\d)\1+$/.test(cleanCPF)) {
    return false;
  }

  // Validar dígitos verificadores
  let sum = 0;
  let remainder;

  // Primeiro dígito verificador
  for (let i = 1; i <= 9; i++) {
    sum += parseInt(cleanCPF.substring(i - 1, i)) * (11 - i);
  }
  remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) {
    remainder = 0;
  }
  if (remainder !== parseInt(cleanCPF.substring(9, 10))) {
    return false;
  }

  // Segundo dígito verificador
  sum = 0;
  for (let i = 1; i <= 10; i++) {
    sum += parseInt(cleanCPF.substring(i - 1, i)) * (12 - i);
  }
  remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) {
    remainder = 0;
  }
  if (remainder !== parseInt(cleanCPF.substring(10, 11))) {
    return false;
  }

  return true;
}

/**
 * Extrai o primeiro nome de um nome completo
 * @param fullName Nome completo
 * @returns Primeiro nome
 */
export function getFirstName(fullName: string): string {
  if (!fullName) return "";
  return fullName.split(" ")[0] || "";
}
