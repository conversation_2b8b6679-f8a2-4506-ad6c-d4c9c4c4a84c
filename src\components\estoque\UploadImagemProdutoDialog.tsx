import { useState, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, Upload, Image as ImageIcon } from "lucide-react";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { supabase } from "@/integrations/supabase/client";
import { updateProductImage } from "@/api/api";

interface UploadImagemProdutoDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  productId: number | null;
  productName: string;
  onSuccess: () => void;
}

export function UploadImagemProdutoDialog({
  open,
  onOpenChange,
  productId,
  productName,
  onSuccess,
}: UploadImagemProdutoDialogProps) {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setSelectedFile(file);
    
    if (file) {
      // Create preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setPreviewUrl(null);
    }
  };

  // Trigger file input click
  const handleSelectFile = () => {
    fileInputRef.current?.click();
  };

  // Upload image
  const handleUpload = async () => {
    if (!productId || !selectedFile) return;

    setIsLoading(true);

    try {
      // Upload image to Supabase Storage
      const fileName = `products/${clubId}/${productId}_${Date.now()}.${selectedFile.name.split('.').pop()}`;
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('profileimages')
        .upload(fileName, selectedFile, {
          upsert: true
        });

      if (uploadError) {
        throw new Error(`Erro ao fazer upload da imagem: ${uploadError.message}`);
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('profileimages')
        .getPublicUrl(fileName);

      const imageUrl = urlData.publicUrl;

      // Update product with image URL
      await updateProductImage(clubId, productId, imageUrl, user?.id);

      toast({
        title: "Imagem enviada",
        description: "A imagem do produto foi atualizada com sucesso.",
      });

      onSuccess();
      onOpenChange(false);
    } catch (error) {
      console.error("Error uploading image:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao enviar a imagem. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[400px]">
        <DialogHeader>
          <DialogTitle>Enviar Imagem do Produto</DialogTitle>
        </DialogHeader>

        <div className="py-4">
          <p className="text-sm text-muted-foreground mb-4">
            Selecione uma imagem para o produto <strong>{productName}</strong>.
          </p>

          <div className="flex flex-col items-center justify-center">
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              accept="image/*"
              className="hidden"
            />

            {previewUrl ? (
              <div className="mb-4 border rounded-md overflow-hidden">
                <img
                  src={previewUrl}
                  alt="Preview"
                  className="max-h-[200px] object-contain"
                />
              </div>
            ) : (
              <div className="mb-4 border rounded-md p-8 flex flex-col items-center justify-center bg-gray-50">
                <ImageIcon className="h-16 w-16 text-gray-400 mb-2" />
                <p className="text-sm text-gray-500">Nenhuma imagem selecionada</p>
              </div>
            )}

            <Button
              type="button"
              variant="outline"
              onClick={handleSelectFile}
              className="gap-2"
            >
              <Upload className="h-4 w-4" />
              Selecionar Imagem
            </Button>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button
            onClick={handleUpload}
            disabled={isLoading || !selectedFile}
            className="gap-2"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Upload className="h-4 w-4" />
            )}
            Enviar Imagem
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
