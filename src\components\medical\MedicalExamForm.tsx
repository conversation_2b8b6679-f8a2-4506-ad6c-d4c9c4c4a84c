import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { CalendarIcon, Plus, Upload } from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { useMedicalExamsStore } from "@/store/useMedicalExamsStore";
import { getMedicalProfessionalByUserId, MedicalProfessional } from "@/api/api";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { usePermission } from "@/hooks/usePermission";

interface MedicalExamFormProps {
  recordId: number;
  playerId: string;
  onExamAdded?: () => void;
}

export function MedicalExamForm({ recordId, playerId, onExamAdded }: MedicalExamFormProps) {
  const { toast } = useToast();
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { role } = usePermission();
  const { addExam, loading } = useMedicalExamsStore();

  const [dialogOpen, setDialogOpen] = useState(false);
  const [medicalProfessional, setMedicalProfessional] = useState<MedicalProfessional | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Form state
  const [examType, setExamType] = useState("");
  const [requestDate, setRequestDate] = useState<Date | undefined>(new Date());
  const [examDate, setExamDate] = useState<Date | undefined>(undefined);
  const [notes, setNotes] = useState("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [customExamType, setCustomExamType] = useState("");

  // Load medical professional when component mounts
  useEffect(() => {
    if (clubId && user?.id && role === "medical") {
      loadMedicalProfessional();
    }
  }, [clubId, user?.id, role]);

  // Load medical professional data
  const loadMedicalProfessional = async () => {
    try {
      const data = await getMedicalProfessionalByUserId(clubId, user?.id || "");
      setMedicalProfessional(data);
    } catch (error) {
      console.error("Erro ao carregar dados do profissional médico:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar os dados do profissional médico",
        variant: "destructive",
      });
    }
  };

  // Reset form
  const resetForm = () => {
    setExamType("");
    setRequestDate(new Date());
    setExamDate(undefined);
    setNotes("");
    setSelectedFile(null);
    setCustomExamType("");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!medicalProfessional) {
      toast({
        title: "Erro",
        description: "Profissional médico não encontrado",
        variant: "destructive",
      });
      return;
    }

    const finalExamType = examType === "outro" ? customExamType : examType;

    if (!finalExamType || !requestDate) {
      toast({
        title: "Campos obrigatórios",
        description: "Por favor, preencha todos os campos obrigatórios",
        variant: "destructive",
      });
      return;
    }

    try {
      // Format dates
      const formattedRequestDate = format(requestDate, "yyyy-MM-dd");
      const formattedExamDate = examDate ? format(examDate, "yyyy-MM-dd") : undefined;

      const newExam = await addExam(clubId, user?.id || "", {
        record_id: recordId,
        player_id: playerId,
        exam_type: finalExamType,
        request_date: formattedRequestDate,
        exam_date: formattedExamDate,
        requested_by: medicalProfessional.id,
        status: "Solicitado",
        notes,
      });

      toast({
        title: "Exame solicitado",
        description: "O exame foi solicitado com sucesso",
      });

      resetForm();
      setDialogOpen(false);
      if (onExamAdded) {
        onExamAdded();
      }
    } catch (error) {
      console.error("Erro ao solicitar exame:", error);
      toast({
        title: "Erro",
        description: "Não foi possível solicitar o exame",
        variant: "destructive",
      });
    }
  };

  return (
    <>
      <Button onClick={() => setDialogOpen(true)} className="bg-primary hover:bg-primary/90">
        <Plus className="h-4 w-4 mr-2" />
        Solicitar Exame
      </Button>

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader className="pb-4 border-b">
            <DialogTitle className="text-xl font-semibold flex items-center gap-2">
              <span className="w-2 h-2 bg-primary rounded-full"></span>
              Solicitar Exame Médico
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6 py-4">
            <div className="space-y-2">
              <Label htmlFor="examType" className="text-sm font-medium">
                Tipo de Exame*
              </Label>
              <Select value={examType} onValueChange={setExamType}>
                <SelectTrigger id="examType">
                  <SelectValue placeholder="Selecione o tipo de exame" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Raio-X">Raio-X</SelectItem>
                  <SelectItem value="Ressonância Magnética">Ressonância Magnética</SelectItem>
                  <SelectItem value="Ultrassonografia">Ultrassonografia</SelectItem>
                  <SelectItem value="Tomografia">Tomografia</SelectItem>
                  <SelectItem value="Eletrocardiograma">Eletrocardiograma</SelectItem>
                  <SelectItem value="Hemograma">Hemograma</SelectItem>
                  <SelectItem value="Bioquímico">Bioquímico</SelectItem>
                  <SelectItem value="Urina">Exame de Urina</SelectItem>
                  <SelectItem value="outro">Outro</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {examType === "outro" && (
              <div className="space-y-2">
                <Label htmlFor="customExamType" className="text-sm font-medium">
                  Especifique o tipo de exame*
                </Label>
                <Input
                  id="customExamType"
                  placeholder="Digite o tipo de exame"
                  value={customExamType}
                  onChange={(e) => setCustomExamType(e.target.value)}
                />
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="requestDate" className="text-sm font-medium">
                  Data da Solicitação*
                </Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !requestDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {requestDate ? format(requestDate, "PPP", { locale: ptBR }) : <span>Selecione uma data</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={requestDate}
                      onSelect={setRequestDate}
                      initialFocus
                      locale={ptBR}
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label htmlFor="examDate" className="text-sm font-medium">
                  Data do Exame
                </Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !examDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {examDate ? format(examDate, "PPP", { locale: ptBR }) : <span>Selecione uma data</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={examDate}
                      onSelect={setExamDate}
                      initialFocus
                      locale={ptBR}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes" className="text-sm font-medium">
                Observações
              </Label>
              <Textarea
                id="notes"
                placeholder="Observações sobre o exame (opcional)"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
                className="resize-none"
              />
            </div>
          </div>

          <DialogFooter className="pt-4 border-t bg-muted/20">
            <div className="flex gap-3 w-full">
              <Button
                variant="outline"
                onClick={() => setDialogOpen(false)}
                className="flex-1"
              >
                Cancelar
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={loading}
                className="flex-1 bg-primary hover:bg-primary/90"
              >
                {loading ? (
                  <>
                    <span className="animate-spin mr-2">⏳</span>
                    Solicitando...
                  </>
                ) : (
                  "Solicitar Exame"
                )}
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
