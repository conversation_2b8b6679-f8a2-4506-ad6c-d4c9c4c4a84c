import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { signIn } from "@/api/auth";
import { getUserClubs } from "@/api/api";
import { LogIn } from "lucide-react";

export default function Login() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const navigate = useNavigate();



  async function handleLogin(e: React.FormEvent) {
    e.preventDefault();
    setLoading(true);
    setError("");
    try {
      // 1. Login via Supabase Auth
      const { user, session } = await signIn(email, password);
      if (!user || !user.id) throw new Error("Usuário ou senha inválidos");

      // Salvar dados do usuário
      localStorage.setItem("userId", user.id);
      if (session && session.access_token) {
        localStorage.setItem("token", session.access_token);
      }

      // 2. Buscar clubes do usuário
      const clubs = await getUserClubs(user.id);
      if (clubs.length > 0) {
        localStorage.setItem("clubId", String(clubs[0].id));
      } else {
        setError("Usuário não possui clube associado. Cadastre-se novamente.");
        setLoading(false);
        return;
      }

      // Redirecionar para o dashboard
      setLoading(false);
      navigate("/dashboard");
    } catch (err) {
      console.error("Erro de login:", err);
      setError("E-mail ou senha inválidos.");
      setLoading(false);
    }
  }

  return (
    <div
      className="min-h-screen flex items-center justify-center bg-cover bg-center"
      style={{
        backgroundImage: "url('https://images.unsplash.com/photo-1508098682722-e99c643e7f3b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')",
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }}
    >
      {/* Overlay gradient with team colors */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/80 to-primary/70"></div>

      <div className="relative z-10 w-full max-w-md px-4">
        {/* Logo and title */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-white/20 backdrop-blur-sm mb-4 shadow-lg">
            <div className="w-16 h-16 rounded-full bg-gradient-to-br from-primary to-primary/80 flex items-center justify-center">
              <span className="text-white font-bold text-2xl">GDN</span>
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-2 drop-shadow-md">Game Day Nexus</h1>
          <p className="text-white/90 text-lg font-medium">Plataforma de Gestão Esportiva</p>
        </div>

        {/* Login card */}
        <Card className="w-full rounded-xl shadow-2xl overflow-hidden border-0 bg-white/95 backdrop-blur-sm">
          <CardHeader className="pb-4">
            <CardTitle className="text-center text-blue-800 text-2xl">Bem-vindo</CardTitle>
            <CardDescription className="text-center text-blue-600">
              Acesse sua conta para gerenciar seu clube
            </CardDescription>
          </CardHeader>

          <CardContent>
            <form className="space-y-6" onSubmit={handleLogin}>
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">E-mail</label>
                <div className="relative">
                  <Input
                    type="email"
                    autoComplete="username"
                    value={email}
                    onChange={e => setEmail(e.target.value)}
                    required
                    placeholder="<EMAIL>"
                    className="pl-3 pr-3 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 block w-full"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Senha</label>
                <div className="relative">
                  <Input
                    type="password"
                    autoComplete="current-password"
                    value={password}
                    onChange={e => setPassword(e.target.value)}
                    required
                    placeholder="••••••••"
                    className="pl-3 pr-3 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 block w-full"
                  />
                </div>
              </div>

              {error && (
                <div className="bg-red-50 text-red-600 text-sm p-3 rounded-lg border border-red-200">
                  {error}
                </div>
              )}

              <Button
                type="submit"
                className="w-full bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg"
                disabled={loading}
              >
                {loading ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Entrando...
                  </span>
                ) : (
                  <span className="flex items-center justify-center">
                    <LogIn className="mr-2 h-4 w-4" />
                    Entrar
                  </span>
                )}
              </Button>

              <div className="text-center text-sm mt-4 text-gray-600">
                Não tem uma conta?{' '}
                <span className="text-blue-600 font-medium hover:text-blue-800 cursor-pointer transition-colors">
                  Entre em contato!
                </span>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
