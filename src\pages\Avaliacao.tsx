import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CreateEvaluationInvitationForm } from "@/components/evaluation/CreateEvaluationInvitationForm";
import { EvaluationInvitationsTable } from "@/components/evaluation/EvaluationInvitationsTable";
import { PlayersInEvaluationTable } from "@/components/evaluation/PlayersInEvaluationTable";
import { EvaluationStatusCounts } from "@/components/evaluation/EvaluationStatusCounts";
import { usePermission } from "@/hooks/usePermission";

export default function Avaliacao() {
  const { can } = usePermission();
  const [activeTab, setActiveTab] = useState("players");

  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  const handleInvitationCreated = () => {
    // Switch to invitations tab after creating a new invitation
    setActiveTab("invitations");
  };

  return (
    <div className="container py-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Pré Cadastro de Atletas</h1>
          <p className="text-muted-foreground">
            Gerencie o processo de pré cadastro de novos atletas
          </p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="players">Atletas em Pré Cadastro</TabsTrigger>
          <TabsTrigger value="invitations">Convites</TabsTrigger>
          <TabsTrigger value="create">Novo Convite</TabsTrigger>
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
        </TabsList>

        <TabsContent value="players" className="space-y-6">
          <PlayersInEvaluationTable />
        </TabsContent>

        <TabsContent value="invitations" className="space-y-6">
          <EvaluationInvitationsTable />
        </TabsContent>

        <TabsContent value="create" className="space-y-6">
          {can("players.create") ? (
            <CreateEvaluationInvitationForm onSuccess={handleInvitationCreated} />
          ) : (
            <div className="p-6 text-center">
              <p className="text-muted-foreground">
                Você não tem permissão para criar convites de pré cadastro.
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="dashboard" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Atletas por Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <EvaluationStatusCounts />

                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Próximos Pré Cadastros</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center p-8 text-muted-foreground">
                    Funcionalidade em desenvolvimento
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
