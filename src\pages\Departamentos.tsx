import { useState } from "react";
import { toast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { deleteDepartment, Department } from "@/api/api";
import { DepartmentList } from "@/components/departments/DepartmentList";
import { DepartmentForm } from "@/components/departments/DepartmentForm";
import { DepartmentUsers } from "@/components/departments/DepartmentUsers";
import { AddUserToDepartment } from "@/components/departments/AddUserToDepartment";

export default function Departamentos() {
  const clubId = useCurrentClubId();
  
  // Estados para controle dos modais
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isUsersOpen, setIsUsersOpen] = useState(false);
  const [isAddUserOpen, setIsAddUserOpen] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState<Department | undefined>(undefined);
  
  // Função para abrir o modal de adição de departamento
  const handleAddDepartment = () => {
    setSelectedDepartment(undefined);
    setIsFormOpen(true);
  };
  
  // Função para abrir o modal de edição de departamento
  const handleEditDepartment = (department: Department) => {
    setSelectedDepartment(department);
    setIsFormOpen(true);
  };
  
  // Função para excluir um departamento
  const handleDeleteDepartment = async (department: Department) => {
    if (!confirm(`Tem certeza que deseja excluir o departamento ${department.name}?`)) {
      return;
    }
    
    try {
      await deleteDepartment(department.id);
      
      toast({
        title: "Sucesso",
        description: "Departamento excluído com sucesso",
      });
      
      // Recarregar a lista de departamentos
      window.location.reload();
    } catch (err: any) {
      console.error("Erro ao excluir departamento:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao excluir departamento",
        variant: "destructive",
      });
    }
  };
  
  // Função para abrir o modal de visualização de usuários
  const handleViewUsers = (department: Department) => {
    setSelectedDepartment(department);
    setIsUsersOpen(true);
  };
  
  // Função para abrir o modal de adição de usuário
  const handleAddUser = () => {
    setIsAddUserOpen(true);
  };
  
  // Função chamada após sucesso na operação
  const handleSuccess = () => {
    // Recarregar a lista de departamentos
    window.location.reload();
  };
  
  return (
    <div>
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Departamentos</h1>
        <p className="text-muted-foreground">
          Gerencie os departamentos e usuários da sua organização
        </p>
      </div>
      
      <DepartmentList
        onAddDepartment={handleAddDepartment}
        onEditDepartment={handleEditDepartment}
        onDeleteDepartment={handleDeleteDepartment}
        onViewUsers={handleViewUsers}
      />
      
      <DepartmentForm
        open={isFormOpen}
        onOpenChange={setIsFormOpen}
        department={selectedDepartment}
        onSuccess={handleSuccess}
      />
      
      <DepartmentUsers
        open={isUsersOpen}
        onOpenChange={setIsUsersOpen}
        department={selectedDepartment}
        onAddUser={handleAddUser}
      />
      
      <AddUserToDepartment
        open={isAddUserOpen}
        onOpenChange={setIsAddUserOpen}
        department={selectedDepartment}
        onSuccess={handleSuccess}
      />
    </div>
  );
}
