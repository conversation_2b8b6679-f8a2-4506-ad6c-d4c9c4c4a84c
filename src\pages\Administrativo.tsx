import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { OficiosTab } from "@/components/administrativo/OficiosTab";
import { TarefasKanbanTab } from "@/components/administrativo/TarefasKanbanTab";
import { LembretesTab } from "@/components/administrativo/LembretesTab";
import { ColaboradoresTab } from "@/components/administrativo/ColaboradoresTab";
import { FornecedoresTab } from "@/components/administrativo/FornecedoresTab";
import { useCurrentClubId } from "@/context/ClubContext";
import { useAdministrativeDocumentsStore } from "@/store/useAdministrativeDocumentsStore";
import { useAdministrativeTasksStore } from "@/store/useAdministrativeTasksStore";
import { useAdministrativeRemindersStore } from "@/store/useAdministrativeRemindersStore";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ban, Calendar, Users, Building } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

export default function Administrativo() {
  const [activeTab, setActiveTab] = useState("oficios");
  const clubId = useCurrentClubId();

  // Stores
  const {
    documents,
    loading: loadingDocuments,
    error: documentsError,
    fetchDocuments
  } = useAdministrativeDocumentsStore();

  const {
    tasks,
    loading: loadingTasks,
    error: tasksError,
    fetchTasks
  } = useAdministrativeTasksStore();

  const {
    reminders,
    loading: loadingReminders,
    error: remindersError,
    fetchReminders
  } = useAdministrativeRemindersStore();

  // State for collaborators and suppliers
  const [collaborators, setCollaborators] = useState<any[]>([]);
  const [suppliers, setSuppliers] = useState<any[]>([]);
  const [loadingCollaborators, setLoadingCollaborators] = useState(false);
  const [loadingSuppliers, setLoadingSuppliers] = useState(false);
  const [collaboratorsError, setCollaboratorsError] = useState<string | null>(null);
  const [suppliersError, setSuppliersError] = useState<string | null>(null);

  // Fetch data when component mounts
  useEffect(() => {
    fetchDocuments(clubId);
    fetchTasks(clubId);
    fetchReminders(clubId);
    fetchCollaborators();
    fetchSuppliers();
  }, [clubId, fetchDocuments, fetchTasks, fetchReminders]);

  // Fetch collaborators
  const fetchCollaborators = async () => {
    try {
      setLoadingCollaborators(true);
      setCollaboratorsError(null);

      // Usar o Supabase diretamente para evitar problemas de tipagem
      const { data, error } = await supabase
        .from("collaborators")
        .select("*")
        .eq("club_id", clubId)
        .order("full_name");

      if (error) {
        throw new Error(`Error fetching collaborators: ${error.message}`);
      }

      setCollaborators(data || []);
    } catch (err: any) {
      console.error("Error fetching collaborators:", err);
      setCollaboratorsError(err.message || "Error fetching collaborators");
    } finally {
      setLoadingCollaborators(false);
    }
  };

  // Fetch suppliers
  const fetchSuppliers = async () => {
    try {
      setLoadingSuppliers(true);
      setSuppliersError(null);

      // Usar o Supabase diretamente para evitar problemas de tipagem
      const { data, error } = await supabase
        .from("suppliers")
        .select("*")
        .eq("club_id", clubId)
        .order("company_name");

      if (error) {
        throw new Error(`Error fetching suppliers: ${error.message}`);
      }

      setSuppliers(data || []);
    } catch (err: any) {
      console.error("Error fetching suppliers:", err);
      setSuppliersError(err.message || "Error fetching suppliers");
    } finally {
      setLoadingSuppliers(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Administrativo</h1>
        <p className="text-muted-foreground">
          Gerencie documentos, tarefas e lembretes administrativos
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="oficios" className="flex items-center gap-1">
            <FileText className="h-4 w-4" />
            <span>Ofícios e Memorandos</span>
          </TabsTrigger>
          <TabsTrigger value="tarefas" className="flex items-center gap-1">
            <Kanban className="h-4 w-4" />
            <span>Tarefas Diárias (Kanban)</span>
          </TabsTrigger>
          <TabsTrigger value="lembretes" className="flex items-center gap-1">
            <Calendar className="h-4 w-4" />
            <span>Agenda e Lembretes</span>
          </TabsTrigger>
          <TabsTrigger value="colaboradores" className="flex items-center gap-1">
            <Users className="h-4 w-4" />
            <span>Colaboradores</span>
          </TabsTrigger>
          <TabsTrigger value="fornecedores" className="flex items-center gap-1">
            <Building className="h-4 w-4" />
            <span>Fornecedores</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="oficios">
          <OficiosTab
            documents={documents}
            loading={loadingDocuments}
            error={documentsError}
            clubId={clubId}
          />
        </TabsContent>

        <TabsContent value="tarefas">
          <TarefasKanbanTab
            tasks={tasks}
            loading={loadingTasks}
            error={tasksError}
            clubId={clubId}
          />
        </TabsContent>

        <TabsContent value="lembretes">
          <LembretesTab
            reminders={reminders}
            loading={loadingReminders}
            error={remindersError}
            clubId={clubId}
          />
        </TabsContent>

        <TabsContent value="colaboradores">
          <ColaboradoresTab
            collaborators={collaborators}
            loading={loadingCollaborators}
            error={collaboratorsError}
            clubId={clubId}
            onRefresh={fetchCollaborators}
          />
        </TabsContent>

        <TabsContent value="fornecedores">
          <FornecedoresTab
            suppliers={suppliers}
            loading={loadingSuppliers}
            error={suppliersError}
            clubId={clubId}
            onRefresh={fetchSuppliers}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
