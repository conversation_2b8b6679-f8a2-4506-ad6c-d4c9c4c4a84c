import { jsPDF } from "jspdf";
import autoTable from 'jspdf-autotable';
import { InventoryProduct, ClubInfo } from "@/api/api";
import { getClubPrimaryColorRgb } from '@/utils/themeUtils';

// Tipo para jsPDF com autoTable
type jsPDFWithAutoTable = jsPDF & {
  autoTable: typeof autoTable;
  lastAutoTable: {
    finalY: number;
  };
}

/**
 * Gera um relatório de estoque em PDF para um departamento específico
 * @param products Lista de produtos do estoque
 * @param department Nome do departamento (ou "all" para todos)
 * @param clubInfo Informações do clube
 * @param filename Nome do arquivo PDF
 */
export async function generateInventoryReport(
  products: InventoryProduct[],
  department: string | null,
  clubInfo: ClubInfo,
  filename: string = 'relatorio-estoque.pdf'
): Promise<void> {
  // Filtrar produtos pelo departamento, se especificado
  const filteredProducts = department
    ? products.filter(product => product.department === department)
    : products;

  // Ordenar produtos por nome
  const sortedProducts = [...filteredProducts].sort((a, b) => a.name.localeCompare(b.name));

  // Criar um novo documento PDF
  const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

  // Adicionar título
  const title = department
    ? `Relatório de Estoque - ${department}`
    : 'Relatório de Estoque - Todos os Departamentos';

  doc.setFontSize(18);
  doc.text(title, 14, 22);

  // Adicionar informações do clube
  doc.setFontSize(12);
  doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, 14, 30);

  if (clubInfo.address) {
    doc.text(`Endereço: ${clubInfo.address}`, 14, 36);
  }

  if (clubInfo.phone) {
    doc.text(`Telefone: ${clubInfo.phone}`, 14, 42);
  }

  // Adicionar data do relatório
  const today = new Date().toLocaleDateString('pt-BR');
  doc.text(`Data do relatório: ${today}`, 14, 48);

  // Adicionar logo se disponível
  if (clubInfo.logo_url) {
    try {
      // Carregar a imagem e aplicar a rotação
      const img = new Image();
      img.crossOrigin = "Anonymous";

      // Criar uma promessa para carregar a imagem
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            // Criar um canvas para rotacionar a imagem
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d')!;

            // Definir as dimensões do canvas
            const size = Math.max(img.width, img.height);
            canvas.width = size;
            canvas.height = size;

            // Mover para o centro do canvas
            ctx.translate(size / 2, size / 2);


            // Desenhar a imagem centralizada
            ctx.drawImage(img, -img.width / 2, -img.height / 2, img.width, img.height);

            // Obter a imagem rotacionada como data URL
            const rotatedImageUrl = canvas.toDataURL('image/png');

            // Adicionar a imagem rotacionada ao PDF
            doc.addImage(rotatedImageUrl, 'PNG', 160, 15, 30, 30);

            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });

      // Iniciar o carregamento da imagem
      img.src = clubInfo.logo_url;

      // Aguardar o carregamento e processamento da imagem
      await loadImage;
    } catch (logoError) {
      console.error("Erro ao adicionar logo ao PDF:", logoError);
    }
  }

  // Preparar dados para a tabela
  const tableData = sortedProducts.map(product => [
    product.name,
    `${product.quantity} ${product.unit_of_measure || 'unidade'}`,
    `${product.minimum_quantity || 0} ${product.unit_of_measure || 'unidade'}`,
    product.department,
    product.location || '-',
    new Date(product.registration_date).toLocaleDateString('pt-BR'),
    product.description || '-'
  ]);

  // Adicionar a tabela ao PDF
  autoTable(doc, {
    startY: 60,
    head: [['Nome do Produto', 'Quantidade', 'Qtd. Mínima', 'Departamento', 'Localização', 'Data de Cadastro', 'Descrição']],
    body: tableData,
    theme: 'striped',
    headStyles: { fillColor: getClubPrimaryColorRgb() },
    didDrawPage: (data) => {
      // Adicionar cabeçalho em cada página
      if (data.pageNumber > 1) {
        doc.setFontSize(10);
        doc.text(title, 14, 10);
        doc.setFontSize(8);
        doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, 14, 15);
      }
    }
  });

  // Adicionar rodapé com numeração de páginas
  // @ts-expect-error - A tipagem do jsPDF está incompleta
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.text(
      `Relatório gerado em ${today} - Página ${i} de ${pageCount}`,
      14,
      doc.internal.pageSize.height - 10
    );
  }

  // Salvar o PDF
  doc.save(filename);
}

/**
 * Gera um relatório de estoque em PDF para todos os departamentos, com cada departamento em uma seção separada
 * @param products Lista de produtos do estoque
 * @param departments Lista de departamentos
 * @param clubInfo Informações do clube
 * @param filename Nome do arquivo PDF
 */
export async function generateInventoryReportByDepartments(
  products: InventoryProduct[],
  departments: string[],
  clubInfo: ClubInfo,
  filename: string = 'relatorio-estoque-departamentos.pdf'
): Promise<void> {
  // Criar um novo documento PDF
  const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

  // Adicionar título
  const title = 'Relatório de Estoque por Departamentos';
  doc.setFontSize(18);
  doc.text(title, 14, 22);

  // Adicionar informações do clube
  doc.setFontSize(12);
  doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, 14, 30);

  if (clubInfo.address) {
    doc.text(`Endereço: ${clubInfo.address}`, 14, 36);
  }

  if (clubInfo.phone) {
    doc.text(`Telefone: ${clubInfo.phone}`, 14, 42);
  }

  // Adicionar data do relatório
  const today = new Date().toLocaleDateString('pt-BR');
  doc.text(`Data do relatório: ${today}`, 14, 48);

  // Adicionar logo se disponível
  if (clubInfo.logo_url) {
    try {
      // Carregar a imagem e aplicar a rotação
      const img = new Image();
      img.crossOrigin = "Anonymous";

      // Criar uma promessa para carregar a imagem
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            // Criar um canvas para rotacionar a imagem
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d')!;

            // Definir as dimensões do canvas
            const size = Math.max(img.width, img.height);
            canvas.width = size;
            canvas.height = size;

            // Mover para o centro do canvas
            ctx.translate(size / 2, size / 2);

            // Rotacionar 45 graus (em radianos)
            ctx.rotate(45 * Math.PI / 180);

            // Desenhar a imagem centralizada
            ctx.drawImage(img, -img.width / 2, -img.height / 2, img.width, img.height);

            // Obter a imagem rotacionada como data URL
            const rotatedImageUrl = canvas.toDataURL('image/png');

            // Adicionar a imagem rotacionada ao PDF
            doc.addImage(rotatedImageUrl, 'PNG', 160, 15, 30, 30);

            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });

      // Iniciar o carregamento da imagem
      img.src = clubInfo.logo_url;

      // Aguardar o carregamento e processamento da imagem
      await loadImage;
    } catch (logoError) {
      console.error("Erro ao adicionar logo ao PDF:", logoError);
    }
  }

  // Posição Y inicial
  let yPosition = 60;

  // Para cada departamento, adicionar uma seção
  for (let i = 0; i < departments.length; i++) {
    const department = departments[i];

    // Filtrar produtos pelo departamento
    const departmentProducts = products.filter(product => product.department === department);

    // Ordenar produtos por nome
    const sortedProducts = [...departmentProducts].sort((a, b) => a.name.localeCompare(b.name));

    // Se não houver produtos neste departamento, pular
    if (sortedProducts.length === 0) continue;

    // Adicionar título do departamento
    if (i > 0) {
      // Verificar se há espaço suficiente para o próximo departamento
      if (yPosition > 250) {
        doc.addPage();
        yPosition = 20;
      } else {
        yPosition += 10;
      }
    }

    doc.setFontSize(14);
    doc.text(`Departamento: ${department}`, 14, yPosition);
    yPosition += 10;

    // Preparar dados para a tabela
    const tableData = sortedProducts.map(product => [
      product.name,
      `${product.quantity} ${product.unit_of_measure || 'unidade'}`,
      `${product.minimum_quantity || 0} ${product.unit_of_measure || 'unidade'}`,
      product.location || '-',
      new Date(product.registration_date).toLocaleDateString('pt-BR'),
      product.description || '-'
    ]);

    // Adicionar a tabela ao PDF
    autoTable(doc, {
      startY: yPosition,
      head: [['Nome do Produto', 'Quantidade', 'Qtd. Mínima', 'Localização', 'Data de Cadastro', 'Descrição']],
      body: tableData,
      theme: 'striped',
      headStyles: { fillColor: getClubPrimaryColorRgb() },
      didDrawPage: (data) => {
        // Adicionar cabeçalho em cada página
        if (data.pageNumber > 1) {
          doc.setFontSize(10);
          doc.text(title, 14, 10);
          doc.setFontSize(8);
          doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, 14, 15);
        }
      }
    });

    // Atualizar a posição Y para o próximo departamento
    const docWithTable = doc as jsPDFWithAutoTable;
    yPosition = docWithTable.lastAutoTable.finalY + 10;
  }

  // Adicionar rodapé com numeração de páginas
  // @ts-expect-error - A tipagem do jsPDF está incompleta
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.text(
      `Relatório gerado em ${today} - Página ${i} de ${pageCount}`,
      14,
      doc.internal.pageSize.height - 10
    );
  }

  // Salvar o PDF
  doc.save(filename);
}
