-- Etapa 5: <PERSON><PERSON><PERSON> tabela de documentos de jogadores
CREATE TABLE player_documents (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id),
  player_id UUID REFERENCES players(id),
  document_type TEXT NOT NULL,
  file_url TEXT NOT NULL,
  status TEXT DEFAULT 'pending',
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  verified_at TIMESTAMP WITH TIME ZONE,
  verified_by UUID REFERENCES users(id)
);
