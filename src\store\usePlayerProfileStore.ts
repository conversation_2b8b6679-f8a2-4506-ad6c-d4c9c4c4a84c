import { create } from "zustand";
import { Player } from "../api/api";
import { getPlayerById } from "../api/api";

interface PlayerProfileState {
  player: Player | null;
  loading: boolean;
  error: string | null;
  fetchPlayerProfile: (clubId: number, id: string) => Promise<void>;
}

export const usePlayerProfileStore = create<PlayerProfileState>((set) => ({
  player: null,
  loading: false,
  error: null,

  fetchPlayerProfile: async (clubId: number, id: string) => {
    set({ loading: true, error: null });
    try {
      const player = await getPlayerById(clubId, id);
      set({ player, loading: false });
    } catch (err: unknown) {
      const error = err as Error;
      set({ error: error.message || "Erro ao buscar perfil do jogador", loading: false });
    }
  },
}));
