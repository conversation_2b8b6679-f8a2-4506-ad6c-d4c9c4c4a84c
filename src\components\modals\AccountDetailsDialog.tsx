import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { FinancialAccount } from "@/api/api";
import { FileMinus, FilePlus, Download, ExternalLink } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

interface AccountDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  account: FinancialAccount | null;
}

export function AccountDetailsDialog({ open, onOpenChange, account }: AccountDetailsDialogProps) {
  if (!account) return null;

  // Format dates
  const formattedCreationDate = format(new Date(account.creation_date), "dd/MM/yyyy", { locale: ptBR });
  const formattedDueDate = format(new Date(account.due_date), "dd/MM/yyyy", { locale: ptBR });

  // Format amount
  const formattedAmount = `R$ ${account.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "pendente":
        return "bg-yellow-50 text-yellow-700 border-yellow-200";
      case "pago":
        return "bg-green-50 text-green-700 border-green-200";
      case "recebido":
        return "bg-primary/10 text-primary border-primary/20";
      default:
        return "bg-gray-50 text-gray-700 border-gray-200";
    }
  };

  // Get status label
  const getStatusLabel = (status: string) => {
    switch (status) {
      case "pendente":
        return "Pendente";
      case "pago":
        return "Pago";
      case "recebido":
        return "Recebido";
      default:
        return status;
    }
  };

  // Handle receipt download
  const handleDownloadReceipt = () => {
    if (account.receipt_url) {
      window.open(account.receipt_url, "_blank");
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Detalhes da Conta</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-4">
            {account.type === "a_receber" ? (
              <FilePlus className="h-5 w-5 text-blue-600" />
            ) : (
              <FileMinus className="h-5 w-5 text-red-600" />
            )}
            <h3 className="text-lg font-semibold">{account.description}</h3>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-muted-foreground">Tipo</p>
              <Badge
                variant="outline"
                className={
                  account.type === "a_receber"
                    ? "bg-blue-50 text-blue-700 border-blue-200"
                    : "bg-red-50 text-red-700 border-red-200"
                }
              >
                {account.type === "a_receber" ? "A Receber" : "A Pagar"}
              </Badge>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Status</p>
              <Badge
                variant="outline"
                className={getStatusColor(account.status)}
              >
                {getStatusLabel(account.status)}
              </Badge>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Valor</p>
              <p className={`font-medium ${account.type === "a_receber" ? "text-blue-600" : "text-red-600"}`}>
                {formattedAmount}
              </p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Vencimento</p>
              <p className="font-medium">{formattedDueDate}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Fornecedor/Cliente</p>
              <p className="font-medium">{account.supplier_client}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Data de Lançamento</p>
              <p className="font-medium">{formattedCreationDate}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Categoria</p>
              <p className="font-medium">{account.category}</p>
            </div>
            {account.cost_center && (
              <div>
                <p className="text-sm text-muted-foreground">Centro de Custo</p>
                <p className="font-medium">{account.cost_center}</p>
              </div>
            )}
          </div>

          {account.notes && (
            <div className="mt-4">
              <p className="text-sm text-muted-foreground">Observações</p>
              <p className="text-sm mt-1">{account.notes}</p>
            </div>
          )}

          {account.receipt_url && (
            <div className="mt-4">
              <p className="text-sm text-muted-foreground">Comprovante</p>
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={handleDownloadReceipt}
              >
                <Download className="h-4 w-4 mr-2" />
                Visualizar Comprovante
              </Button>
            </div>
          )}
        </div>
        <DialogFooter>
          <Button onClick={() => onOpenChange(false)}>Fechar</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
