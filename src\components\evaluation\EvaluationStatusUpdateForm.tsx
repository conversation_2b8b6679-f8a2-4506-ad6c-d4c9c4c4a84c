import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useToast } from "@/components/ui/use-toast";
import { useUser } from "@/context/UserContext";
import { updatePlayerEvaluationStatus } from "@/api/playerEvaluationInvitations";

interface EvaluationStatusUpdateFormProps {
  clubId: number;
  player: any;
  onSuccess: () => void;
  onCancel: () => void;
}

export function EvaluationStatusUpdateForm({
  clubId,
  player,
  onSuccess,
  onCancel
}: EvaluationStatusUpdateFormProps) {
  const { user } = useUser();
  const { toast } = useToast();
  
  const [status, setStatus] = useState("disponivel"); // Default to "disponivel" (approved)
  const [notes, setNotes] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user?.id) {
      setError("Usuário não autenticado");
      return;
    }
    
    if (!status) {
      setError("Selecione um status");
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      // Update player status
      await updatePlayerEvaluationStatus(
        clubId,
        player.id,
        status,
        notes,
        user.id
      );
      
      onSuccess();
    } catch (err: any) {
      console.error("Erro ao atualizar status:", err);
      setError(err.message || "Erro ao atualizar status");
      toast({
        title: "Erro",
        description: err.message || "Erro ao atualizar status",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <p className="font-medium">Jogador: {player.name}</p>
        <p className="text-sm text-muted-foreground">Posição: {player.position}</p>
      </div>
      
      <div className="space-y-2">
        <Label>Status após pré cadastro</Label>
        <RadioGroup value={status} onValueChange={setStatus} className="space-y-2">
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="disponivel" id="disponivel" />
            <Label htmlFor="disponivel" className="cursor-pointer">
              Aprovado (Disponível)
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="inativo" id="inativo" />
            <Label htmlFor="inativo" className="cursor-pointer">
              Reprovado (Inativo)
            </Label>
          </div>
        </RadioGroup>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="notes">Observações</Label>
        <Textarea
          id="notes"
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
          placeholder="Observações sobre o pré cadastro do atleta"
          rows={4}
        />
      </div>
      
      {error && (
        <div className="text-sm font-medium text-destructive">{error}</div>
      )}
      
      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancelar
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? "Salvando..." : "Salvar Status"}
        </Button>
      </div>
    </form>
  );
}
