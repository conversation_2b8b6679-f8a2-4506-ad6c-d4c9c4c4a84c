import { useEffect, useState } from "react";
import { usePermissionsStore } from "@/store/usePermissionsStore";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";

/**
 * Hook para verificar permissões do usuário atual
 */
export function usePermission() {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const [initialized, setInitialized] = useState(false);

  const {
    fetchUserPermissions,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    isLoaded,
    role,
    permissions,
    loading,
    error
  } = usePermissionsStore();

  // Carregar permissões do usuário quando o componente for montado
  useEffect(() => {
    if (user?.id && clubId && !isLoaded && !initialized) {
      fetchUserPermissions(clubId, user.id);
      setInitialized(true);
    }
  }, [user?.id, clubId, fetchUserPermissions, isLoaded, initialized]);

  /**
   * Verifica se o usuário atual tem uma permissão específica
   * @param permission Permissão a ser verificada
   * @returns true se o usuário tem a permissão
   */
  const checkPermission = async (permission: string): Promise<boolean> => {
    if (!user?.id || !clubId) return false;
    return hasPermission(clubId, user.id, permission);
  };

  /**
   * Verifica se o usuário atual tem pelo menos uma das permissões especificadas
   * @param permissions Lista de permissões a serem verificadas
   * @returns true se o usuário tem pelo menos uma das permissões
   */
  const checkAnyPermission = async (permissionList: string[]): Promise<boolean> => {
    if (!user?.id || !clubId) return false;
    return hasAnyPermission(clubId, user.id, permissionList);
  };

  /**
   * Verifica se o usuário atual tem todas as permissões especificadas
   * @param permissions Lista de permissões a serem verificadas
   * @returns true se o usuário tem todas as permissões
   */
  const checkAllPermissions = async (permissionList: string[]): Promise<boolean> => {
    if (!user?.id || !clubId) return false;
    return hasAllPermissions(clubId, user.id, permissionList);
  };

  /**
   * Versão síncrona para verificar permissão (usa cache)
   * Útil para renderização condicional em componentes
   * @param permission Permissão a ser verificada
   * @param departmentId ID do departamento (opcional, para permissões específicas de departamento)
   * @returns true se o usuário tem a permissão (ou é admin/president)
   */
  const can = (permission: string, departmentId?: number): boolean => {
    if (!isLoaded || !user?.id) return false;

    try {
      // Jogadores têm acesso muito restrito
      if (role === "player") {
        // Jogadores só podem acessar seu próprio perfil e dashboard
        const playerPermissions = ["dashboard.view", "players.view_own", "players.edit_own", "players.evaluation.view"];
        return playerPermissions.includes(permission);
      }

      // Admin e president têm tratamento especial
      if (role === "president") return true;
      if (role === "admin" && !permission.startsWith("president.")) return true;

      // Se um departamento específico foi fornecido, verificar permissões desse departamento
      if (departmentId && permissions.departments) {
        try {
          // Verificar se departments é um array
          if (Array.isArray(permissions.departments)) {
            const departments = permissions.departments as any[];
            const department = departments.find(d => d.id === departmentId);
            if (department && department.permissions && department.permissions[permission]) {
              return true;
            }
          }
        } catch (error) {
          console.warn("Erro ao verificar permissões de departamento:", error);
        }
      }

      // Verificar permissão específica nas permissões gerais
      return !!permissions[permission];
    } catch (error) {
      console.warn("Erro ao verificar permissão:", error);
      return false;
    }
  };

  /**
   * Versão síncrona para verificar se tem qualquer uma das permissões
   * @param permissionList Lista de permissões
   * @returns true se o usuário tem pelo menos uma das permissões
   */
  const canAny = (permissionList: string[]): boolean => {
    if (!isLoaded || !user?.id) return false;

    // Jogadores têm acesso muito restrito
    if (role === "player") {
      // Jogadores só podem acessar seu próprio perfil e dashboard
      const playerPermissions = ["dashboard.view", "players.view_own", "players.edit_own", "players.evaluation.view"];
      return permissionList.some(p => playerPermissions.includes(p));
    }

    // Admin e president têm tratamento especial
    if (role === "president") return true;
    if (role === "admin" && !permissionList.some(p => p.startsWith("president."))) return true;

    // Verificar se tem pelo menos uma das permissões
    return permissionList.some(p => !!permissions[p]);
  };

  /**
   * Versão síncrona para verificar se tem todas as permissões
   * @param permissionList Lista de permissões
   * @returns true se o usuário tem todas as permissões
   */
  const canAll = (permissionList: string[]): boolean => {
    if (!isLoaded || !user?.id) return false;

    // Jogadores têm acesso muito restrito
    if (role === "player") {
      // Jogadores só podem acessar seu próprio perfil e dashboard
      const playerPermissions = ["dashboard.view", "players.view_own", "players.edit_own", "players.evaluation.view"];
      return permissionList.every(p => playerPermissions.includes(p));
    }

    // Admin e president têm tratamento especial
    if (role === "president") return true;
    if (role === "admin" && !permissionList.some(p => p.startsWith("president."))) return true;

    // Verificar se tem todas as permissões
    return permissionList.every(p => !!permissions[p]);
  };

  return {
    // Estado
    isLoaded,
    role,
    permissions,
    loading,
    error,

    // Métodos assíncronos (para operações)
    checkPermission,
    checkAnyPermission,
    checkAllPermissions,

    // Métodos síncronos (para renderização condicional)
    can,
    canAny,
    canAll
  };
}
