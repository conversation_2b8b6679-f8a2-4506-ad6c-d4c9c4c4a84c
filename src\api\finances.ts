import { supabase } from "@/integrations/supabase/client";

export type PlayerFinancialEntry = {
  month: number;
  year: number;
  amount: number;
  description: string;
};

export type PlayerBankingInfo = {
  bank_name?: string;
  bank_account_number?: string;
  bank_branch?: string;
  bank_account_type?: string;
  bank_pix_key?: string;
};

export type PlayerFinancialData = {
  [key: string]: PlayerFinancialEntry[]; // Chave no formato "YYYY-MM"
};

/**
 * Obtém os dados financeiros de um jogador
 * @param clubId ID do clube
 * @param playerId ID do jogador
 * @returns Dados financeiros do jogador
 */
export async function getPlayerFinancialData(
  clubId: number,
  playerId: string
): Promise<PlayerFinancialData> {
  try {
    const { data: player, error } = await supabase
      .from("players")
      .select("financial_data")
      .eq("club_id", clubId)
      .eq("id", playerId)
      .single();

    if (error) {
      throw new Error(`Erro ao obter dados financeiros: ${error.message}`);
    }

    return player.financial_data || {};
  } catch (error: any) {
    console.error("Erro ao obter dados financeiros:", error);
    throw new Error(error.message || "Erro ao obter dados financeiros");
  }
}

/**
 * Adiciona uma entrada financeira para um jogador
 * @param clubId ID do clube
 * @param playerId ID do jogador
 * @param month Mês (1-12)
 * @param year Ano
 * @param amount Valor (positivo para receita, negativo para despesa)
 * @param description Descrição
 * @param category Categoria da transação (opcional, padrão: "salários")
 * @returns Dados financeiros atualizados
 */
export async function addPlayerFinancialEntry(
  clubId: number,
  playerId: string,
  month: number,
  year: number,
  amount: number,
  description: string,
  category: string = "salários"
): Promise<PlayerFinancialData> {
  try {
    // Validar mês
    if (month < 1 || month > 12) {
      throw new Error("Mês inválido. Deve ser um número entre 1 e 12.");
    }

    // Obter dados financeiros atuais
    const financialData = await getPlayerFinancialData(clubId, playerId);

    // Chave para o mês e ano
    const key = `${year}-${month.toString().padStart(2, "0")}`;

    // Adicionar nova entrada
    const entries = financialData[key] || [];
    entries.push({
      month,
      year,
      amount,
      description,
    });

    // Atualizar dados financeiros
    const updatedData = {
      ...financialData,
      [key]: entries,
    };

    // Obter nome do jogador para a descrição da transação financeira
    const { data: playerData, error: playerError } = await supabase
      .from("players")
      .select("name")
      .eq("club_id", clubId)
      .eq("id", playerId)
      .single();

    if (playerError) {
      console.error("Erro ao buscar nome do jogador:", playerError);
      throw new Error(`Erro ao buscar nome do jogador: ${playerError.message}`);
    }

    const playerName = playerData.name;

    // Criar uma transação financeira geral
    // Usar o primeiro dia do mês especificado para a transação
    const transactionDate = new Date(year, month - 1, 1).toISOString().split('T')[0];
    const transactionType = amount < 0 ? "despesa" : "receita";
    const transactionCategory = category;
    const transactionDescription = `${description} - ${playerName}`;

    console.log("Criando transação financeira:", {
      date: transactionDate,
      type: transactionType,
      category: transactionCategory,
      amount: Math.abs(amount),
      description: transactionDescription,
      player_id: playerId
    });

    try {
      // Inserir na tabela financial_transactions
      const { error: transactionError } = await supabase
        .from("financial_transactions")
        .insert({
          club_id: clubId,
          date: transactionDate,
          type: transactionType,
          category: transactionCategory,
          amount: Math.abs(amount), // Valor absoluto, pois o tipo já indica se é receita ou despesa
          description: transactionDescription,
          player_id: playerId // Adicionar referência ao jogador
        });

      if (transactionError) {
        // Se houver erro com o campo player_id, tentar novamente sem ele
        if (transactionError.message.includes('player_id')) {
          console.warn("Campo player_id não disponível, tentando sem ele");
          const { error: fallbackError } = await supabase
            .from("financial_transactions")
            .insert({
              club_id: clubId,
              date: transactionDate,
              type: transactionType,
              category: transactionCategory,
              amount: Math.abs(amount),
              description: transactionDescription
            });

          if (fallbackError) {
            console.error("Erro ao criar transação financeira (fallback):", fallbackError);
          }
        } else {
          console.error("Erro ao criar transação financeira:", transactionError);
        }
      }
    } catch (err) {
      console.error("Exceção ao criar transação financeira:", err);
    }

    // O erro já foi tratado acima

    // Salvar no banco de dados
    const { data, error } = await supabase
      .from("players")
      .update({
        financial_data: updatedData,
      })
      .eq("club_id", clubId)
      .eq("id", playerId)
      .select("financial_data")
      .single();

    if (error) {
      throw new Error(`Erro ao adicionar entrada financeira: ${error.message}`);
    }

    return data.financial_data;
  } catch (error: any) {
    console.error("Erro ao adicionar entrada financeira:", error);
    throw new Error(error.message || "Erro ao adicionar entrada financeira");
  }
}

/**
 * Remove uma entrada financeira de um jogador
 * @param clubId ID do clube
 * @param playerId ID do jogador
 * @param month Mês (1-12)
 * @param year Ano
 * @param index Índice da entrada a ser removida
 * @returns Dados financeiros atualizados
 */
export async function removePlayerFinancialEntry(
  clubId: number,
  playerId: string,
  month: number,
  year: number,
  index: number
): Promise<PlayerFinancialData> {
  try {
    // Obter dados financeiros atuais
    const financialData = await getPlayerFinancialData(clubId, playerId);

    // Chave para o mês e ano
    const key = `${year}-${month.toString().padStart(2, "0")}`;

    // Verificar se existem entradas para o mês e ano
    if (!financialData[key] || !financialData[key][index]) {
      throw new Error("Entrada financeira não encontrada");
    }

    // Obter a entrada que será removida
    const entryToRemove = financialData[key][index];

    // Tentar encontrar e remover a transação financeira correspondente
    try {
      // Buscar transações financeiras do jogador no mesmo mês/ano
      const startDate = new Date(year, month - 1, 1).toISOString().split('T')[0];
      const endDate = new Date(year, month, 0).toISOString().split('T')[0];

      const { data: transactions } = await supabase
        .from("financial_transactions")
        .select("id, description, amount")
        .eq("club_id", clubId)
        .eq("player_id", playerId)
        .gte("date", startDate)
        .lte("date", endDate);

      if (transactions && transactions.length > 0) {
        // Tentar encontrar uma transação com valor e descrição semelhantes
        const transaction = transactions.find(t => {
          const transactionAmount = Math.abs(typeof t.amount === 'string' ? parseFloat(t.amount) : t.amount);
          const entryAmount = Math.abs(entryToRemove.amount);
          return Math.abs(transactionAmount - entryAmount) < 0.01; // Comparação com tolerância para evitar problemas de arredondamento
        });

        if (transaction) {
          // Remover a transação financeira
          await supabase
            .from("financial_transactions")
            .delete()
            .eq("id", transaction.id);
        }
      }
    } catch (err) {
      console.error("Erro ao remover transação financeira:", err);
      // Não interromper o fluxo, apenas logar o erro
    }

    // Remover entrada
    const entries = [...financialData[key]];
    entries.splice(index, 1);

    // Atualizar dados financeiros
    const updatedData = {
      ...financialData,
      [key]: entries,
    };

    // Se não houver mais entradas para o mês, remover a chave
    if (entries.length === 0) {
      delete updatedData[key];
    }

    // Salvar no banco de dados
    const { data, error } = await supabase
      .from("players")
      .update({
        financial_data: updatedData,
      })
      .eq("club_id", clubId)
      .eq("id", playerId)
      .select("financial_data")
      .single();

    if (error) {
      throw new Error(`Erro ao remover entrada financeira: ${error.message}`);
    }

    return data.financial_data;
  } catch (error: any) {
    console.error("Erro ao remover entrada financeira:", error);
    throw new Error(error.message || "Erro ao remover entrada financeira");
  }
}

/**
 * Calcula o saldo financeiro de um jogador para um mês específico
 * @param financialData Dados financeiros do jogador
 * @param month Mês (1-12)
 * @param year Ano
 * @returns Saldo do mês
 */
export function calculateMonthlyBalance(
  financialData: PlayerFinancialData,
  month: number,
  year: number
): number {
  const key = `${year}-${month.toString().padStart(2, "0")}`;
  const entries = financialData[key] || [];

  return entries.reduce((total, entry) => total + entry.amount, 0);
}

/**
 * Calcula o saldo financeiro total de um jogador
 * @param financialData Dados financeiros do jogador
 * @returns Saldo total
 */
export function calculateTotalBalance(financialData: PlayerFinancialData): number {
  let total = 0;

  for (const key in financialData) {
    const entries = financialData[key];
    total += entries.reduce((sum, entry) => sum + entry.amount, 0);
  }

  return total;
}

/**
 * Obtém os dados financeiros de um jogador para um ano específico
 * @param financialData Dados financeiros do jogador
 * @param year Ano
 * @returns Dados financeiros do ano
 */
export function getYearlyFinancialData(
  financialData: PlayerFinancialData,
  year: number
): Record<number, PlayerFinancialEntry[]> {
  const yearlyData: Record<number, PlayerFinancialEntry[]> = {};

  // Inicializar todos os meses
  for (let month = 1; month <= 12; month++) {
    yearlyData[month] = [];
  }

  // Preencher com os dados existentes
  for (const key in financialData) {
    const [entryYear, entryMonth] = key.split("-").map(Number);

    if (entryYear === year) {
      yearlyData[entryMonth] = financialData[key];
    }
  }

  return yearlyData;
}

/**
 * Obtém as informações bancárias de um jogador
 * @param clubId ID do clube
 * @param playerId ID do jogador
 * @returns Informações bancárias do jogador
 */
export async function getPlayerBankingInfo(
  clubId: number,
  playerId: string
): Promise<PlayerBankingInfo> {
  try {
    const { data: player, error } = await supabase
      .from("players")
      .select("bank_name, bank_account_number, bank_branch, bank_account_type, bank_pix_key")
      .eq("club_id", clubId)
      .eq("id", playerId)
      .single();

    if (error) {
      throw new Error(`Erro ao obter informações bancárias: ${error.message}`);
    }

    return {
      bank_name: player.bank_name || "",
      bank_account_number: player.bank_account_number || "",
      bank_branch: player.bank_branch || "",
      bank_account_type: player.bank_account_type || "",
      bank_pix_key: player.bank_pix_key || ""
    };
  } catch (error: any) {
    console.error("Erro ao obter informações bancárias:", error);
    throw new Error(error.message || "Erro ao obter informações bancárias");
  }
}

/**
 * Atualiza as informações bancárias de um jogador
 * @param clubId ID do clube
 * @param playerId ID do jogador
 * @param bankingInfo Informações bancárias do jogador
 * @returns Informações bancárias atualizadas
 */
export async function updatePlayerBankingInfo(
  clubId: number,
  playerId: string,
  bankingInfo: PlayerBankingInfo
): Promise<PlayerBankingInfo> {
  try {
    const { data, error } = await supabase
      .from("players")
      .update({
        bank_name: bankingInfo.bank_name,
        bank_account_number: bankingInfo.bank_account_number,
        bank_branch: bankingInfo.bank_branch,
        bank_account_type: bankingInfo.bank_account_type,
        bank_pix_key: bankingInfo.bank_pix_key
      })
      .eq("club_id", clubId)
      .eq("id", playerId)
      .select("bank_name, bank_account_number, bank_branch, bank_account_type, bank_pix_key")
      .single();

    if (error) {
      throw new Error(`Erro ao atualizar informações bancárias: ${error.message}`);
    }

    return {
      bank_name: data.bank_name || "",
      bank_account_number: data.bank_account_number || "",
      bank_branch: data.bank_branch || "",
      bank_account_type: data.bank_account_type || "",
      bank_pix_key: data.bank_pix_key || ""
    };
  } catch (error: any) {
    console.error("Erro ao atualizar informações bancárias:", error);
    throw new Error(error.message || "Erro ao atualizar informações bancárias");
  }
}
