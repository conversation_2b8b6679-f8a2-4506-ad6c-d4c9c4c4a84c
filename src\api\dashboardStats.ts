import { supabase } from "@/integrations/supabase/client";

// Types for dashboard statistics
export interface DashboardStats {
  totalActivePlayers: number;
  playersByCategory: CategoryPlayerCount[];
  playersInRehabilitation: number;
  averageFitness: number;
}

export interface CategoryPlayerCount {
  categoryId: number;
  categoryName: string;
  playerCount: number;
}

/**
 * Get total count of active players for a club
 * @param clubId Club ID
 * @returns Number of active players
 */
export async function getTotalActivePlayers(clubId: number): Promise<number> {
  const { data, error } = await supabase
    .from("players")
    .select("id", { count: "exact" })
    .eq("club_id", clubId)
    .neq("status", "inativo");

  if (error) {
    console.error("Error fetching total active players:", error);
    throw new Error(`Error fetching total active players: ${error.message}`);
  }

  return data?.length || 0;
}

/**
 * Get count of players by category for a club
 * @param clubId Club ID
 * @returns Array of category player counts
 */
export async function getPlayersByCategory(clubId: number): Promise<CategoryPlayerCount[]> {
  const { data, error } = await supabase
    .from("categories")
    .select(`
      id,
      name,
      player_categories!inner(
        player_id,
        players!inner(
          id,
          status
        )
      )
    `)
    .eq("club_id", clubId);

  if (error) {
    console.error("Error fetching players by category:", error);
    throw new Error(`Error fetching players by category: ${error.message}`);
  }

  // Process the data to count active players per category
  const categoryStats: CategoryPlayerCount[] = [];

  data?.forEach((category) => {
    const activePlayers = category.player_categories.filter(
      (pc: any) => pc.players.status !== "inativo"
    );

    categoryStats.push({
      categoryId: category.id,
      categoryName: category.name,
      playerCount: activePlayers.length,
    });
  });

  return categoryStats.sort((a, b) => a.categoryName.localeCompare(b.categoryName));
}

/**
 * Get count of players currently in rehabilitation
 * @param clubId Club ID
 * @returns Number of players in rehabilitation
 */
export async function getPlayersInRehabilitation(clubId: number): Promise<number> {
  const { data, error } = await supabase
    .from("rehab_sessions")
    .select("player_id")
    .eq("club_id", clubId)
    .in("status", ["Agendada", "Em andamento"])
    .or("archived.is.null,archived.eq.false");

  if (error) {
    console.error("Error fetching players in rehabilitation:", error);
    throw new Error(`Error fetching players in rehabilitation: ${error.message}`);
  }

  // Count unique players (a player might have multiple sessions)
  const uniquePlayerIds = new Set(data?.map(session => session.player_id) || []);
  return uniquePlayerIds.size;
}

/**
 * Get average fitness level for all active players
 * Note: Currently returns 0 as the system doesn't store fitness data
 * @param clubId Club ID
 * @returns Average fitness percentage (always 0)
 */
export async function getAverageFitness(clubId: number): Promise<number> {
  // The system doesn't currently store fitness data in a usable format
  // The stats.minutes field is not being used for fitness tracking
  return 0;
}

/**
 * Get all dashboard statistics at once
 * @param clubId Club ID
 * @returns Complete dashboard statistics
 */
export async function getDashboardStats(clubId: number): Promise<DashboardStats> {
  try {
    const [
      totalActivePlayers,
      playersByCategory,
      playersInRehabilitation,
      averageFitness,
    ] = await Promise.all([
      getTotalActivePlayers(clubId),
      getPlayersByCategory(clubId),
      getPlayersInRehabilitation(clubId),
      getAverageFitness(clubId),
    ]);

    return {
      totalActivePlayers,
      playersByCategory,
      playersInRehabilitation,
      averageFitness,
    };
  } catch (error) {
    console.error("Error fetching dashboard stats:", error);
    throw error;
  }
}
