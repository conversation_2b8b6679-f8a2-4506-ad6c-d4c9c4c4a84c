import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "@/components/ui/use-toast";
import { Plus, Edit, Trash2, Users } from "lucide-react";
import { useCurrentClubId } from "@/context/ClubContext";
import { getDepartments, Department } from "@/api/api";

interface DepartmentListProps {
  onAddDepartment: () => void;
  onEditDepartment: (department: Department) => void;
  onDeleteDepartment: (department: Department) => void;
  onViewUsers: (department: Department) => void;
}

export function DepartmentList({
  onAddDepartment,
  onEditDepartment,
  onDeleteDepartment,
  onViewUsers,
}: DepartmentListProps) {
  const clubId = useCurrentClubId();
  const [departments, setDepartments] = useState<Department[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");

  // Carregar departamentos
  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        setLoading(true);
        const data = await getDepartments(clubId);
        setDepartments(data);
      } catch (err: any) {
        console.error("Erro ao carregar departamentos:", err);
        toast({
          title: "Erro",
          description: "Não foi possível carregar os departamentos",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDepartments();
  }, [clubId]);

  // Filtrar departamentos com base na pesquisa
  const filteredDepartments = departments.filter((department) =>
    department.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (department.description || "").toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Departamentos</CardTitle>
        <Button onClick={onAddDepartment}>
          <Plus className="h-4 w-4 mr-1" />
          Novo Departamento
        </Button>
      </CardHeader>
      <CardContent>
        <div className="mb-4">
          <Input
            placeholder="Buscar departamento..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        ) : filteredDepartments.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            {searchQuery
              ? "Nenhum departamento encontrado para a pesquisa"
              : "Nenhum departamento cadastrado"}
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Nome</TableHead>
                <TableHead className="hidden md:table-cell">Descrição</TableHead>
                <TableHead className="w-[120px] text-right">Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredDepartments.map((department) => (
                <TableRow key={department.id}>
                  <TableCell className="font-medium">{department.name}</TableCell>
                  <TableCell className="hidden md:table-cell">
                    {department.description || "Sem descrição"}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onViewUsers(department)}
                        title="Ver usuários"
                      >
                        <Users className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onEditDepartment(department)}
                        title="Editar"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onDeleteDepartment(department)}
                        title="Excluir"
                        className="text-red-500 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
}
