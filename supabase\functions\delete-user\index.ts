// supabase/functions/delete-user/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Verificar autorização
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Não autorizado' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Criar cliente Supabase com chave de serviço
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      { auth: { autoRefreshToken: false, persistSession: false } }
    )

    // Obter dados do corpo da requisição
    const { userId } = await req.json()

    // Verificar campos obrigatórios
    if (!userId) {
      return new Response(
        JSON.stringify({ error: 'ID do usuário é obrigatório' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Verificar se o usuário existe
    const { data: userData, error: userError } = await supabaseAdmin
      .from("users")
      .select("id")
      .eq("id", userId)
      .single();

    if (userError) {
      return new Response(
        JSON.stringify({ error: `Usuário não encontrado: ${userError.message}` }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Primeiro, remover todas as referências ao usuário nas tabelas relacionadas
    // Isso é necessário para evitar erros de chave estrangeira

    // 1. Remover da tabela club_members
    await supabaseAdmin
      .from("club_members")
      .delete()
      .eq("user_id", userId);

    // 2. Remover da tabela user_departments (se existir)
    try {
      await supabaseAdmin
        .from("user_departments")
        .delete()
        .eq("user_id", userId);
    } catch (e) {
      // Ignorar erro se a tabela não existir
    }

    // 3. Remover da tabela player_accounts (se existir)
    try {
      await supabaseAdmin
        .from("player_accounts")
        .delete()
        .eq("user_id", userId);
    } catch (e) {
      // Ignorar erro se a tabela não existir
    }

    // 4. Atualizar a tabela players para remover a referência ao usuário (se existir)
    try {
      await supabaseAdmin
        .from("players")
        .update({ user_id: null })
        .eq("user_id", userId);
    } catch (e) {
      // Ignorar erro se a tabela não existir
    }

    // 5. Atualizar a tabela medical_professionals para remover a referência ao usuário (se existir)
    try {
      await supabaseAdmin
        .from("medical_professionals")
        .update({ user_id: null })
        .eq("user_id", userId);
    } catch (e) {
      // Ignorar erro se a tabela não existir
    }

    // 6. Remover da tabela users
    const { error: deleteUserError } = await supabaseAdmin
      .from("users")
      .delete()
      .eq("id", userId);

    if (deleteUserError) {
      return new Response(
        JSON.stringify({ error: `Erro ao excluir usuário da tabela users: ${deleteUserError.message}` }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Finalmente, excluir o usuário do Supabase Auth
    const { error: authDeleteError } = await supabaseAdmin.auth.admin.deleteUser(userId);

    if (authDeleteError) {
      return new Response(
        JSON.stringify({ error: `Erro ao excluir usuário do Auth: ${authDeleteError.message}` }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Retornar sucesso
    return new Response(
      JSON.stringify({
        success: true,
        message: "Usuário excluído com sucesso"
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
