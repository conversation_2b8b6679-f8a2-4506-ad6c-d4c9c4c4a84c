import { supabase } from "@/integrations/supabase/client";
import { withPermission, withAuditLog } from "./middleware";
import { jsPDF } from "jspdf";
import autoTable from 'jspdf-autotable';

// Types
export type AdministrativeDocument = {
  id: number;
  club_id: number;
  document_number: number;
  title: string;
  content: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  digital_signature: boolean;
  signed_by?: string;
  signed_at?: string;
  signature_url?: string;
  document_type: 'oficio' | 'memorando';
  creator_name?: string; // For joins
  signer_name?: string; // For joins
};

export type AdministrativeTask = {
  id: number;
  club_id: number;
  title: string;
  description?: string;
  responsible?: string;
  collaborator_id?: number;
  due_date?: string;
  status: 'a_fazer' | 'em_andamento' | 'concluido';
  created_by: string;
  created_at: string;
  updated_at: string;
  responsible_name?: string; // For joins
  creator_name?: string; // For joins
  collaborator_name?: string; // For joins
};

export type AdministrativeReminder = {
  id: number;
  club_id: number;
  title: string;
  description?: string;
  reminder_date: string;
  completed: boolean;
  created_by: string;
  created_at: string;
  updated_at: string;
  reminder_type: 'activity' | 'document' | 'email' | 'meeting';
  creator_name?: string; // For joins
};

// Document Functions
export async function getAdministrativeDocuments(clubId: number): Promise<AdministrativeDocument[]> {
  const { data, error } = await supabase
    .from("administrative_documents_view")
    .select("*")
    .eq("club_id", clubId)
    .order("created_at", { ascending: false });

  if (error) {
    console.error("Error fetching administrative documents:", error);
    throw new Error(`Error fetching administrative documents: ${error.message}`);
  }

  return data;
}

export async function getAdministrativeDocumentById(clubId: number, id: number): Promise<AdministrativeDocument> {
  const { data, error } = await supabase
    .from("administrative_documents_view")
    .select("*")
    .eq("club_id", clubId)
    .eq("id", id)
    .single();

  if (error) {
    console.error("Error fetching administrative document:", error);
    throw new Error(`Error fetching administrative document: ${error.message}`);
  }

  return data;
}

export async function createAdministrativeDocument(
  clubId: number,
  document: Omit<AdministrativeDocument, "id" | "created_at" | "updated_at" | "document_number">
): Promise<AdministrativeDocument> {
  const { data, error } = await supabase
    .from("administrative_documents")
    .insert({
      club_id: clubId,
      title: document.title,
      content: document.content,
      created_by: document.created_by,
      digital_signature: document.digital_signature || false,
      document_type: document.document_type
    })
    .select()
    .single();

  if (error) {
    console.error("Error creating administrative document:", error);
    throw new Error(`Error creating administrative document: ${error.message}`);
  }

  return data;
};

export async function updateAdministrativeDocument(
  clubId: number,
  id: number,
  updates: Partial<AdministrativeDocument>
): Promise<AdministrativeDocument> {
  const { data, error } = await supabase
    .from("administrative_documents")
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error("Error updating administrative document:", error);
    throw new Error(`Error updating administrative document: ${error.message}`);
  }

  return data;
};

export async function deleteAdministrativeDocument(
  clubId: number,
  id: number
): Promise<boolean> {
  const { error } = await supabase
    .from("administrative_documents")
    .delete()
    .eq("club_id", clubId)
    .eq("id", id);

  if (error) {
    console.error("Error deleting administrative document:", error);
    throw new Error(`Error deleting administrative document: ${error.message}`);
  }

  return true;
};

export async function signAdministrativeDocument(
  clubId: number,
  id: number,
  userId: string,
  signatureUrl?: string
): Promise<AdministrativeDocument> {
  const { data, error } = await supabase
    .from("administrative_documents")
    .update({
      digital_signature: true,
      signed_by: userId,
      signed_at: new Date().toISOString(),
      signature_url: signatureUrl,
      updated_at: new Date().toISOString()
    })
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error("Error signing administrative document:", error);
    throw new Error(`Error signing administrative document: ${error.message}`);
  }

  return data;
};

// Task Functions
export async function getAdministrativeTasks(clubId: number): Promise<AdministrativeTask[]> {
  const { data, error } = await supabase
    .from("administrative_tasks_view")
    .select("*")
    .eq("club_id", clubId)
    .order("created_at", { ascending: false });

  if (error) {
    console.error("Error fetching administrative tasks:", error);
    throw new Error(`Error fetching administrative tasks: ${error.message}`);
  }

  return data;
}

export async function createAdministrativeTask(
  clubId: number,
  task: Omit<AdministrativeTask, "id" | "created_at" | "updated_at">
): Promise<AdministrativeTask> {
  const { data, error } = await supabase
    .from("administrative_tasks")
    .insert({
      club_id: clubId,
      title: task.title,
      description: task.description,
      responsible: task.responsible,
      collaborator_id: task.collaborator_id,
      due_date: task.due_date,
      status: task.status || 'a_fazer',
      created_by: task.created_by
    })
    .select()
    .single();

  if (error) {
    console.error("Error creating administrative task:", error);
    throw new Error(`Error creating administrative task: ${error.message}`);
  }

  return data;
};

export async function updateAdministrativeTask(
  clubId: number,
  id: number,
  updates: Partial<AdministrativeTask>
): Promise<AdministrativeTask> {
  const { data, error } = await supabase
    .from("administrative_tasks")
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error("Error updating administrative task:", error);
    throw new Error(`Error updating administrative task: ${error.message}`);
  }

  return data;
};

export async function deleteAdministrativeTask(
  clubId: number,
  id: number
): Promise<boolean> {
  const { error } = await supabase
    .from("administrative_tasks")
    .delete()
    .eq("club_id", clubId)
    .eq("id", id);

  if (error) {
    console.error("Error deleting administrative task:", error);
    throw new Error(`Error deleting administrative task: ${error.message}`);
  }

  return true;
};

// Reminder Functions
export async function getAdministrativeReminders(clubId: number): Promise<AdministrativeReminder[]> {
  const { data, error } = await supabase
    .from("administrative_reminders_view")
    .select("*")
    .eq("club_id", clubId)
    .order("reminder_date", { ascending: true });

  if (error) {
    console.error("Error fetching administrative reminders:", error);
    throw new Error(`Error fetching administrative reminders: ${error.message}`);
  }

  return data;
}

export async function createAdministrativeReminder(
  clubId: number,
  reminder: Omit<AdministrativeReminder, "id" | "created_at" | "updated_at">
): Promise<AdministrativeReminder> {
  const { data, error } = await supabase
    .from("administrative_reminders")
    .insert({
      club_id: clubId,
      title: reminder.title,
      description: reminder.description,
      reminder_date: reminder.reminder_date,
      completed: reminder.completed || false,
      created_by: reminder.created_by,
      reminder_type: reminder.reminder_type
    })
    .select()
    .single();

  if (error) {
    console.error("Error creating administrative reminder:", error);
    throw new Error(`Error creating administrative reminder: ${error.message}`);
  }

  return data;
};

export async function updateAdministrativeReminder(
  clubId: number,
  id: number,
  updates: Partial<AdministrativeReminder>
): Promise<AdministrativeReminder> {
  const { data, error } = await supabase
    .from("administrative_reminders")
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error("Error updating administrative reminder:", error);
    throw new Error(`Error updating administrative reminder: ${error.message}`);
  }

  return data;
};

export async function deleteAdministrativeReminder(
  clubId: number,
  id: number
): Promise<boolean> {
  const { error } = await supabase
    .from("administrative_reminders")
    .delete()
    .eq("club_id", clubId)
    .eq("id", id);

  if (error) {
    console.error("Error deleting administrative reminder:", error);
    throw new Error(`Error deleting administrative reminder: ${error.message}`);
  }

  return true;
};

// PDF Generation
export async function generateDocumentPDF(document: AdministrativeDocument, clubInfo: any): Promise<Blob> {
  return new Promise(async (resolve, reject) => {
    const doc = new jsPDF();
    let initialY = 10; // Initial Y position

    // Função auxiliar para adicionar cabeçalho de texto e conteúdo principal
    const addHeaderTextAndMainContent = () => {
      doc.setFontSize(18);
      doc.text(clubInfo.name || 'Clube', 105, initialY > 30 ? initialY -10 : 20, { align: 'center' });
      initialY = initialY > 30 ? initialY : 30; // Ajusta Y se o logo tiver empurrado para baixo

      doc.setFontSize(14);
      doc.text(`${document.document_type === 'oficio' ? 'OFÍCIO' : 'MEMORANDO'} Nº ${document.document_number}`, 105, initialY, { align: 'center' });
      initialY += 10;

      doc.setFontSize(12);
      const createdDate = new Date(document.created_at).toLocaleDateString('pt-BR');
      doc.text(`Data: ${createdDate}`, 15, initialY);
      initialY += 10;

      doc.setFontSize(14);
      doc.text(document.title, 15, initialY, { maxWidth: 180 });
      const titleLines = doc.splitTextToSize(document.title, 180);
      initialY += (titleLines.length * 7) + 10;

      if (initialY > doc.internal.pageSize.height - 40) {
        doc.addPage();
        initialY = 20;
      }

      const htmlContent = `
        <div style="font-family: Arial, sans-serif; font-size: 10pt; line-height: 1.4; color: #333;">
          ${document.content}
        </div>
      `;

      doc.html(htmlContent, {
        callback: function (docInstance) {
          let currentY = docInstance.internal.getCurrentPageInfo().pageContext.tmp_y || initialY;

          if (document.digital_signature && document.signed_by) {
            let signatureY = currentY + 20;
            if (docInstance.internal.pageSize.height - signatureY < 70) {
              docInstance.addPage();
              signatureY = 30;
            }

            if (document.signature_url) {
              try {
                // Tenta adicionar a imagem da assinatura diretamente pela URL
                // NOTA: Isso pode não funcionar no servidor para URLs http/https.
                // Se não funcionar, a URL precisará ser buscada e convertida para base64 ANTES de chamar addImage.
                docInstance.addImage(document.signature_url, 'PNG', (docInstance.internal.pageSize.width / 2) - 30, signatureY, 60, 30); // Largura e altura fixas, ajuste conforme necessário
                signatureY += 30 + 5; // Altura da imagem + espaço
              } catch (error) {
                console.error("Error adding signature image to PDF (direct URL attempt):", error);
                // Fallback para texto se a imagem falhar
              }
            }
            // Texto da assinatura (sempre adicionado, mesmo se a imagem falhar ou não existir)
            docInstance.setFontSize(10);
            docInstance.text('Documento assinado digitalmente por:', docInstance.internal.pageSize.width / 2, signatureY, { align: 'center' });
            signatureY += 5;
            docInstance.text(document.signer_name || 'Usuário', docInstance.internal.pageSize.width / 2, signatureY, { align: 'center' });
            signatureY += 5;
            docInstance.text(`Data: ${new Date(document.signed_at || '').toLocaleDateString('pt-BR')}`, docInstance.internal.pageSize.width / 2, signatureY, { align: 'center' });
            resolve(docInstance.output('blob'));
          } else {
            resolve(docInstance.output('blob'));
          }
        },
        x: 15,
        y: initialY,
        width: 180,
        windowWidth: 650,
        autoPaging: 'text'
      });
    };

    // Lógica para adicionar o logo (se existir)
    if (clubInfo.logo_url) {
      try {
        // Tenta adicionar a imagem do logo diretamente pela URL
        // NOTA: Isso pode não funcionar no servidor para URLs http/https.
        // Se não funcionar, a URL precisará ser buscada e convertida para base64 ANTES de chamar addImage.
        // Para addImage funcionar com URL no servidor, jsPDF precisaria de um ambiente que suporte fetch ou XHR e Canvas, o que não é o caso aqui.
        // Por agora, apenas tentamos. Se falhar, o console mostrará um erro, mas o PDF tentará continuar.
        doc.addImage(clubInfo.logo_url, 'JPEG', 15, initialY, 40, 0); // Altura 0 para auto-ajuste pela largura 40
        // A Posição Y (initialY) será ajustada *após* a imagem ser teoricamente adicionada.
        // Como não temos como saber a altura real da imagem de forma síncrona aqui sem 'new Image()',
        // vamos adicionar um espaço fixo. Isso pode precisar de ajuste se a imagem for muito alta ou muito baixa.
        initialY += 40 + 10; // Assumindo altura do logo ~40 + espaço
        addHeaderTextAndMainContent();
      } catch (error) {
        console.error("Error adding logo to PDF (direct URL attempt):", error);
        // Prosseguir sem o logo se houver erro na tentativa direta
        addHeaderTextAndMainContent();
      }
    } else {
      // Prosseguir sem o logo
      addHeaderTextAndMainContent();
    }
  });
}
