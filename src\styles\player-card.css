/* Estilos para os cards de jogadores */
.player-card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.player-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.player-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: white;
  font-size: 2rem;
  font-weight: bold;
  color: var(--color-primary); /* Usar cor primária do tema */
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Estilos para o avatar do jogador no card */
.player-card .avatar-container {
  position: relative;
  margin-top: 5px;
}

.player-card .player-avatar {
  border: 2px solid white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  z-index: 5;
  position: relative;
}

/* Estilo para o número de cadastro */
.player-card .registration-number {
  background-color: white;
  color: var(--color-primary); /* Usar cor primária do tema */
  font-weight: 600;
  font-size: 0.75rem;
  padding: 2px 6px;
  border-radius: 4px;
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.player-status {
  position: absolute;
  top: 8px;
  left: 8px;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  color: white;
}

.status-disponivel {
  background-color: #10b981; /* green-500 */
}

.status-lesionado {
  background-color: #ef4444; /* red-500 */
}

.status-suspenso {
  background-color: #f59e0b; /* amber-500 */
}

.status-recuperacao,
.status-em-recuperacao {
  background-color: #f97316; /* orange-500 */
}

.status-inativo {
  background-color: #6b7280; /* gray-500 */
}

.status-emprestado {
  background-color: #3b82f6; /* blue-500 */
}

/* Estilos para a tabela de jogadores */
.player-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.player-table th {
  background-color: #f9fafb;
  font-weight: 500;
  text-align: left;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
}

.player-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
}

.player-table tr:hover {
  background-color: #f9fafb;
}

/* Estilos para os badges de status */
.status-badge {
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  padding: 2px 8px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge-disponivel {
  background-color: #d1fae5;
  color: #065f46;
}

.status-badge-lesionado {
  background-color: #fee2e2;
  color: #991b1b;
}

.status-badge-suspenso {
  background-color: #fef3c7;
  color: #92400e;
}

.status-badge-recuperacao {
  background-color: #ffedd5;
  color: #9a3412;
}

.status-badge-inativo {
  background-color: #f3f4f6;
  color: #374151;
}

.status-badge-emprestado {
  background-color: #dbeafe;
  color: #1e40af;
}
