import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { createCollaborator, updateCollaborator, Collaborator } from "@/api/api";
import { useUser } from "@/context/UserContext";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Upload, User } from "lucide-react";
import { validateCPF } from "@/lib/validators";
import { fetchAddressByCEP } from "@/lib/cep";
import { supabase } from "@/integrations/supabase/client";
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface NovoColaboradorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  onSuccess?: () => void;
  collaborator?: Collaborator; // Optional collaborator for editing
}

export function NovoColaboradorDialog({
  open,
  onOpenChange,
  clubId,
  onSuccess,
  collaborator
}: NovoColaboradorDialogProps) {
  const { user } = useUser();
  const [activeTab, setActiveTab] = useState("basic");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const isEditing = !!collaborator;

  // Campos básicos
  const [fullName, setFullName] = useState("");
  const [role, setRole] = useState("");
  const [phone, setPhone] = useState("");
  const [email, setEmail] = useState("");
  const [image, setImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [status, setStatus] = useState("available");
  const [entryDate, setEntryDate] = useState("");

  // Campos de documentos
  const [cpf, setCpf] = useState("");
  const [birthDate, setBirthDate] = useState("");
  const [credentialNumber, setCredentialNumber] = useState("");
  const [documentId, setDocumentId] = useState("");
  const [documentIdFile, setDocumentIdFile] = useState<File | null>(null);
  const [certificateFile, setCertificateFile] = useState<File | null>(null);
  const [medicalCertificateFile, setMedicalCertificateFile] = useState<File | null>(null);
  const [resumeFile, setResumeFile] = useState<File | null>(null);
  const [criminalRecordFile, setCriminalRecordFile] = useState<File | null>(null);

  // Campos de endereço
  const [zipCode, setZipCode] = useState("");
  const [state, setState] = useState("");
  const [city, setCity] = useState("");
  const [address, setAddress] = useState("");
  const [addressNumber, setAddressNumber] = useState("");

  // Atualizar campos quando o colaborador mudar ou o modal abrir
  useEffect(() => {
    if (collaborator) {
      setFullName(collaborator.full_name || "");
      setRole(collaborator.role || "");
      setPhone(collaborator.phone || "");
      setEmail(collaborator.email || "");
      setImagePreview(collaborator.image || null);
      setStatus(collaborator.status || "available");
      setEntryDate(collaborator.entry_date ? new Date(collaborator.entry_date).toISOString().split('T')[0] : "");
      setCpf(collaborator.cpf || "");
      setBirthDate(collaborator.birth_date ? new Date(collaborator.birth_date).toISOString().split('T')[0] : "");
      setCredentialNumber(collaborator.credential_number || "");
      setDocumentId(collaborator.document_id || "");
      setZipCode(collaborator.zip_code || "");
      setState(collaborator.state || "");
      setCity(collaborator.city || "");
      setAddress(collaborator.address || "");
      setAddressNumber(collaborator.address_number || "");
    } else {
      // Reset fields when opening for a new collaborator
      setFullName("");
      setRole("");
      setPhone("");
      setEmail("");
      setImagePreview(null);
      setStatus("available");
      setEntryDate("");
      setCpf("");
      setBirthDate("");
      setCredentialNumber("");
      setDocumentId("");
      setZipCode("");
      setState("");
      setCity("");
      setAddress("");
      setAddressNumber("");
    }
  }, [collaborator, open]);

  // Função para buscar endereço pelo CEP
  const handleZipCodeBlur = async () => {
    if (zipCode.length === 8 || zipCode.length === 9) {
      try {
        const addressData = await fetchAddressByCEP(zipCode);
        if (addressData) {
          setState(addressData.state);
          setCity(addressData.city);
          setAddress(addressData.street);
        }
      } catch (err) {
        console.error("Erro ao buscar CEP:", err);
      }
    }
  };

  // Função para lidar com o upload de imagem
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImage(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Função para lidar com o upload de documentos
  const handleFileChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    setFile: React.Dispatch<React.SetStateAction<File | null>>
  ) => {
    const file = e.target.files?.[0];
    if (file) {
      setFile(file);
    }
  };

  // Função para salvar o colaborador
  const handleSave = async () => {
    try {
      setLoading(true);
      setError(null);

      // Validar campos obrigatórios
      if (!fullName) {
        throw new Error("O nome completo é obrigatório");
      }

      if (!role) {
        throw new Error("A função é obrigatória");
      }

      // Validar CPF se preenchido
      if (cpf && !validateCPF(cpf)) {
        throw new Error("CPF inválido");
      }

      // Processar uploads de arquivos
      let imageUrl = collaborator?.image;
      let documentIdUrl = collaborator?.document_id_url;
      let certificateUrl = collaborator?.certificate_url;
      let medicalCertificateUrl = collaborator?.medical_certificate_url;
      let resumeUrl = collaborator?.resume_url;
      let criminalRecordUrl = collaborator?.criminal_record_url;

      // Upload da imagem de perfil
      if (image) {
        const { data: imageData, error: imageError } = await supabase.storage
          .from('profileimages')
          .upload(`collaborators/${clubId}/${Date.now()}_profile`, image);

        if (imageError) {
          throw new Error(`Erro ao fazer upload da imagem: ${imageError.message}`);
        }

        const { data: urlData } = supabase.storage
          .from('profileimages')
          .getPublicUrl(imageData.path);

        imageUrl = urlData.publicUrl;
      }

      // Upload do documento de identidade
      if (documentIdFile) {
        const { data: docData, error: docError } = await supabase.storage
          .from('playerdocuments')
          .upload(`collaborators/${clubId}/${Date.now()}_document_id`, documentIdFile);

        if (docError) {
          throw new Error(`Erro ao fazer upload do documento: ${docError.message}`);
        }

        const { data: urlData } = supabase.storage
          .from('playerdocuments')
          .getPublicUrl(docData.path);

        documentIdUrl = urlData.publicUrl;
      }

      // Upload do certificado
      if (certificateFile) {
        const { data: certData, error: certError } = await supabase.storage
          .from('playerdocuments')
          .upload(`collaborators/${clubId}/${Date.now()}_certificate`, certificateFile);

        if (certError) {
          throw new Error(`Erro ao fazer upload do certificado: ${certError.message}`);
        }

        const { data: urlData } = supabase.storage
          .from('playerdocuments')
          .getPublicUrl(certData.path);

        certificateUrl = urlData.publicUrl;
      }

      // Upload do atestado médico
      if (medicalCertificateFile) {
        const { data: medData, error: medError } = await supabase.storage
          .from('playerdocuments')
          .upload(`collaborators/${clubId}/${Date.now()}_medical`, medicalCertificateFile);

        if (medError) {
          throw new Error(`Erro ao fazer upload do atestado médico: ${medError.message}`);
        }

        const { data: urlData } = supabase.storage
          .from('playerdocuments')
          .getPublicUrl(medData.path);

        medicalCertificateUrl = urlData.publicUrl;
      }

      // Upload do currículo
      if (resumeFile) {
        const { data: resumeData, error: resumeError } = await supabase.storage
          .from('playerdocuments')
          .upload(`collaborators/${clubId}/${Date.now()}_resume`, resumeFile);

        if (resumeError) {
          throw new Error(`Erro ao fazer upload do currículo: ${resumeError.message}`);
        }

        const { data: urlData } = supabase.storage
          .from('playerdocuments')
          .getPublicUrl(resumeData.path);

        resumeUrl = urlData.publicUrl;
      }

      // Upload dos antecedentes criminais
      if (criminalRecordFile) {
        const { data: criminalData, error: criminalError } = await supabase.storage
          .from('playerdocuments')
          .upload(`collaborators/${clubId}/${Date.now()}_criminal`, criminalRecordFile);

        if (criminalError) {
          throw new Error(`Erro ao fazer upload dos antecedentes criminais: ${criminalError.message}`);
        }

        const { data: urlData } = supabase.storage
          .from('playerdocuments')
          .getPublicUrl(criminalData.path);

        criminalRecordUrl = urlData.publicUrl;
      }

      // Dados do colaborador
      const collaboratorData = {
        full_name: fullName,
        role,
        role_type: "technical", // Mantido para compatibilidade com o banco de dados
        phone: phone || undefined,
        email: email || undefined,
        cpf: cpf || undefined,
        birth_date: birthDate || undefined,
        credential_number: credentialNumber || undefined,
        zip_code: zipCode || undefined,
        state: state || undefined,
        city: city || undefined,
        address: address || undefined,
        address_number: addressNumber || undefined,
        // Novos campos
        image: imageUrl,
        entry_date: entryDate || undefined,
        status: status || "available",
        document_id: documentId || undefined,
        document_id_url: documentIdUrl,
        certificate_url: certificateUrl,
        medical_certificate_url: medicalCertificateUrl,
        resume_url: resumeUrl,
        criminal_record_url: criminalRecordUrl
      };

      if (isEditing && collaborator) {
        // Atualizar colaborador existente
        await updateCollaborator(
          clubId,
          user?.id || "",
          collaborator.id,
          collaboratorData
        );

        toast({
          title: "Sucesso",
          description: "Colaborador atualizado com sucesso",
        });
      } else {
        // Criar novo colaborador
        await createCollaborator(
          clubId,
          user?.id || "",
          collaboratorData
        );

        toast({
          title: "Sucesso",
          description: "Colaborador cadastrado com sucesso",
        });
      }

      // Limpar campos
      setFullName("");
      setRole("");
      setPhone("");
      setEmail("");
      setImage(null);
      setImagePreview(null);
      setStatus("available");
      setEntryDate("");
      setCpf("");
      setBirthDate("");
      setCredentialNumber("");
      setDocumentId("");
      setDocumentIdFile(null);
      setCertificateFile(null);
      setMedicalCertificateFile(null);
      setResumeFile(null);
      setCriminalRecordFile(null);

      setZipCode("");
      setState("");
      setCity("");
      setAddress("");
      setAddressNumber("");

      // Fechar o diálogo
      onOpenChange(false);

      // Chamar callback de sucesso
      if (onSuccess) {
        onSuccess();
      }
    } catch (err: any) {
      console.error("Erro ao cadastrar colaborador:", err);
      setError(err.message || "Erro ao cadastrar colaborador");
      toast({
        title: "Erro",
        description: err.message || "Erro ao cadastrar colaborador",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Editar Colaborador" : "Novo Colaborador"}</DialogTitle>
        </DialogHeader>

        {error && (
          <div className="bg-red-50 text-red-700 p-3 rounded-md text-sm mb-4">
            {error}
          </div>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="basic">Informações Básicas</TabsTrigger>
            <TabsTrigger value="documents">Documentos</TabsTrigger>
            <TabsTrigger value="uploads">Uploads</TabsTrigger>
            <TabsTrigger value="address">Endereço</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="flex items-center gap-4">
                <div className="flex-shrink-0">
                  <div className="relative w-24 h-24 rounded-full overflow-hidden border-2 border-gray-200">
                    {imagePreview ? (
                      <img src={imagePreview} alt="Preview" className="w-full h-full object-cover" />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-gray-100">
                        <User className="h-12 w-12 text-gray-400" />
                      </div>
                    )}
                    <label htmlFor="profile-image" className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity cursor-pointer">
                      <Upload className="h-6 w-6 text-white" />
                      <input
                        type="file"
                        id="profile-image"
                        className="sr-only"
                        accept="image/*"
                        onChange={handleImageChange}
                      />
                    </label>
                  </div>
                </div>
                <div className="flex-grow space-y-2">
                  <Label htmlFor="fullName">Nome Completo *</Label>
                  <Input
                    id="fullName"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    placeholder="Nome completo do colaborador"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="role">Função *</Label>
                <Select
                  value={role}
                  onValueChange={setRole}
                >
                  <SelectTrigger id="role">
                    <SelectValue placeholder="Selecione a função" />
                  </SelectTrigger>
                  <SelectContent className="max-h-[200px] overflow-y-auto">
                    <SelectItem value="Técnico">Técnico</SelectItem>
                    <SelectItem value="Auxiliar técnico">Auxiliar técnico</SelectItem>
                    <SelectItem value="Preparador de goleiro">Preparador de goleiro</SelectItem>
                    <SelectItem value="Preparador físico">Preparador físico</SelectItem>
                    <SelectItem value="Supervisor">Supervisor</SelectItem>
                    <SelectItem value="Massagista">Massagista</SelectItem>
                    <SelectItem value="Fisioterapeuta">Fisioterapeuta</SelectItem>
                    <SelectItem value="Fisiologista">Fisiologista</SelectItem>
                    <SelectItem value="Psicóloga">Psicóloga</SelectItem>
                    <SelectItem value="Nutricionista">Nutricionista</SelectItem>
                    <SelectItem value="Coordenador">Coordenador</SelectItem>
                    <SelectItem value="Motorista">Motorista</SelectItem>
                    <SelectItem value="Roupeiro">Roupeiro</SelectItem>
                    <SelectItem value="Massa terapeuta">Massa terapeuta</SelectItem>
                    <SelectItem value="Gerente de futebol">Gerente de futebol</SelectItem>
                    <SelectItem value="CEO">CEO</SelectItem>
                    <SelectItem value="Gerente administrativo">Gerente administrativo</SelectItem>
                    <SelectItem value="Cozinheira">Cozinheira</SelectItem>
                    <SelectItem value="Cozinheiro">Cozinheiro</SelectItem>
                    <SelectItem value="Gerente de operações">Gerente de operações</SelectItem>
                    <SelectItem value="Presidente">Presidente</SelectItem>
                    <SelectItem value="Vice presidente">Vice presidente</SelectItem>
                    <SelectItem value="Sócio">Sócio</SelectItem>
                    <SelectItem value="Diretor">Diretor</SelectItem>
                    <SelectItem value="Auxiliar de limpeza">Auxiliar de limpeza</SelectItem>
                    <SelectItem value="Serviço geral">Serviço geral</SelectItem>
                    <SelectItem value="Jardineiro">Jardineiro</SelectItem>
                    <SelectItem value="Lavadora de roupas">Lavadora de roupas</SelectItem>
                    <SelectItem value="Assistente social">Assistente social</SelectItem>
                    <SelectItem value="Outros">Outros</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select
                    value={status}
                    onValueChange={setStatus}
                  >
                    <SelectTrigger id="status">
                      <SelectValue placeholder="Selecione o status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="available">Disponível</SelectItem>
                      <SelectItem value="inactive">Inativo</SelectItem>
                      <SelectItem value="vacation">Férias</SelectItem>
                      <SelectItem value="away">Afastado</SelectItem>
                      <SelectItem value="medical">Ordem Médica</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="entryDate">Data de Entrada</Label>
                  <Input
                    id="entryDate"
                    type="date"
                    value={entryDate}
                    onChange={(e) => setEntryDate(e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">Telefone</Label>
                  <Input
                    id="phone"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    placeholder="(00) 00000-0000"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">E-mail</Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="documents" className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="cpf">CPF</Label>
                  <Input
                    id="cpf"
                    value={cpf}
                    onChange={(e) => setCpf(e.target.value)}
                    placeholder="000.000.000-00"
                    maxLength={14}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="birthDate">Data de Nascimento</Label>
                  <Input
                    id="birthDate"
                    type="date"
                    value={birthDate}
                    onChange={(e) => setBirthDate(e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="documentId">Número do RG/Documento</Label>
                  <Input
                    id="documentId"
                    value={documentId}
                    onChange={(e) => setDocumentId(e.target.value)}
                    placeholder="Número do documento de identidade"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="credentialNumber">Número de Registro na Função</Label>
                  <Input
                    id="credentialNumber"
                    value={credentialNumber}
                    onChange={(e) => setCredentialNumber(e.target.value)}
                    placeholder="Número de registro profissional"
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="uploads" className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="documentIdFile">RG ou Documento com Número de Registro</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="documentIdFile"
                    type="file"
                    onChange={(e) => handleFileChange(e, setDocumentIdFile)}
                    className="flex-1"
                  />
                  {documentIdFile && <span className="text-green-600 text-sm">Arquivo selecionado</span>}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="certificateFile">Certificado</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="certificateFile"
                    type="file"
                    onChange={(e) => handleFileChange(e, setCertificateFile)}
                    className="flex-1"
                  />
                  {certificateFile && <span className="text-green-600 text-sm">Arquivo selecionado</span>}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="medicalCertificateFile">Atestado Médico</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="medicalCertificateFile"
                    type="file"
                    onChange={(e) => handleFileChange(e, setMedicalCertificateFile)}
                    className="flex-1"
                  />
                  {medicalCertificateFile && <span className="text-green-600 text-sm">Arquivo selecionado</span>}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="resumeFile">Currículo</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="resumeFile"
                    type="file"
                    onChange={(e) => handleFileChange(e, setResumeFile)}
                    className="flex-1"
                  />
                  {resumeFile && <span className="text-green-600 text-sm">Arquivo selecionado</span>}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="criminalRecordFile">Antecedentes Criminais</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="criminalRecordFile"
                    type="file"
                    onChange={(e) => handleFileChange(e, setCriminalRecordFile)}
                    className="flex-1"
                  />
                  {criminalRecordFile && <span className="text-green-600 text-sm">Arquivo selecionado</span>}
                </div>
              </div>
            </div>
          </TabsContent>



          <TabsContent value="address" className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="zipCode">CEP</Label>
                  <Input
                    id="zipCode"
                    value={zipCode}
                    onChange={(e) => setZipCode(e.target.value)}
                    onBlur={handleZipCodeBlur}
                    placeholder="00000-000"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="state">Estado</Label>
                  <Input
                    id="state"
                    value={state}
                    onChange={(e) => setState(e.target.value)}
                    placeholder="UF"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="city">Cidade</Label>
                <Input
                  id="city"
                  value={city}
                  onChange={(e) => setCity(e.target.value)}
                  placeholder="Cidade"
                />
              </div>

              <div className="grid grid-cols-4 gap-4">
                <div className="col-span-3 space-y-2">
                  <Label htmlFor="address">Endereço</Label>
                  <Input
                    id="address"
                    value={address}
                    onChange={(e) => setAddress(e.target.value)}
                    placeholder="Rua, Avenida, etc."
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="addressNumber">Número</Label>
                  <Input
                    id="addressNumber"
                    value={addressNumber}
                    onChange={(e) => setAddressNumber(e.target.value)}
                    placeholder="Nº"
                  />
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Salvar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
