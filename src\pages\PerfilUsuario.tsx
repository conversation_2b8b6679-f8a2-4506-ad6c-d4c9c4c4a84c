import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/components/ui/use-toast";
import { useUser } from "@/context/UserContext";
import { useCurrentClubId } from "@/context/ClubContext";
import { getUserProfile, updateUserProfile } from "@/api/api";
import { AvatarUpload } from "@/components/users/AvatarUpload";
import { UserPermissions } from "@/components/users/UserPermissions";
import { Save, User } from "lucide-react";

interface UserProfile {
  id: string;
  name: string;
  email: string;
  profile_image?: string;
  phone?: string;
  role?: string;
}

export default function PerfilUsuario() {
  const { user, setShowChangePassword } = useUser();
  const clubId = useCurrentClubId();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Estados para os campos do formulário
  const [name, setName] = useState("");
  const [phone, setPhone] = useState("");
  const [profileImage, setProfileImage] = useState("");

  // Carregar perfil do usuário
  useEffect(() => {
    const fetchProfile = async () => {
      if (!user?.id) return;

      try {
        setLoading(true);
        const data = await getUserProfile(user.id);
        setProfile(data);

        // Preencher campos do formulário
        setName(data.name || "");
        setPhone(data.phone || "");
        setProfileImage(data.profile_image || "");
      } catch (err: any) {
        console.error("Erro ao carregar perfil:", err);
        toast({
          title: "Erro",
          description: "Não foi possível carregar seu perfil",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, [user?.id]);

  // Função para salvar o perfil
  const handleSaveProfile = async () => {
    if (!user?.id) return;

    try {
      setSaving(true);

      await updateUserProfile(user.id, {
        name,
        phone,
        profile_image: profileImage,
      });

      toast({
        title: "Sucesso",
        description: "Perfil atualizado com sucesso",
      });

      // Atualizar perfil local
      setProfile((prev) => {
        if (!prev) return null;
        return {
          ...prev,
          name,
          phone,
          profile_image: profileImage,
        };
      });
    } catch (err: any) {
      console.error("Erro ao salvar perfil:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao salvar perfil",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-80">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-team-blue"></div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        Perfil não encontrado
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Meu Perfil</h1>
        <p className="text-muted-foreground">
          Gerencie suas informações pessoais e preferências
        </p>
      </div>

      <Separator />

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Informações Pessoais
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex flex-col sm:flex-row items-center gap-4 mb-6">
                  <AvatarUpload
                    userId={user?.id || ""}
                    name={name}
                    imageUrl={profileImage}
                    onImageChange={setProfileImage}
                    size="lg"
                  />
                  <div className="text-center sm:text-left">
                    <h3 className="text-lg font-medium">{name}</h3>
                    <p className="text-muted-foreground">{profile.email}</p>
                    <p className="text-sm text-muted-foreground mt-1">
                      {profile.role === "admin"
                        ? "Administrador"
                        : profile.role === "manager"
                        ? "Gerente"
                        : profile.role === "coach"
                        ? "Treinador"
                        : profile.role === "staff"
                        ? "Funcionário"
                        : profile.role === "player"
                        ? "Jogador"
                        : profile.role || "Usuário"}
                    </p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Nome</Label>
                      <Input
                        id="name"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        placeholder="Seu nome completo"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        value={profile.email}
                        disabled
                        className="bg-muted"
                      />
                      <p className="text-xs text-muted-foreground">
                        O email não pode ser alterado
                      </p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Telefone</Label>
                    <Input
                      id="phone"
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                      placeholder="Seu telefone"
                    />
                  </div>

                  <div className="flex justify-end">
                    <Button onClick={handleSaveProfile} disabled={saving}>
                      {saving ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Salvando...
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4 mr-1" />
                          Salvar Alterações
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Seção de permissões (apenas para visualização) */}
          {user?.id && (
            <UserPermissions userId={user.id} userName={name} />
          )}
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Informações da Conta</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium">ID da Conta</h3>
                  <p className="text-sm text-muted-foreground break-all mt-1">
                    {profile.id}
                  </p>
                </div>

                <div>
                  <h3 className="font-medium">Clube Atual</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    ID: {clubId}
                  </p>
                </div>

                <div>
                  <h3 className="font-medium">Papel no Sistema</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    {profile.role === "admin"
                      ? "Administrador"
                      : profile.role === "manager"
                      ? "Gerente"
                      : profile.role === "coach"
                      ? "Treinador"
                      : profile.role === "staff"
                      ? "Funcionário"
                      : profile.role === "player"
                      ? "Jogador"
                      : profile.role || "Usuário"}
                  </p>
                </div>

                <Separator />

                <div>
                  <h3 className="font-medium">Alterar Senha</h3>
                  <p className="text-sm text-muted-foreground mt-1 mb-3">
                    Você pode alterar sua senha a qualquer momento para manter sua conta segura.
                  </p>
                  <Button
                    variant="outline"
                    onClick={() => setShowChangePassword(true)}
                    className="w-full"
                  >
                    Alterar Senha
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
