import { supabase } from "@/integrations/supabase/client";

// Tipos
export type Opponent = {
  id: string;
  club_id: number;
  name: string;
  logo_url: string | null;
  country: string | null;
  city: string | null;
  stadium: string | null;
  created_at: string;
  updated_at: string;
};

// Funções para Opponents
export async function getOpponents(clubId: number): Promise<Opponent[]> {
  const { data, error } = await supabase
    .from("opponents")
    .select("*")
    .eq("club_id", clubId)
    .order("name");

  if (error) {
    console.error("Erro ao buscar adversários:", error);
    throw new Error(`Erro ao buscar adversários: ${error.message}`);
  }

  return data as Opponent[];
}

export async function getOpponentById(clubId: number, id: string): Promise<Opponent> {
  const { data, error } = await supabase
    .from("opponents")
    .select("*")
    .eq("club_id", clubId)
    .eq("id", id)
    .single();

  if (error) {
    console.error("Erro ao buscar adversário:", error);
    throw new Error(`Erro ao buscar adversário: ${error.message}`);
  }

  return data as Opponent;
}

export async function createOpponent(clubId: number, opponent: Omit<Opponent, "id" | "club_id" | "created_at" | "updated_at">): Promise<Opponent> {
  const { data, error } = await supabase
    .from("opponents")
    .insert({
      club_id: clubId,
      name: opponent.name,
      logo_url: opponent.logo_url,
      country: opponent.country,
      city: opponent.city,
      stadium: opponent.stadium,
    })
    .select()
    .single();

  if (error) {
    console.error("Erro ao criar adversário:", error);
    throw new Error(`Erro ao criar adversário: ${error.message}`);
  }

  return data as Opponent;
}

export async function updateOpponent(clubId: number, id: string, opponent: Partial<Omit<Opponent, "id" | "club_id" | "created_at" | "updated_at">>): Promise<Opponent> {
  const { data, error } = await supabase
    .from("opponents")
    .update({
      name: opponent.name,
      logo_url: opponent.logo_url,
      country: opponent.country,
      city: opponent.city,
      stadium: opponent.stadium,
      updated_at: new Date().toISOString(),
    })
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error("Erro ao atualizar adversário:", error);
    throw new Error(`Erro ao atualizar adversário: ${error.message}`);
  }

  return data as Opponent;
}

export async function deleteOpponent(clubId: number, id: string): Promise<boolean> {
  // Verificar se o adversário está sendo usado em alguma partida
  const { data: matchesData, error: matchesError } = await supabase
    .from("matches")
    .select("id")
    .eq("opponent_id", id)
    .limit(1);

  if (matchesError) {
    console.error("Erro ao verificar uso do adversário:", matchesError);
    throw new Error(`Erro ao verificar uso do adversário: ${matchesError.message}`);
  }

  if (matchesData && matchesData.length > 0) {
    throw new Error("Este adversário não pode ser excluído porque está sendo usado em partidas.");
  }

  const { error } = await supabase
    .from("opponents")
    .delete()
    .eq("club_id", clubId)
    .eq("id", id);

  if (error) {
    console.error("Erro ao deletar adversário:", error);
    throw new Error(`Erro ao deletar adversário: ${error.message}`);
  }

  return true;
}
