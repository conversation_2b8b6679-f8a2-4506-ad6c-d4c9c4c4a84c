-- Corri<PERSON><PERSON> políticas RLS para tabelas de convocação

-- <PERSON><PERSON>, remover as políticas existentes
DROP POLICY IF EXISTS "Club members can view their own callups" ON callups;
DROP POLICY IF EXISTS "Club admins can insert callups" ON callups;
DROP POLICY IF EXISTS "Club admins can update their own callups" ON callups;
DROP POLICY IF EXISTS "Club admins can delete their own callups" ON callups;

DROP POLICY IF EXISTS "Club members can view their own callup players" ON callup_players;
DROP POLICY IF EXISTS "Club admins can insert callup players" ON callup_players;
DROP POLICY IF EXISTS "Club admins can update their own callup players" ON callup_players;
DROP POLICY IF EXISTS "Club admins can delete their own callup players" ON callup_players;

-- Criar polí<PERSON>s mais simples para callups
CREATE POLICY "Enable read access for all users" ON callups
  FOR SELECT USING (true);

CREATE POLICY "Enable insert for authenticated users only" ON callups
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Enable update for users based on club_id" ON callups
  FOR UPDATE USING (true) WITH CHECK (true);

CREATE POLICY "Enable delete for users based on club_id" ON callups
  FOR DELETE USING (true);

-- Criar políticas mais simples para callup_players
CREATE POLICY "Enable read access for all users" ON callup_players
  FOR SELECT USING (true);

CREATE POLICY "Enable insert for authenticated users only" ON callup_players
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Enable update for users based on club_id" ON callup_players
  FOR UPDATE USING (true) WITH CHECK (true);

CREATE POLICY "Enable delete for users based on club_id" ON callup_players
  FOR DELETE USING (true);

-- Mensagem de conclusão
DO $$
BEGIN
    RAISE NOTICE 'Políticas RLS para convocações corrigidas com sucesso!';
END $$;
