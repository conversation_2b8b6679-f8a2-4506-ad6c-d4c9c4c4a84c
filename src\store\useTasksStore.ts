import { create } from "zustand";
import { Task } from "../api/api";
import { createTask, updateTask, deleteTask, getTasks } from "../api/api";

interface TasksState {
  tasks: Task[];
  loading: boolean;
  error: string | null;
  fetchTasks: (club_id: number) => Promise<void>;
  addTask: (club_id: number, task: Omit<Task, "id">) => Promise<void>;
  updateTask: (club_id: number, id: number, updates: Partial<Task>) => Promise<void>;
  deleteTask: (club_id: number, id: number) => Promise<void>;
}

export const useTasksStore = create<TasksState>((set) => ({
  tasks: [],
  loading: false,
  error: null,

  fetchTasks: async (club_id: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const tasks = await getTasks(club_id);
      set({ tasks, loading: false });
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao buscar tarefas", loading: false });
    }
  },

  addTask: async (club_id: number, task: Omit<Task, "id">): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const newTask = await createTask(club_id, task);
      set((state) => ({ tasks: [...state.tasks, newTask], loading: false }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao adicionar tarefa", loading: false });
    }
  },

  updateTask: async (club_id: number, id: number, updates: Partial<Task>): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const updated = await updateTask(club_id, id, updates);
      if (updated) {
        // Atualiza imediatamente a lista (otimista) - sem fetch duplo
        set((state) => ({
          tasks: state.tasks.map(t => t.id === id ? updated : t),
          loading: false,
        }));
      } else {
        set({ error: "Tarefa não encontrada", loading: false });
      }
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao atualizar tarefa", loading: false });
    }
  },

  deleteTask: async (club_id: number, id: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const ok = await deleteTask(club_id, id);
      if (ok) {
        set((state) => ({ tasks: state.tasks.filter(t => t.id !== id), loading: false }));
      } else {
        set({ error: "Erro ao remover tarefa", loading: false });
      }
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao remover tarefa", loading: false });
    }
  },
}));
