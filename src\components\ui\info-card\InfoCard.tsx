
import React from 'react';
import { cn } from "@/lib/utils";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export interface Action {
  label: string;
  onClick: () => void;
  className?: string;
}

export interface InfoCardProps {
  title?: string;
  description?: string;
  className?: string;
  contentClassName?: string;
  icon?: React.ReactNode;
  actions?: Action[];
  children: React.ReactNode;
  footer?: React.ReactNode;
  onCardClick?: () => void;
  menuActions?: { label: string; onClick: () => void; className?: string }[];
}

export const InfoCard = ({
  title,
  description,
  className,
  contentClassName,
  icon,
  actions,
  children,
  footer,
  onCardClick,
  menuActions,
}: InfoCardProps) => {
  return (
    <Card 
      className={cn(
        onCardClick && "cursor-pointer hover:border-primary/50 transition-colors",
        className
      )}
      onClick={onCardClick}
    >
      {(title || description || icon || menuActions) && (
        <CardHeader className={cn(
          "flex flex-row items-start justify-between space-y-0",
          !description && "pb-2"
        )}>
          <div className="space-y-1">
            {icon && title ? (
              <div className="flex items-center gap-2">
                {icon}
                <CardTitle>{title}</CardTitle>
              </div>
            ) : title ? (
              <CardTitle>{title}</CardTitle>
            ) : icon ? (
              <div>{icon}</div>
            ) : null}
            
            {description && <CardDescription>{description}</CardDescription>}
          </div>
          
          {menuActions && menuActions.length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {menuActions.map((action, index) => (
                  <DropdownMenuItem 
                    key={index}
                    onClick={(e) => {
                      e.stopPropagation();
                      action.onClick();
                    }}
                    className={action.className}
                  >
                    {action.label}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </CardHeader>
      )}
      
      <CardContent className={contentClassName}>
        {children}
      </CardContent>
      
      {(footer || (actions && actions.length > 0)) && (
        <CardFooter className="flex justify-between items-center">
          {footer ? (
            footer
          ) : actions && actions.length > 0 ? (
            <div className="flex gap-2">
              {actions.map((action, index) => (
                <Button 
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    action.onClick();
                  }}
                  className={action.className}
                >
                  {action.label}
                </Button>
              ))}
            </div>
          ) : null}
        </CardFooter>
      )}
    </Card>
  );
};

