import { useState } from "react";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON>eader, 
  <PERSON><PERSON>Title, 
  DialogFooter 
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { useAdministrativeRemindersStore } from "@/store/useAdministrativeRemindersStore";
import { useUser } from "@/context/UserContext";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon, Clock } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { TimePickerDemo } from "@/components/ui/time-picker-demo";

interface NovoLembreteDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
}

export function NovoLembreteDialog({ 
  open, 
  onOpenChange, 
  clubId 
}: NovoLembreteDialogProps) {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [reminderDate, setReminderDate] = useState<Date | undefined>(undefined);
  const [reminderTime, setReminderTime] = useState<Date | undefined>(new Date());
  const [reminderType, setReminderType] = useState<'activity' | 'document' | 'email' | 'meeting'>('activity');
  const [error, setError] = useState("");
  
  const { addReminder, loading } = useAdministrativeRemindersStore();
  const { user } = useUser();

  const handleSave = async () => {
    if (!title.trim()) {
      setError("O título é obrigatório.");
      return;
    }
    
    if (!reminderDate) {
      setError("A data do lembrete é obrigatória.");
      return;
    }
    
    if (!user) {
      setError("Usuário não autenticado.");
      return;
    }
    
    setError("");
    
    // Combine date and time
    const combinedDate = new Date(reminderDate);
    if (reminderTime) {
      combinedDate.setHours(reminderTime.getHours());
      combinedDate.setMinutes(reminderTime.getMinutes());
    }
    
    try {
      await addReminder(clubId, {
        title,
        description,
        reminder_date: combinedDate.toISOString(),
        completed: false,
        created_by: user.id,
        club_id: clubId,
        reminder_type: reminderType
      });
      
      toast({
        title: "Lembrete criado",
        description: "O lembrete foi criado com sucesso.",
      });
      
      resetForm();
      onOpenChange(false);
    } catch (err) {
      setError("Erro ao criar lembrete.");
      toast({
        title: "Erro ao criar lembrete",
        description: "Ocorreu um erro ao criar o lembrete.",
        variant: "destructive",
      });
    }
  };

  const resetForm = () => {
    setTitle("");
    setDescription("");
    setReminderDate(undefined);
    setReminderTime(new Date());
    setReminderType('activity');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Novo Lembrete</DialogTitle>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="title" className="text-right">
              Título
            </Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="col-span-3"
            />
          </div>
          
          <div className="grid grid-cols-4 items-start gap-4">
            <Label htmlFor="description" className="text-right pt-2">
              Descrição
            </Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="col-span-3"
              rows={3}
            />
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="reminder-type" className="text-right">
              Tipo
            </Label>
            <Select
              value={reminderType}
              onValueChange={(value) => setReminderType(value as 'activity' | 'document' | 'email' | 'meeting')}
            >
              <SelectTrigger id="reminder-type" className="col-span-3">
                <SelectValue placeholder="Selecione o tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="activity">Atividade</SelectItem>
                <SelectItem value="document">Documento</SelectItem>
                <SelectItem value="email">E-mail</SelectItem>
                <SelectItem value="meeting">Reunião</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="reminder-date" className="text-right">
              Data
            </Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id="reminder-date"
                  variant="outline"
                  className={cn(
                    "col-span-3 justify-start text-left font-normal",
                    !reminderDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {reminderDate ? format(reminderDate, "PPP", { locale: ptBR }) : "Selecione uma data"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={reminderDate}
                  onSelect={setReminderDate}
                  initialFocus
                  locale={ptBR}
                />
              </PopoverContent>
            </Popover>
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="reminder-time" className="text-right">
              Hora
            </Label>
            <div className="col-span-3 flex items-center gap-2">
              <TimePickerDemo 
                setDate={setReminderTime} 
                date={reminderTime || new Date()} 
              />
            </div>
          </div>
          
          {error && (
            <div className="text-red-500 text-sm col-span-4 text-center">
              {error}
            </div>
          )}
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            {loading ? "Salvando..." : "Salvar"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
