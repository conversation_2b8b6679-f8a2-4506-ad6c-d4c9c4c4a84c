-- Fix RLS policies for players table to resolve 404 errors

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Club members can view their own players" ON players;
DROP POLICY IF EXISTS "Club members can insert their own players" ON players;
DROP POLICY IF EXISTS "Club members can update their own players" ON players;
DROP POLICY IF EXISTS "Club members can delete their own players" ON players;
DROP POLICY IF EXISTS "Players can view their own profile" ON players;
DROP POLICY IF EXISTS "Players can update their own profile" ON players;

-- Enable RLS on players table
ALTER TABLE players ENABLE ROW LEVEL SECURITY;

-- Create function to get current club_id from JWT or headers
CREATE OR REPLACE FUNCTION get_current_club_id()
RETURNS INTEGER AS $$
BEGIN
  -- Try to get club_id from JWT first
  IF auth.jwt() ->> 'club_id' IS NOT NULL THEN
    RETURN (auth.jwt() ->> 'club_id')::INTEGER;
  END IF;
  
  -- Try to get from custom header (for API calls)
  IF current_setting('request.headers', true)::json ->> 'x-club-id' IS NOT NULL THEN
    RETURN (current_setting('request.headers', true)::json ->> 'x-club-id')::INTEGER;
  END IF;
  
  -- Fallback: try to get from club_members table
  RETURN (
    SELECT club_id 
    FROM club_members 
    WHERE user_id = auth.uid() 
    AND status = 'ativo'
    LIMIT 1
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Policy for club members to view players
CREATE POLICY "Club members can view players" 
ON players
FOR SELECT 
USING (
  club_id = get_current_club_id()
  OR 
  -- Allow players to view their own profile
  (user_id = auth.uid())
);

-- Policy for club members to insert players
CREATE POLICY "Club members can insert players" 
ON players
FOR INSERT 
WITH CHECK (
  club_id = get_current_club_id()
);

-- Policy for club members to update players
CREATE POLICY "Club members can update players" 
ON players
FOR UPDATE 
USING (
  club_id = get_current_club_id()
  OR 
  -- Allow players to update their own profile (limited fields)
  (user_id = auth.uid())
)
WITH CHECK (
  club_id = get_current_club_id()
  OR 
  -- Allow players to update their own profile (limited fields)
  (user_id = auth.uid())
);

-- Policy for club members to delete players
CREATE POLICY "Club members can delete players" 
ON players
FOR DELETE 
USING (
  club_id = get_current_club_id()
);

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON players TO authenticated;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_players_club_id_user_id ON players(club_id, user_id);
CREATE INDEX IF NOT EXISTS idx_players_user_id ON players(user_id);
