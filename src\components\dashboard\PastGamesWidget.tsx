import { ChevronRight } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import type { MatchHistory } from "@/api/api";

interface PastGamesWidgetProps {
  matchHistory?: MatchHistory[];
}

export function PastGamesWidget({ matchHistory = [] }: PastGamesWidgetProps) {
  const navigate = useNavigate();

  // Mapeamento para status visual
  const getStatus = (result: string) => {
    if (result === "win") return "vitoria";
    if (result === "draw") return "empate";
    if (result === "loss") return "derrota";
    return "";
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-lg font-medium"><PERSON><PERSON></CardTitle>
        <Button variant="ghost" size="sm" onClick={() => navigate("/jogos-passados")}>
          Ver todos
          <ChevronRight className="ml-1 h-4 w-4" />
        </Button>
      </CardHeader>
      <CardContent className="p-0">
        {matchHistory.length === 0 ? (
          <div className="p-4 text-center text-muted-foreground">Nenhum jogo registrado para este clube.</div>
        ) : (
          matchHistory.slice(0, 3).map((game) => (
            <div
              key={game.id}
              className="flex items-center justify-between p-4 border-b last:border-b-0 hover:bg-muted/50 transition-colors"
            >
              <div>
                <div className="font-medium flex items-center gap-2">
                  {game.opponent}
                  <Badge
                    variant="outline"
                    className={`${
                      getStatus(game.result) === "vitoria"
                        ? "border-emerald-200 bg-emerald-50 text-emerald-700"
                        : getStatus(game.result) === "empate"
                        ? "border-gray-200 bg-gray-50 text-gray-700"
                        : "border-rose-200 bg-rose-50 text-rose-700"
                    }`}
                  >
                    {game.score_home}-{game.score_away}
                  </Badge>
                </div>
                <div className="text-sm text-muted-foreground">{game.competition} • {game.date}</div>
              </div>
              <Button variant="ghost" size="icon" className="h-8 w-8 p-0">
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          ))
        )}
      </CardContent>
    </Card>
  );
}
