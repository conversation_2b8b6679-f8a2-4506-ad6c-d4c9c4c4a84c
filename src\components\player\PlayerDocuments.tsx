import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { DocumentUpload } from "@/components/ui/document-upload";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { toast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { usePermission } from "@/hooks/usePermission";
import {
  DOCUMENT_TYPES,
  DOCUMENT_LABELS,
  getPlayerDocuments,
  uploadDocument,
  deleteDocument,
  PlayerDocument,
  checkRequiredDocuments,
  uploadPlayerPhoto,
} from "@/api/api";
import { verifyAndFixDocumentUrl } from "@/api/document-utils";
import { FileText, Upload, CheckCircle, AlertCircle, Trash2, Camera } from "lucide-react";
import { PermissionControl } from "@/components/PermissionControl";

interface PlayerDocumentsProps {
  playerId: string;
  canEdit?: boolean;
}

export function PlayerDocuments({ playerId, canEdit = false }: PlayerDocumentsProps) {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { can } = usePermission();

  const [documents, setDocuments] = useState<PlayerDocument[]>([]);
  const [documentStatus, setDocumentStatus] = useState<Record<string, { status: string }>>(
    {}
  );
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [viewDocument, setViewDocument] = useState<string | null>(null);
  const [uploadingDocumentType, setUploadingDocumentType] = useState<string | null>(null);
  const [documentFile, setDocumentFile] = useState<File | null>(null);
  // Estado para armazenar as URLs corrigidas dos documentos
  const [correctedUrls, setCorrectedUrls] = useState<Record<string, string | null>>({});

  // Verificar permissões
  const hasViewPermission = can("players.documents.view") || can("players.view_own");
  const hasEditPermission = canEdit || can("players.documents.verify") || can("players.view_own");

  // Carregar documentos do jogador
  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        setLoading(true);
        const [docs, status] = await Promise.all([
          getPlayerDocuments(clubId, playerId),
          checkRequiredDocuments(clubId, playerId),
        ]);
        setDocuments(docs);
        setDocumentStatus(status);
      } catch (err: any) {
        console.error("Erro ao carregar documentos:", err);
        toast({
          title: "Erro",
          description: "Não foi possível carregar os documentos",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    if (playerId) {
      fetchDocuments();
    }
  }, [clubId, playerId]);

  // Função para fazer upload de um documento
  const handleUploadDocument = async () => {
    if (!uploadingDocumentType || !documentFile) {
      return;
    }

    // Verificar permissão
    if (!hasEditPermission) {
      toast({
        title: "Acesso Negado",
        description: "Você não tem permissão para enviar documentos",
        variant: "destructive",
      });
      return;
    }

    try {
      setUploading(true);
      await uploadDocument(clubId, playerId, uploadingDocumentType, documentFile, user?.id);

      // Recarregar documentos
      const [docs, status] = await Promise.all([
        getPlayerDocuments(clubId, playerId),
        checkRequiredDocuments(clubId, playerId),
      ]);
      setDocuments(docs);
      setDocumentStatus(status);

      toast({
        title: "Sucesso",
        description: "Documento enviado com sucesso",
      });

      // Limpar estado
      setUploadingDocumentType(null);
      setDocumentFile(null);
    } catch (err: any) {
      console.error("Erro ao enviar documento:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao enviar documento",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  // Função para excluir um documento
  const handleDeleteDocument = async (documentType: string) => {
    // Verificar permissão
    if (!hasEditPermission) {
      toast({
        title: "Acesso Negado",
        description: "Você não tem permissão para excluir documentos",
        variant: "destructive",
      });
      return;
    }

    try {
      // Encontrar o documento pelo tipo
      const doc = documents.find((d) => d.document_type === documentType);
      if (!doc) {
        toast({
          title: "Erro",
          description: "Documento não encontrado",
          variant: "destructive",
        });
        return;
      }

      // Confirmar exclusão
      if (!window.confirm(`Tem certeza que deseja excluir o documento ${DOCUMENT_LABELS[documentType]}?`)) {
        return;
      }

      // Excluir o documento
      await deleteDocument(doc.id, user?.id);

      // Recarregar documentos
      const [docs, status] = await Promise.all([
        getPlayerDocuments(clubId, playerId),
        checkRequiredDocuments(clubId, playerId),
      ]);
      setDocuments(docs);
      setDocumentStatus(status);

      // Atualizar URLs corrigidas
      const updatedUrls = { ...correctedUrls };
      delete updatedUrls[documentType];
      setCorrectedUrls(updatedUrls);

      toast({
        title: "Sucesso",
        description: "Documento excluído com sucesso",
      });
    } catch (err: any) {
      console.error("Erro ao excluir documento:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao excluir documento",
        variant: "destructive",
      });
    }
  };

  // Função para fazer upload da foto do jogador
  const handleUploadPlayerPhoto = async (file: File) => {
    // Verificar permissão
    if (!hasEditPermission) {
      toast({
        title: "Acesso Negado",
        description: "Você não tem permissão para enviar fotos",
        variant: "destructive",
      });
      return;
    }

    try {
      setUploading(true);

      // Fazer upload da foto usando a função específica para fotos de perfil
      const imageUrl = await uploadPlayerPhoto(clubId, playerId, file, user?.id);

      toast({
        title: "Sucesso",
        description: "Foto do jogador enviada com sucesso",
      });

      // Forçar atualização da página para mostrar a nova foto
      window.location.reload();

    } catch (err: any) {
      console.error("Erro ao enviar foto do jogador:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao enviar foto do jogador",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  // Função para abrir o modal de upload
  const openUploadModal = (documentType: string) => {
    setUploadingDocumentType(documentType);
    setDocumentFile(null);
  };

  // Função para obter o status de um documento
  const getDocumentStatus = (documentType: string) => {
    // Verificar se o documento existe na lista de documentos
    const doc = documents.find((d) => d.document_type === documentType);
    if (doc) {
      return doc.status as "pending" | "verified" | "rejected";
    }

    // Se não existir, verificar no status geral
    return documentStatus[documentType]?.status || "missing";
  };

  // Função para obter a URL de um documento
  const getDocumentUrl = (documentType: string) => {
    // Primeiro, verificar se temos uma URL corrigida
    if (correctedUrls[documentType]) {
      return correctedUrls[documentType];
    }

    // Caso contrário, retornar a URL original
    const doc = documents.find((d) => d.document_type === documentType);
    return doc?.file_url || null;
  };

  // Efeito para verificar e corrigir as URLs dos documentos
  useEffect(() => {
    const verifyDocumentUrls = async () => {
      if (documents.length === 0) return;

      const updatedUrls: Record<string, string | null> = {};

      // Verificar cada documento
      for (const doc of documents) {
        try {
          const correctedUrl = await verifyAndFixDocumentUrl(doc.id, doc.file_url);
          updatedUrls[doc.document_type] = correctedUrl || doc.file_url;
        } catch (err) {
          console.error(`Erro ao verificar URL do documento ${doc.document_type}:`, err);
          updatedUrls[doc.document_type] = doc.file_url;
        }
      }

      setCorrectedUrls(updatedUrls);
    };

    verifyDocumentUrls();
  }, [documents]);

  // Calcular progresso de documentos
  const calculateProgress = () => {
    const requiredDocumentTypes = Object.keys(DOCUMENT_TYPES).filter(
      (key) => key !== "OTHER"
    );
    const totalRequired = requiredDocumentTypes.length;
    const uploaded = requiredDocumentTypes.filter(
      (type) => getDocumentStatus(DOCUMENT_TYPES[type as keyof typeof DOCUMENT_TYPES]) !== "missing"
    ).length;
    const verified = requiredDocumentTypes.filter(
      (type) =>
        getDocumentStatus(DOCUMENT_TYPES[type as keyof typeof DOCUMENT_TYPES]) === "verified"
    ).length;

    return {
      uploaded: Math.round((uploaded / totalRequired) * 100),
      verified: Math.round((verified / totalRequired) * 100),
    };
  };

  const progress = calculateProgress();

  // Se não tem permissão para visualizar, mostrar mensagem
  if (!hasViewPermission) {
    return (
      <Card>
        <CardContent className="py-6">
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  Você não tem permissão para visualizar os documentos do jogador.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <span>Documentos</span>
            <div className="flex gap-2">
              <PermissionControl permission={["players.documents.verify", "players.view_own"]} fallback={null}>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // Criar um input de arquivo oculto
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = 'image/*';
                    input.onchange = (e) => {
                      const file = (e.target as HTMLInputElement).files?.[0];
                      if (file) {
                        handleUploadPlayerPhoto(file);
                      }
                    };
                    input.click();
                  }}
                  className="text-xs flex items-center gap-1"
                  disabled={uploading}
                >
                  <Camera className="h-3 w-3" />
                  Foto
                </Button>
              </PermissionControl>

              {documents.length > 0 && (
                <PermissionControl permission={["players.documents.view", "players.view_own"]}>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={async () => {
                      // Verificar todos os documentos novamente
                      const updatedUrls: Record<string, string | null> = {};

                      for (const doc of documents) {
                        try {
                          const correctedUrl = await verifyAndFixDocumentUrl(doc.id, doc.file_url);
                          updatedUrls[doc.document_type] = correctedUrl || doc.file_url;

                          if (correctedUrl && correctedUrl !== doc.file_url) {
                            toast({
                              title: "URL Corrigida",
                              description: `URL do documento ${DOCUMENT_LABELS[doc.document_type]} foi corrigida.`,
                            });
                          }
                        } catch (err) {
                          console.error(`Erro ao verificar URL do documento ${doc.document_type}:`, err);
                          updatedUrls[doc.document_type] = doc.file_url;
                        }
                      }

                      setCorrectedUrls(updatedUrls);

                      toast({
                        title: "Verificação Concluída",
                        description: "Todos os documentos foram verificados.",
                      });
                    }}
                    className="text-xs"
                  >
                    Verificar URLs
                  </Button>
                </PermissionControl>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2 text-sm font-normal">
            <div className="flex items-center">
              <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
              <span>Verificados: {progress.verified}%</span>
            </div>
            <div className="flex items-center">
              <Upload className="h-4 w-4 text-blue-500 mr-1" />
              <span>Enviados: {progress.uploaded}%</span>
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.keys(DOCUMENT_TYPES).map((key) => {
              const docType = DOCUMENT_TYPES[key as keyof typeof DOCUMENT_TYPES];
              const docLabel = DOCUMENT_LABELS[docType];
              const status = getDocumentStatus(docType);
              const url = getDocumentUrl(docType);

              return (
                <DocumentUpload
                  key={docType}
                  documentType={docType}
                  documentLabel={docLabel}
                  status={status as any}
                  value={url || undefined}
                  onChange={(value, file) => {
                    if (file) {
                      // Upload de novo documento
                      setUploadingDocumentType(docType);
                      setDocumentFile(file);
                      handleUploadDocument();
                    } else if (value === null) {
                      // Exclusão de documento
                      handleDeleteDocument(docType);
                    }
                  }}
                  disabled={!hasEditPermission}
                  required={docType !== DOCUMENT_TYPES.OTHER}
                  onView={() => {
                    if (url) {
                      // Registrar no console para debug
                      console.log("Abrindo documento:", url);

                      // Apenas abrir no modal para visualização interna
                      setViewDocument(url);
                    } else {
                      toast({
                        title: "Erro",
                        description: "Não foi possível visualizar o documento",
                        variant: "destructive",
                      });
                    }
                  }}
                  actions={
                    hasEditPermission && status !== "missing" ? (
                      <div className="flex items-center">
                        {status !== "verified" && (
                          <PermissionControl permission={["players.documents.verify", "players.view_own"]}>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                // Abrir modal de upload para atualizar o documento
                                setUploadingDocumentType(docType);
                              }}
                              className="h-8 w-8 p-0 text-blue-500 hover:text-blue-700 hover:bg-blue-50 relative z-20"
                              title="Atualizar documento"
                            >
                              <Upload className="h-4 w-4" />
                            </Button>
                          </PermissionControl>
                        )}
                        {status !== "verified" && (
                          <PermissionControl permission={["players.documents.verify", "players.view_own"]}>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteDocument(docType);
                              }}
                              className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50 relative z-20"
                              title="Excluir documento"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </PermissionControl>
                        )}
                      </div>
                    ) : undefined
                  }
                />
              );
            })}
          </div>
        )}

        {/* Modal para visualizar documento */}
        <Dialog open={!!viewDocument} onOpenChange={() => setViewDocument(null)}>
          <DialogContent className="max-w-4xl max-h-[90vh]">
            <DialogHeader>
              <DialogTitle>Visualizar Documento</DialogTitle>
            </DialogHeader>
            {viewDocument && (
              <div className="mt-4 flex flex-col">
                {/* Mostrar informações do documento */}
                {(() => {
                  // Encontrar o documento atual
                  const currentDoc = documents.find(doc => doc.file_url === viewDocument);
                  if (currentDoc && currentDoc.status === "rejected" && currentDoc.rejection_reason) {
                    return (
                      <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                        <p className="font-medium text-red-700">Documento Rejeitado</p>
                        <p className="text-red-600 mt-1">
                          <span className="font-medium">Motivo da rejeição:</span> {currentDoc.rejection_reason}
                        </p>
                      </div>
                    );
                  }
                  return null;
                })()}

                <div className="mb-4 w-full flex justify-end">
                  <Button
                    variant="outline"
                    onClick={() => window.open(viewDocument, '_blank')}
                    className="mb-2"
                  >
                    Abrir no Navegador
                  </Button>
                </div>

                <div className="w-full overflow-hidden" style={{ maxHeight: "60vh" }}>
                  {viewDocument.endsWith(".pdf") ? (
                    <iframe
                      src={`https://docs.google.com/viewer?url=${encodeURIComponent(viewDocument)}&embedded=true`}
                      className="w-full h-[60vh] border-0"
                      title="Documento"
                      allowFullScreen
                    />
                  ) : (
                    <img
                      src={viewDocument}
                      alt="Documento"
                      className="max-h-[60vh] object-contain mx-auto"
                      style={{ pointerEvents: "none", display: "block" }}
                    />
                  )}
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Modal para upload de documento */}
        <Dialog
          open={!!uploadingDocumentType && !documentFile}
          onOpenChange={(open) => !open && setUploadingDocumentType(null)}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                Enviar {uploadingDocumentType && DOCUMENT_LABELS[uploadingDocumentType]}
              </DialogTitle>
            </DialogHeader>
            <div className="mt-4">
              <DocumentUpload
                documentType={uploadingDocumentType || ""}
                documentLabel={
                  uploadingDocumentType ? DOCUMENT_LABELS[uploadingDocumentType] : ""
                }
                onChange={(value, file) => {
                  if (file) {
                    setDocumentFile(file);
                    handleUploadDocument();
                  }
                }}
                required
              />
            </div>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}
