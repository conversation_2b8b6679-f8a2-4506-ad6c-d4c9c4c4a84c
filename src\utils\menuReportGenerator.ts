import { jsPDF } from "jspdf";
import 'jspdf-autotable';
import { ClubInfo } from "@/api/api";
import autoTable from "jspdf-autotable";
import { getClubPrimaryColorRgb } from '@/utils/themeUtils';

// Tipo para jsPDF com autoTable
type jsPDFWithAutoTable = jsPDF & {
  autoTable: typeof autoTable;
  lastAutoTable: {
    finalY: number;
  };
};

/**
 * Gera um relatório de cardápio semanal em PDF
 * @param clubInfo Informações do clube
 * @param weekStart Data de início da semana (opcional)
 * @param filename Nome do arquivo PDF
 */
export async function generateWeeklyMenuReport(
  clubInfo: ClubInfo,
  weekStart: Date = new Date(),
  filename: string = 'cardapio-semanal.pdf'
): Promise<Blob> {
  // Criar um novo documento PDF com orientação paisagem
  const doc = new jsPDF({
    orientation: 'landscape',
    unit: 'mm',
    format: 'a4'
  }) as unknown as jsPDFWithAutoTable;

  // Dimensões do PDF
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();

  // Adicionar título
  const title = 'Cardápio Semanal';
  doc.setFontSize(18);
  doc.text(title, 14, 22);

  // Adicionar informações do clube
  doc.setFontSize(12);
  doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, 14, 30);

  if (clubInfo.address) {
    doc.text(`Endereço: ${clubInfo.address}`, 14, 36);
  }

  if (clubInfo.phone) {
    doc.text(`Telefone: ${clubInfo.phone}`, 14, 42);
  }

  // Tentar adicionar o logo do clube, se disponível
  if (clubInfo.logo_url) {
    try {
      const img = new Image();

      // Criar uma promessa para lidar com o carregamento da imagem
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            // Calcular proporção para manter as proporções
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;

            // Adicionar a imagem ao PDF (alinhada à direita)
            doc.addImage(img, 'PNG', pageWidth - 44, 15, imgWidth, imgHeight);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });

      // Iniciar o carregamento da imagem
      img.src = clubInfo.logo_url;

      // Aguardar o carregamento e processamento da imagem
      await loadImage;
    } catch (logoError) {
      console.error("Erro ao adicionar logo ao PDF:", logoError);
    }
  }

  // Calcular datas da semana
  const weekDates = [];
  const startDate = new Date(weekStart);
  startDate.setDate(startDate.getDate() - startDate.getDay()); // Ajustar para domingo

  for (let i = 0; i < 7; i++) {
    const date = new Date(startDate);
    date.setDate(date.getDate() + i);
    weekDates.push(date.toLocaleDateString('pt-BR', { weekday: 'long', day: '2-digit', month: '2-digit' }));
  }

  // Adicionar data do relatório
  const currentDate = new Date().toLocaleDateString('pt-BR');
  doc.setFontSize(10);
  doc.text(`Data: ${currentDate}`, pageWidth - 14, 50, { align: 'right' });
  doc.text(`Semana: ${weekDates[0]} a ${weekDates[6]}`, pageWidth - 14, 55, { align: 'right' });

  // Preparar cabeçalhos da tabela
  const headers = ['Refeição', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado', 'Domingo'];

  // Preparar dados para a tabela
  const tableData = [
    ['Café da Manhã', '', '', '', '', '', '', ''],
    ['Almoço', '', '', '', '', '', '', ''],
    ['Lanche da Tarde', '', '', '', '', '', '', ''],
    ['Jantar', '', '', '', '', '', '', ''],
    ['Ceia', '', '', '', '', '', '', '']
  ];

  // Adicionar a tabela ao PDF
  autoTable(doc, {
    startY: 60,
    head: [headers],
    body: tableData,
    theme: 'grid',
    headStyles: { fillColor: getClubPrimaryColorRgb(), textColor: [255, 255, 255], fontStyle: 'bold' },
    columnStyles: {
      0: { cellWidth: 30, fontStyle: 'bold' }
    },
    styles: {
      cellPadding: 5,
      minCellHeight: 20
    },
    didDrawPage: (data) => {
      // Adicionar cabeçalho em cada página
      if (data.pageNumber > 1) {
        doc.setFontSize(10);
        doc.text(title, 14, 10);
        doc.setFontSize(8);
        doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, 14, 15);
        doc.text(`Semana: ${weekDates[0]} a ${weekDates[6]}`, 14, 20);
      }
    }
  });

  // Adicionar instruções
  const docWithTable = doc as jsPDFWithAutoTable;
  let yPosition = docWithTable.lastAutoTable.finalY + 10;

  doc.setFontSize(10);
  doc.text("Instruções: Preencha o cardápio semanal com as refeições planejadas para cada dia.", 14, yPosition);
  yPosition += 5;
  doc.text("Observações:", 14, yPosition);

  // Desenhar linhas para observações
  yPosition += 5;
  for (let i = 0; i < 3; i++) {
    doc.line(14, yPosition + (i * 5), pageWidth - 14, yPosition + (i * 5));
  }

  // Adicionar campos para aprovação
  yPosition += 20;
  doc.setFontSize(10);
  doc.text("Aprovado por:", 14, yPosition);
  doc.line(50, yPosition, 120, yPosition);

  doc.text("Data:", pageWidth - 80, yPosition);
  doc.line(pageWidth - 65, yPosition, pageWidth - 14, yPosition);

  // Adicionar rodapé
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.text(
      `Relatório gerado em ${currentDate} - Página ${i} de ${pageCount}`,
      14,
      pageHeight - 10
    );
  }

  // Retornar o PDF como um Blob
  return doc.output('blob');
}
