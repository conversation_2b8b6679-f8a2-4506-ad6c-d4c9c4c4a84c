import { jsPDF } from "jspdf";
import 'jspdf-autotable';
import { ClubInfo } from "@/api/api";
import autoTable from "jspdf-autotable";

// Tipo para jsPDF com autoTable
type jsPDFWithAutoTable = jsPDF & {
  autoTable: typeof autoTable;
  lastAutoTable: {
    finalY: number;
  };
};

/**
 * Gera um relatório médico em PDF
 * @param patientData Dados do paciente
 * @param medicalData Dados médicos
 * @param clubInfo Informações do clube
 * @param includeSignature Se deve incluir campo para assinatura digital
 * @param doctorName Nome do médico responsável
 * @param filename Nome do arquivo PDF
 */
export async function generateMedicalReport(
  patientData: any,
  medicalData: any,
  clubInfo: ClubInfo,
  includeSignature: boolean = false,
  doctorName: string = '',
  filename: string = 'relatorio-medico.pdf'
): Promise<Blob> {
  // Criar um novo documento PDF
  const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

  // Adicionar título
  const title = 'Relatório Médico';
  doc.setFontSize(18);
  doc.text(title, 14, 22);

  // Adicionar informações do clube
  doc.setFontSize(12);
  doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, 14, 30);

  if (clubInfo.address) {
    doc.text(`Endereço: ${clubInfo.address}`, 14, 36);
  }

  if (clubInfo.phone) {
    doc.text(`Telefone: ${clubInfo.phone}`, 14, 42);
  }

  // Tentar adicionar o logo do clube, se disponível
  if (clubInfo.logo_url) {
    try {
      const img = new Image();
      
      // Criar uma promessa para lidar com o carregamento da imagem
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            // Calcular proporção para manter as proporções
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;
            
            // Adicionar a imagem ao PDF (alinhada à direita)
            doc.addImage(img, 'PNG', 170, 15, imgWidth, imgHeight);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });

      // Iniciar o carregamento da imagem
      img.src = clubInfo.logo_url;

      // Aguardar o carregamento e processamento da imagem
      await loadImage;
    } catch (logoError) {
      console.error("Erro ao adicionar logo ao PDF:", logoError);
    }
  }

  // Adicionar data do relatório
  const currentDate = new Date().toLocaleDateString('pt-BR');
  doc.setFontSize(10);
  doc.text(`Data: ${currentDate}`, 170, 50, { align: 'right' });

  // Adicionar informações do paciente
  doc.setFontSize(14);
  doc.text("Informações do Paciente", 14, 60);

  // Preparar dados do paciente para a tabela
  const patientTableData = [
    ['Nome', patientData.name || '-'],
    ['Data de Nascimento', patientData.birth_date ? new Date(patientData.birth_date).toLocaleDateString('pt-BR') : '-'],
    ['Categoria', patientData.category || '-'],
    ['Posição', patientData.position || '-'],
    ['Registro', patientData.registration_number || '-']
  ];

  // Adicionar tabela de informações do paciente
  autoTable(doc, {
    startY: 65,
    body: patientTableData,
    theme: 'grid',
    styles: { fontSize: 10 },
    columnStyles: { 0: { fontStyle: 'bold', width: 40 } }
  });

  const docWithTable = doc as jsPDFWithAutoTable;
  let yPosition = docWithTable.lastAutoTable.finalY + 15;

  // Adicionar informações médicas
  doc.setFontSize(14);
  doc.text("Informações Médicas", 14, yPosition);
  yPosition += 5;

  // Preparar dados médicos para a tabela
  const medicalTableData = [
    ['Diagnóstico', medicalData.diagnosis || '-'],
    ['Tratamento', medicalData.treatment || '-'],
    ['Observações', medicalData.observations || '-'],
    ['Recomendações', medicalData.recommendations || '-']
  ];

  // Adicionar tabela de informações médicas
  autoTable(doc, {
    startY: yPosition,
    body: medicalTableData,
    theme: 'grid',
    styles: { fontSize: 10 },
    columnStyles: { 0: { fontStyle: 'bold', width: 40 } },
    rowPageBreak: 'avoid'
  });

  yPosition = docWithTable.lastAutoTable.finalY + 20;

  // Adicionar informações do médico responsável
  if (doctorName) {
    doc.setFontSize(12);
    doc.text(`Médico Responsável: ${doctorName}`, 14, yPosition);
    yPosition += 10;
  }

  // Adicionar campo para assinatura digital, se solicitado
  if (includeSignature) {
    doc.setFontSize(12);
    doc.text("Assinatura Digital:", 14, yPosition);
    yPosition += 5;

    // Desenhar linha para assinatura
    doc.setDrawColor(0);
    doc.line(14, yPosition + 20, 100, yPosition + 20);
    
    // Adicionar texto abaixo da linha
    doc.setFontSize(8);
    doc.text("Assinatura do Médico Responsável", 14, yPosition + 25);
  }

  // Adicionar rodapé
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.text(
      `Relatório gerado em ${currentDate} - Página ${i} de ${pageCount}`,
      14,
      doc.internal.pageSize.height - 10
    );
  }

  // Retornar o PDF como um Blob
  return doc.output('blob');
}
