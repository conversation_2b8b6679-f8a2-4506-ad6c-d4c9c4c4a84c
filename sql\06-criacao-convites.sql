-- Etapa 6: <PERSON><PERSON><PERSON> tabel<PERSON> de convites para novos usuários
CREATE TABLE user_invitations (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id),
  email TEXT NOT NULL,
  role TEXT NOT NULL,
  department_id INTEGER REFERENCES departments(id),
  permissions JSONB DEFAULT '{}',
  token TEXT NOT NULL,
  status TEXT DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days')
);
