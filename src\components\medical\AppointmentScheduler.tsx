import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { CalendarIcon, Clock, Plus } from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { usePlayersStore } from "@/store/usePlayersStore";
import { useMedicalAppointmentsStore } from "@/store/useMedicalAppointmentsStore";
import { getMedicalProfessionals, MedicalProfessional, getCollaborators, Collaborator } from "@/api/api";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";

interface AppointmentSchedulerProps {
  onAppointmentCreated?: () => void;
}

export function AppointmentScheduler({ onAppointmentCreated }: AppointmentSchedulerProps) {
  const { toast } = useToast();
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { players, fetchPlayers, loading: loadingPlayers } = usePlayersStore();
  const { addAppointment, loading } = useMedicalAppointmentsStore();

  const [dialogOpen, setDialogOpen] = useState(false);
  const [professionals, setProfessionals] = useState<MedicalProfessional[]>([]);
  const [loadingProfessionals, setLoadingProfessionals] = useState(false);
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [loadingCollaborators, setLoadingCollaborators] = useState(false);

  // Form state
  const [patientType, setPatientType] = useState<"player" | "collaborator">("player");
  const [playerId, setPlayerId] = useState("");
  const [professionalId, setProfessionalId] = useState("");
  const [appointmentType, setAppointmentType] = useState("Consulta");
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [time, setTime] = useState("08:00");
  const [duration, setDuration] = useState("30");
  const [location, setLocation] = useState("");
  const [notes, setNotes] = useState("");

  // Load players, collaborators and professionals when component mounts
  useEffect(() => {
    if (clubId) {
      if (players.length === 0 && !loadingPlayers) {
        fetchPlayers(clubId, undefined, { includeInactive: false, includeLoaned: false });
      }
      loadProfessionals();
      loadCollaborators();
    }
  }, [clubId, fetchPlayers, players.length, loadingPlayers]);

  // Update location when professional changes
  useEffect(() => {
    if (professionalId) {
      const selectedProfessional = professionals.find(p => p.id === Number(professionalId));
      if (selectedProfessional && selectedProfessional.address) {
        setLocation(selectedProfessional.address);
      }
    }
  }, [professionalId, professionals]);

  // Clear patient selection when patient type changes
  useEffect(() => {
    setPlayerId("");
  }, [patientType]);

  // Load medical professionals
  const loadProfessionals = async () => {
    try {
      setLoadingProfessionals(true);
      const data = await getMedicalProfessionals(clubId);
      setProfessionals(data);
    } catch (error) {
      console.error("Erro ao carregar profissionais médicos:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar os profissionais médicos",
        variant: "destructive",
      });
    } finally {
      setLoadingProfessionals(false);
    }
  };

  // Load collaborators
  const loadCollaborators = async () => {
    try {
      setLoadingCollaborators(true);
      const data = await getCollaborators(clubId);
      // Filtrar apenas colaboradores ativos
      const activeCollaborators = data.filter(collab => collab.status !== 'inactive');
      setCollaborators(activeCollaborators);
    } catch (error) {
      console.error("Erro ao carregar colaboradores:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar os colaboradores",
        variant: "destructive",
      });
    } finally {
      setLoadingCollaborators(false);
    }
  };

  // Reset form
  const resetForm = () => {
    setPatientType("player");
    setPlayerId("");
    setProfessionalId("");
    setAppointmentType("Consulta");
    setDate(new Date());
    setTime("08:00");
    setDuration("30");
    setLocation("");
    setNotes("");
  };

  // Get current patient list based on selected type
  const getCurrentPatients = () => {
    if (patientType === "player") {
      return players.sort((a, b) => a.name.localeCompare(b.name));
    } else {
      return collaborators.sort((a, b) => a.full_name.localeCompare(b.full_name));
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!playerId || !professionalId || !date || !time) {
      toast({
        title: "Campos obrigatórios",
        description: "Por favor, preencha todos os campos obrigatórios",
        variant: "destructive",
      });
      return;
    }

    try {
      // Format date and time
      const formattedDate = format(date, "yyyy-MM-dd");

      // Get professional details for location
      const professional = professionals.find(p => p.id === Number(professionalId));
      const appointmentLocation = location || professional?.address || "";

      await addAppointment(clubId, user?.id || "", {
        player_id: playerId,
        professional_id: Number(professionalId),
        appointment_date: formattedDate,
        appointment_time: time,
        duration: Number(duration),
        status: "Agendada",
        appointment_type: appointmentType,
        location: appointmentLocation,
        notes,
        created_by: user?.id
      });

      toast({
        title: "Agendamento criado",
        description: "O agendamento foi criado com sucesso",
      });

      resetForm();
      setDialogOpen(false);
      if (onAppointmentCreated) {
        onAppointmentCreated();
      }
    } catch (error) {
      console.error("Erro ao criar agendamento:", error);
      toast({
        title: "Erro",
        description: "Não foi possível criar o agendamento",
        variant: "destructive",
      });
    }
  };

  return (
    <>
      <Button onClick={() => setDialogOpen(true)} className="bg-team-blue hover:bg-blue-700">
        <Plus className="h-4 w-4 mr-2" />
        Novo Agendamento
      </Button>

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Novo Agendamento</DialogTitle>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="patientType" className="text-right">
                Tipo de Paciente*
              </Label>
              <Select value={patientType} onValueChange={(value: "player" | "collaborator") => setPatientType(value)}>
                <SelectTrigger id="patientType" className="col-span-3">
                  <SelectValue placeholder="Selecione o tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="player">Atleta</SelectItem>
                  <SelectItem value="collaborator">Colaborador</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="patient" className="text-right">
                {patientType === "player" ? "Atleta*" : "Colaborador*"}
              </Label>
              <Select value={playerId} onValueChange={setPlayerId}>
                <SelectTrigger id="patient" className="col-span-3">
                  <SelectValue placeholder={`Selecione ${patientType === "player" ? "um atleta" : "um colaborador"}`} />
                </SelectTrigger>
                <SelectContent>
                  {patientType === "player"
                    ? players.sort((a, b) => a.name.localeCompare(b.name)).map((player) => (
                        <SelectItem key={player.id} value={player.id}>
                          {player.name}
                        </SelectItem>
                      ))
                    : collaborators.sort((a, b) => a.full_name.localeCompare(b.full_name)).map((collaborator) => (
                        <SelectItem key={collaborator.id} value={collaborator.id.toString()}>
                          {collaborator.full_name} - {collaborator.role}
                        </SelectItem>
                      ))
                  }
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="professional" className="text-right">
                Profissional*
              </Label>
              <Select value={professionalId} onValueChange={setProfessionalId}>
                <SelectTrigger id="professional" className="col-span-3">
                  <SelectValue placeholder="Selecione um profissional" />
                </SelectTrigger>
                <SelectContent>
                  {professionals.map((professional) => (
                    <SelectItem key={professional.id} value={professional.id.toString()}>
                      {professional.name} ({professional.role})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="type" className="text-right">
                Tipo*
              </Label>
              <Select value={appointmentType} onValueChange={setAppointmentType}>
                <SelectTrigger id="type" className="col-span-3">
                  <SelectValue placeholder="Selecione o tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Consulta">Consulta</SelectItem>
                  <SelectItem value="Fisioterapia">Fisioterapia</SelectItem>
                  <SelectItem value="Avaliação">Avaliação</SelectItem>
                  <SelectItem value="Retorno">Retorno</SelectItem>
                  <SelectItem value="Exame">Exame</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="date" className="text-right">
                Data*
              </Label>
              <div className="col-span-3">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !date && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {date ? format(date, "PPP", { locale: ptBR }) : <span>Selecione uma data</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={date}
                      onSelect={setDate}
                      initialFocus
                      locale={ptBR}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="time" className="text-right">
                Horário*
              </Label>
              <div className="col-span-3 flex items-center">
                <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="time"
                  type="time"
                  value={time}
                  onChange={(e) => setTime(e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="duration" className="text-right">
                Duração (min)
              </Label>
              <Select value={duration} onValueChange={setDuration}>
                <SelectTrigger id="duration" className="col-span-3">
                  <SelectValue placeholder="Selecione a duração" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="15">15 minutos</SelectItem>
                  <SelectItem value="30">30 minutos</SelectItem>
                  <SelectItem value="45">45 minutos</SelectItem>
                  <SelectItem value="60">1 hora</SelectItem>
                  <SelectItem value="90">1 hora e 30 minutos</SelectItem>
                  <SelectItem value="120">2 horas</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="location" className="text-right">
                Local
              </Label>
              <Input
                id="location"
                placeholder="Local do atendimento"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="notes" className="text-right">
                Observações
              </Label>
              <Input
                id="notes"
                placeholder="Observações sobre o agendamento"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                className="col-span-3"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleSubmit} disabled={loading}>
              {loading ? "Agendando..." : "Agendar"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
