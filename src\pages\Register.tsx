/*  */import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { createUserDirectly } from "@/api/directAuth";
import { supabase } from "@/api/supabaseClient";

export default function Register() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [name, setName] = useState("");
  const [clubName, setClubName] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const navigate = useNavigate();

  async function handleRegister(e: React.FormEvent) {
    e.preventDefault();
    setLoading(true);
    setError("");
    try {
      console.log("Iniciando processo de registro com Edge Function");

      // 1. Criar o clube na tabela club_info
      const { data: club, error: clubError } = await supabase
        .from("club_info")
        .insert([{ name: clubName }])
        .select()
        .single();

      if (clubError || !club) {
        console.error("Erro ao criar clube:", clubError);
        throw new Error("Erro ao criar clube: " + (clubError?.message || ""));
      }

      console.log("Clube criado com sucesso:", club.id);

      // 2. Criar o usuário usando a Edge Function
      console.log("Chamando createUserDirectly com:", {
        email,
        name,
        clubName,
        role: "admin",
        clubId: club.id
      });

      const result = await createUserDirectly(
        email,
        name,
        clubName,
        "admin",
        club.id,
        password
      );

      console.log("Resultado da chamada createUserDirectly:", result);

      if (!result.success || !result.userId) {
        console.error("Erro ao criar usuário:", result.message);
        throw new Error(result.message || "Erro ao criar usuário");
      }

      console.log("Usuário criado com sucesso:", result.userId);

      // 3. Adicionar manualmente o usuário ao clube (caso a Edge Function não tenha feito isso)
      console.log("Adicionando usuário ao clube manualmente...");
      try {
        // Usar SQL direto para garantir que a inserção funcione
        const { data: insertResult, error: insertError } = await supabase.rpc(
          'add_user_to_club',
          {
            p_user_id: result.userId,
            p_club_id: club?.id || 0,
            p_role: 'admin',
            p_status: 'ativo'
          }
        );

        if (insertError) {
          console.error("Erro ao adicionar usuário ao clube via RPC:", insertError);

          // Tentar inserção direta via SQL como fallback
          console.log("Tentando inserção direta via SQL...");
          const { data: sqlResult, error: sqlError } = await supabase.rpc(
            'execute_sql',
            {
              sql_query: `INSERT INTO club_members (user_id, club_id, role, status)
                         VALUES ('${result.userId}', ${club?.id || 0}, 'admin', 'ativo')
                         ON CONFLICT (user_id, club_id) DO NOTHING
                         RETURNING id;`
            }
          );

          if (sqlError) {
            console.error("Erro na inserção direta via SQL:", sqlError);
          } else {
            console.log("Resultado da inserção direta via SQL:", sqlResult);
          }
        } else {
          console.log("Usuário adicionado ao clube com sucesso via RPC:", insertResult);
        }
      } catch (memberError) {
        console.error("Exceção ao adicionar usuário ao clube manualmente:", memberError);
        // Não lançamos erro aqui para não interromper o fluxo principal
      }

      // 4. Salvar clubId e userId
      localStorage.setItem("clubId", String(club?.id || ""));
      localStorage.setItem("userId", result.userId || "");

      setLoading(false);
      navigate("/login");
    } catch (err) {
      console.error("Erro no processo de registro:", err);
      setError(err instanceof Error ? err.message : "Erro ao criar conta. Tente novamente.");
      setLoading(false);
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary to-primary/60">
      <Card className="w-full max-w-md shadow-xl">
        <CardHeader>
          <CardTitle>Criar Conta</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleRegister} className="space-y-4">
            <div>
              <label htmlFor="name">Seu nome</label>
              <Input
                id="name"
                type="text"
                value={name}
                onChange={e => setName(e.target.value)}
                required
                placeholder="Seu nome"
              />
            </div>
            <div>
              <label htmlFor="clubName">Nome do clube</label>
              <Input
                id="clubName"
                type="text"
                value={clubName}
                onChange={e => setClubName(e.target.value)}
                required
                placeholder="Nome do clube"
              />
            </div>
            <div>
              <label htmlFor="email">E-mail</label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={e => setEmail(e.target.value)}
                required
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label htmlFor="password">Senha</label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={e => setPassword(e.target.value)}
                required
                placeholder="Senha"
              />
            </div>
            {error && <div className="text-red-500 text-sm">{error}</div>}
            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? "Criando..." : "Criar Conta"}
            </Button>
            <div className="text-center text-sm mt-2">
              Já tem uma conta?{' '}
              <span className="text-primary hover:underline cursor-pointer" onClick={() => navigate("/login")}>Entrar</span>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
