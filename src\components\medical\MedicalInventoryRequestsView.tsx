import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { FileText, Eye } from "lucide-react";
import { getInventoryRequests, getInventoryRequestItems, getMedicalProfessionalByUserId } from "@/api/api";
import { useUser } from "@/context/UserContext";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";

interface MedicalInventoryRequestsViewProps {
  clubId: number;
}

export function MedicalInventoryRequestsView({ clubId }: MedicalInventoryRequestsViewProps) {
  const [requests, setRequests] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRequest, setSelectedRequest] = useState<any>(null);
  const [requestItems, setRequestItems] = useState<any[]>([]);
  const [loadingItems, setLoadingItems] = useState(false);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const { user } = useUser();
  const { toast } = useToast();

  // Load requests when component mounts
  useEffect(() => {
    if (user?.id) {
      loadRequests();
    }
  }, [clubId, user?.id]);

  // Load requests from API
  const loadRequests = async () => {
    try {
      setLoading(true);

      // Get the medical professional ID
      const medicalProfessional = await getMedicalProfessionalByUserId(clubId, user?.id || "");

      if (!medicalProfessional) {
        toast({
          title: "Erro",
          description: "Não foi possível identificar o profissional médico.",
          variant: "destructive",
        });
        setLoading(false);
        return;
      }

      // Get all requests
      const allRequests = await getInventoryRequests(clubId);

      // Filter requests by medical professional ID
      const medicalRequests = allRequests.filter(
        req => req.requester_type === "medical" && req.requester_id === medicalProfessional.id.toString()
      );

      // Sort by created_at (newest first)
      medicalRequests.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

      setRequests(medicalRequests);
    } catch (error) {
      console.error("Error loading requests:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao carregar as solicitações. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // View request details
  const handleViewRequest = async (request: any) => {
    try {
      setSelectedRequest(request);
      setViewDialogOpen(true);
      setLoadingItems(true);

      const items = await getInventoryRequestItems(clubId, request.id);
      setRequestItems(items);
    } catch (error) {
      console.error("Error loading request items:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao carregar os itens da solicitação.",
        variant: "destructive",
      });
    } finally {
      setLoadingItems(false);
    }
  };

  // Generate a report of requests
  const generateReport = () => {
    // Create a new window for the report
    const reportWindow = window.open("", "_blank");
    if (!reportWindow) {
      toast({
        title: "Erro",
        description: "Não foi possível abrir uma nova janela para o relatório. Verifique se o bloqueador de pop-ups está desativado.",
        variant: "destructive",
      });
      return;
    }

    // Generate the report HTML
    const reportHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Relatório de Solicitações - Departamento Médico</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          h1 { color: #2563eb; }
          table { width: 100%; border-collapse: collapse; margin-top: 20px; }
          th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
          th { background-color: #f1f5f9; }
          .pending { color: #ca8a04; }
          .approved { color: #2563eb; }
          .rejected { color: #dc2626; }
          .completed { color: #16a34a; }
          .date { margin-bottom: 20px; color: #64748b; }
          .footer { margin-top: 30px; font-size: 12px; color: #64748b; text-align: center; }
        </style>
      </head>
      <body>
        <h1>Relatório de Solicitações - Departamento Médico</h1>
        <div class="date">Data: ${format(new Date(), "PPP", { locale: ptBR })}</div>

        <table>
          <thead>
            <tr>
              <th>ID</th>
              <th>Data da Solicitação</th>
              <th>Data de Retirada</th>
              <th>Status</th>
              <th>Observações</th>
            </tr>
          </thead>
          <tbody>
            ${requests.map(request => `
              <tr class="${request.status}">
                <td>${request.id}</td>
                <td>${format(new Date(request.created_at), "dd/MM/yyyy")}</td>
                <td>${format(new Date(request.withdrawal_date), "dd/MM/yyyy")}</td>
                <td>${
                  request.status === "pending" ? "Pendente" :
                  request.status === "approved" ? "Aprovado" :
                  request.status === "rejected" ? "Rejeitado" :
                  "Concluído"
                }</td>
                <td>${request.requester_notes || "-"}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <div class="footer">
          Relatório gerado em ${format(new Date(), "PPP 'às' HH:mm", { locale: ptBR })}
        </div>
      </body>
      </html>
    `;

    // Write the report to the new window
    reportWindow.document.write(reportHtml);
    reportWindow.document.close();
  };

  return (
    <>
      <Card>
        <CardHeader className="pb-0 flex flex-col sm:flex-row justify-between">
          <div>
            <CardTitle>Minhas Solicitações</CardTitle>
            <CardDescription>
              Acompanhe suas solicitações de medicamentos
            </CardDescription>
          </div>
          <div className="flex justify-end mb-4">
            <Button variant="outline" onClick={generateReport}>
              <FileText className="h-4 w-4 mr-2" />
              Relatório
            </Button>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          {loading ? (
            <div className="text-center py-8">Carregando solicitações...</div>
          ) : requests.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              Nenhuma solicitação encontrada.
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>Data da Solicitação</TableHead>
                    <TableHead>Data de Retirada</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {requests.map((request) => (
                    <TableRow key={request.id}>
                      <TableCell>{request.id}</TableCell>
                      <TableCell>{format(new Date(request.created_at), "dd/MM/yyyy")}</TableCell>
                      <TableCell>{format(new Date(request.withdrawal_date), "dd/MM/yyyy")}</TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={
                            request.status === "pending"
                              ? "border-yellow-200 bg-yellow-50 text-yellow-800"
                              : request.status === "approved"
                              ? "border-primary/20 bg-primary/10 text-primary"
                              : request.status === "rejected"
                              ? "border-red-200 bg-red-50 text-red-800"
                              : "border-green-200 bg-green-50 text-green-800"
                          }
                        >
                          {request.status === "pending"
                            ? "Pendente"
                            : request.status === "approved"
                            ? "Aprovado"
                            : request.status === "rejected"
                            ? "Rejeitado"
                            : "Concluído"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleViewRequest(request)}
                          title="Ver detalhes"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Request Details Dialog */}
      {selectedRequest && (
        <Dialog open={viewDialogOpen} onOpenChange={setViewDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Detalhes da Solicitação #{selectedRequest.id}</DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium">Data da Solicitação</p>
                  <p>{format(new Date(selectedRequest.created_at), "dd/MM/yyyy")}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Data de Retirada</p>
                  <p>{format(new Date(selectedRequest.withdrawal_date), "dd/MM/yyyy")}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Status</p>
                  <Badge
                    variant="outline"
                    className={
                      selectedRequest.status === "pending"
                        ? "border-yellow-200 bg-yellow-50 text-yellow-800"
                        : selectedRequest.status === "approved"
                        ? "border-primary/20 bg-primary/10 text-primary"
                        : selectedRequest.status === "rejected"
                        ? "border-red-200 bg-red-50 text-red-800"
                        : "border-green-200 bg-green-50 text-green-800"
                    }
                  >
                    {selectedRequest.status === "pending"
                      ? "Pendente"
                      : selectedRequest.status === "approved"
                      ? "Aprovado"
                      : selectedRequest.status === "rejected"
                      ? "Rejeitado"
                      : "Concluído"}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm font-medium">Observações</p>
                  <p>{selectedRequest.requester_notes || "-"}</p>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Itens Solicitados</h3>
                {loadingItems ? (
                  <div className="text-center py-4">Carregando itens...</div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Medicamento</TableHead>
                        <TableHead>Quantidade</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {requestItems.map((item) => (
                        <TableRow key={item.id}>
                          <TableCell>{item.product_name}</TableCell>
                          <TableCell>{item.quantity}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setViewDialogOpen(false)}>
                Fechar
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}
