import { supabase } from "@/integrations/supabase/client";
import { Player } from "@/api/api";

/**
 * Verifica e atualiza o status de jogadores emprestados cujo empréstimo terminou
 * @param clubId ID do clube
 * @returns Número de jogadores atualizados
 */
export async function checkAndUpdateLoanedPlayers(clubId: number): Promise<number> {
  try {
    // Buscar jogadores emprestados cujo empréstimo terminou
    const now = new Date().toISOString();
    const { data, error } = await supabase
      .from("players")
      .select("*")
      .eq("club_id", clubId as any)
      .eq("status", "emprestado")
      .lt("loan_end_date", now);

    if (error) {
      console.error("Erro ao buscar jogadores emprestados:", error);
      return 0;
    }

    if (!data || data.length === 0) {
      return 0; // Nenhum jogador para atualizar
    }

    // Atualizar o status dos jogadores para "disponivel"
    const playerIds = data.map(player => player.id);
    const { error: updateError, count } = await supabase
      .from("players")
      .update({
        status: "disponivel",
        loan_end_date: null,
        loan_club_name: null
      })
      .eq("club_id", clubId as any)
      .in("id", playerIds);

    if (updateError) {
      console.error("Erro ao atualizar status dos jogadores:", updateError);
      return 0;
    }

    // Registrar no log
    console.log(`${count} jogadores retornaram de empréstimo automaticamente`);
    
    // Registrar no log de auditoria, se necessário
    if (count > 0) {
      try {
        await supabase
          .from("audit_logs")
          .insert({
            club_id: clubId,
            user_id: null, // Sistema
            action: "player.loan_return",
            details: {
              player_count: count,
              player_ids: playerIds,
              automatic: true
            }
          });
      } catch (auditError) {
        console.error("Erro ao registrar no log de auditoria:", auditError);
      }
    }

    return count || 0;
  } catch (error) {
    console.error("Erro ao verificar jogadores emprestados:", error);
    return 0;
  }
}
