import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";

// Tipos
export type HotelRoom = Database["public"]["Tables"]["hotel_rooms"]["Row"];

// Funções para gerenciar quartos de hotel
export async function getHotelRooms(clubId: number, accommodationId: number): Promise<HotelRoom[]> {
  const { data, error } = await supabase
    .from("hotel_rooms")
    .select("*")
    .eq("club_id", clubId as any)
    .eq("accommodation_id", accommodationId as any)
    .order("room_number");

  if (error) {
    console.error("Erro ao buscar quartos do hotel:", error);
    throw new Error(`Erro ao buscar quartos do hotel: ${error.message}`);
  }

  if (!data) {
    return [];
  }

  return data as unknown as HotelRoom[];
}

export async function getHotelRoomById(clubId: number, id: number): Promise<HotelRoom> {
  const { data, error } = await supabase
    .from("hotel_rooms")
    .select("*")
    .eq("club_id", clubId as any)
    .eq("id", id as any)
    .single();

  if (error) {
    console.error(`Erro ao buscar quarto ${id}:`, error);
    throw new Error(`Erro ao buscar quarto: ${error.message}`);
  }

  if (!data) {
    throw new Error(`Quarto não encontrado: ${id}`);
  }

  return data as unknown as HotelRoom;
}

export async function createHotelRoom(
  clubId: number,
  accommodationId: number,
  room: { room_number: string; capacity: number }
): Promise<HotelRoom> {
  // Usar o tipo any para contornar problemas de tipagem do Supabase
  const { data, error } = await supabase
    .from("hotel_rooms")
    .insert({
      club_id: clubId,
      accommodation_id: accommodationId,
      room_number: room.room_number,
      capacity: room.capacity
    } as any)
    .select()
    .single();

  if (error) {
    console.error("Erro ao criar quarto:", error);
    throw new Error(`Erro ao criar quarto: ${error.message}`);
  }

  if (!data) {
    throw new Error("Erro ao criar quarto: Nenhum dado retornado");
  }

  return data as unknown as HotelRoom;
}

export async function updateHotelRoom(
  clubId: number,
  id: number,
  room: Partial<{ room_number: string; capacity: number }>
): Promise<HotelRoom> {
  // Usar o tipo any para contornar problemas de tipagem do Supabase
  const updateData = {
    ...(room.room_number !== undefined ? { room_number: room.room_number } : {}),
    ...(room.capacity !== undefined ? { capacity: room.capacity } : {})
  };

  const { data, error } = await supabase
    .from("hotel_rooms")
    .update(updateData as any)
    .eq("club_id", clubId as any)
    .eq("id", id as any)
    .select()
    .single();

  if (error) {
    console.error(`Erro ao atualizar quarto ${id}:`, error);
    throw new Error(`Erro ao atualizar quarto: ${error.message}`);
  }

  if (!data) {
    throw new Error(`Erro ao atualizar quarto: Nenhum dado retornado`);
  }

  return data as unknown as HotelRoom;
}

export async function deleteHotelRoom(clubId: number, id: number): Promise<boolean> {
  // Primeiro, verificar se há jogadores associados a este quarto
  const { data: playerAccommodations } = await supabase
    .from("player_accommodations")
    .select("id")
    .eq("hotel_room_id", id as any)
    .eq("club_id", clubId as any);

  if (playerAccommodations && playerAccommodations.length > 0) {
    throw new Error("Não é possível excluir um quarto com jogadores associados");
  }

  const { error } = await supabase
    .from("hotel_rooms")
    .delete()
    .eq("club_id", clubId as any)
    .eq("id", id as any);

  if (error) {
    console.error(`Erro ao excluir quarto ${id}:`, error);
    throw new Error(`Erro ao excluir quarto: ${error.message}`);
  }

  return true;
}

// Função para verificar a disponibilidade de um quarto
export async function getRoomAvailability(clubId: number, roomId: number): Promise<{
  capacity: number;
  occupied: number;
  available: number;
  players: { id: string; name: string }[];
}> {
  // Buscar informações do quarto
  const { data: room, error: roomError } = await supabase
    .from("hotel_rooms")
    .select("capacity")
    .eq("club_id", clubId as any)
    .eq("id", roomId as any)
    .single();

  if (roomError) {
    console.error(`Erro ao buscar informações do quarto ${roomId}:`, roomError);
    throw new Error(`Erro ao buscar informações do quarto: ${roomError.message}`);
  }

  // Buscar jogadores no quarto
  const { data: players, error: playersError } = await supabase
    .from("player_accommodations")
    .select(`
      player_id,
      players!player_accommodations_player_id_fkey(id, name)
    `)
    .eq("club_id", clubId as any)
    .eq("hotel_room_id", roomId as any)
    .eq("status", "active" as any);

  if (playersError) {
    console.error(`Erro ao buscar jogadores do quarto ${roomId}:`, playersError);
    throw new Error(`Erro ao buscar jogadores do quarto: ${playersError.message}`);
  }

  const occupiedCount = players?.length || 0;
  const capacity = room ? (room as any).capacity || 0 : 0;
  const available = capacity - occupiedCount;

  // Formatar a lista de jogadores
  const playersList = (players || []).map(item => {
    const playerItem = item as any;
    return {
      id: playerItem.player_id || "",
      name: playerItem.players?.name || "Jogador sem nome"
    };
  });

  return {
    capacity,
    occupied: occupiedCount,
    available,
    players: playersList
  };
}

// Função para listar todos os quartos disponíveis de um hotel
export async function getAvailableRooms(clubId: number, accommodationId: number): Promise<{
  id: number;
  room_number: string;
  capacity: number;
  occupied: number;
  available: number;
}[]> {
  // Buscar todos os quartos do hotel
  const { data: rooms, error: roomsError } = await supabase
    .from("hotel_rooms")
    .select("*")
    .eq("club_id", clubId as any)
    .eq("accommodation_id", accommodationId as any)
    .order("room_number");

  if (roomsError) {
    console.error(`Erro ao buscar quartos do hotel ${accommodationId}:`, roomsError);
    throw new Error(`Erro ao buscar quartos do hotel: ${roomsError.message}`);
  }

  if (!rooms || rooms.length === 0) {
    return [];
  }

  // Buscar ocupação de cada quarto
  // Como não podemos usar GROUP BY diretamente, vamos buscar todos os registros
  // e fazer o agrupamento manualmente
  const { data: playerAccommodations, error: occupancyError } = await supabase
    .from("player_accommodations")
    .select("hotel_room_id")
    .eq("club_id", clubId as any)
    .eq("accommodation_id", accommodationId as any)
    .eq("status", "active" as any);

  if (occupancyError) {
    console.error(`Erro ao buscar ocupação dos quartos:`, occupancyError);
    throw new Error(`Erro ao buscar ocupação dos quartos: ${occupancyError.message}`);
  }

  // Criar um mapa de ocupação por quarto manualmente
  const occupancyMap = new Map<number, number>();

  // Contar manualmente as ocorrências de cada quarto
  if (playerAccommodations) {
    (playerAccommodations as any[]).forEach(item => {
      if (item && item.hotel_room_id) {
        const currentCount = occupancyMap.get(item.hotel_room_id) || 0;
        occupancyMap.set(item.hotel_room_id, currentCount + 1);
      }
    });
  }

  // Formatar o resultado
  return (rooms as any[]).map(room => {
    const occupied = occupancyMap.get(room.id) || 0;
    const available = room.capacity - occupied;

    return {
      id: room.id,
      room_number: room.room_number,
      capacity: room.capacity,
      occupied,
      available
    };
  });
}

// Função para criar vários quartos de uma vez
export async function createMultipleRooms(
  clubId: number,
  accommodationId: number,
  rooms: { room_number: string; capacity: number }[]
): Promise<HotelRoom[]> {
  if (!rooms || rooms.length === 0) {
    return [];
  }

  const roomsToInsert = rooms.map(room => ({
    club_id: clubId,
    accommodation_id: accommodationId,
    room_number: room.room_number,
    capacity: room.capacity
  }));

  const { data, error } = await supabase
    .from("hotel_rooms")
    .insert(roomsToInsert as any)
    .select();

  if (error) {
    console.error("Erro ao criar quartos:", error);
    throw new Error(`Erro ao criar quartos: ${error.message}`);
  }

  if (!data) {
    return [];
  }

  // Converter para o tipo esperado
  return data as unknown as HotelRoom[];
}
