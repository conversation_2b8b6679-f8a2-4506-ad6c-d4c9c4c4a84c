import { <PERSON>, CardContent, <PERSON><PERSON>eader, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar, AlertCircle } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { differenceInDays, parseISO } from "date-fns";
import type { Player } from "@/api/api";

interface ExpiringContractsWidgetProps {
  players: Player[];
  daysThreshold?: number;
}

export function ExpiringContractsWidget({ players, daysThreshold = 30 }: ExpiringContractsWidgetProps) {
  const navigate = useNavigate();
  
  // Filtrar jogadores com contratos prestes a vencer
  const today = new Date();
  const expiringContracts = players
    .filter(player => player.contract_end_date)
    .map(player => ({
      ...player,
      daysRemaining: differenceInDays(parseISO(player.contract_end_date!), today)
    }))
    .filter(player => player.daysRemaining >= 0 && player.daysRemaining <= daysThreshold)
    .sort((a, b) => a.daysRemaining - b.daysRemaining);

  const handleViewPlayer = (playerId: string) => {
    navigate(`/jogador/${playerId}`);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-md font-medium">Contratos a Vencer</CardTitle>
        <AlertCircle className="h-4 w-4 text-amber-500" />
      </CardHeader>
      <CardContent>
        {expiringContracts.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground">
            <Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>Nenhum contrato prestes a vencer nos próximos {daysThreshold} dias.</p>
          </div>
        ) : (
          <div className="space-y-3">
            {expiringContracts.map(player => (
              <div 
                key={player.id} 
                className="flex items-center justify-between p-2 bg-muted/20 rounded-lg hover:bg-muted/40 transition-colors"
              >
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${
                    player.daysRemaining <= 7 ? 'bg-red-500' : 
                    player.daysRemaining <= 15 ? 'bg-amber-500' : 'bg-yellow-300'
                  }`} />
                  <div>
                    <p className="font-medium text-sm">{player.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {player.daysRemaining === 0 
                        ? 'Vence hoje' 
                        : player.daysRemaining === 1 
                          ? 'Vence amanhã' 
                          : `Vence em ${player.daysRemaining} dias`
                      }
                    </p>
                  </div>
                </div>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => handleViewPlayer(player.id)}
                  className="text-xs"
                >
                  Ver
                </Button>
              </div>
            ))}
            
            {expiringContracts.length > 5 && (
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full mt-2"
                onClick={() => navigate('/elenco')}
              >
                Ver todos ({expiringContracts.length})
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
