import { jsPD<PERSON> } from "jspdf";
import autoTable from 'jspdf-autotable';
import { InventoryRequest, InventoryRequestItem, ClubInfo } from "@/api/api";
import { getClubPrimaryColorRgb } from '@/utils/themeUtils';

// Type for jsPDF with autoTable
type jsPDFWithAutoTable = jsPDF & {
  autoTable: typeof autoTable;
  lastAutoTable: {
    finalY: number;
  };
}

/**
 * Generates a PDF report for an inventory request
 * @param request The inventory request
 * @param items The inventory request items
 * @param clubInfo Club information
 * @param filename Optional filename for the PDF
 */
export async function generateInventoryRequestReport(
  request: InventoryRequest,
  items: InventoryRequestItem[],
  clubInfo: ClubInfo,
  filename: string = 'solicitacao-estoque.pdf'
): Promise<Blob> {
  // Create a new PDF document
  const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

  // Add title
  const title = `Solicitação de Estoque #${request.id}`;
  doc.setFontSize(18);
  doc.text(title, 14, 22);

  // Add club information
  doc.setFontSize(12);
  doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, 14, 30);

  if (clubInfo.address) {
    doc.text(`Endereço: ${clubInfo.address}`, 14, 36);
  }

  if (clubInfo.phone) {
    doc.text(`Telefone: ${clubInfo.phone}`, 14, 42);
  }

  // Try to add club logo if available
  if (clubInfo.logo_url) {
    try {
      const img = new Image();

      // Create a promise to handle image loading
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            // Calculate aspect ratio to maintain proportions
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;

            // Add the image to the PDF (right-aligned)
            doc.addImage(img, 'PNG', 170, 15, imgWidth, imgHeight);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });

      // Start loading the image
      img.src = clubInfo.logo_url;

      // Wait for the image to load and be processed
      await loadImage;
    } catch (logoError) {
      console.error("Error adding logo to PDF:", logoError);
    }
  }

  // Add request information
  let yPosition = 50;
  doc.setFontSize(11);
  doc.text(`Departamento Solicitante: ${request.department_name || '-'}`, 14, yPosition);
  yPosition += 6;
  doc.text(`Solicitado por: ${request.requester_name || '-'}`, 14, yPosition);
  yPosition += 6;
  doc.text(`Categoria: ${request.category || '-'}`, 14, yPosition);
  yPosition += 6;
  doc.text(`Data de Retirada: ${new Date(request.withdrawal_date).toLocaleDateString('pt-BR')}`, 14, yPosition);
  yPosition += 6;
  doc.text(`Status: ${getStatusLabel(request.status)}`, 14, yPosition);
  yPosition += 6;
  doc.text(`Método de Entrega: ${request.delivery_method === 'pickup' ? 'Retirada' : 'Entrega'}`, 14, yPosition);

  if (request.delivery_method === 'delivery' && request.delivery_location) {
    yPosition += 6;
    doc.text(`Local de Entrega: ${request.delivery_location}`, 14, yPosition);
  }

  // Add items table
  yPosition += 15;

  // Prepare data for the table
  const tableData = items.map(item => [
    item.product_name || '-',
    item.product_department || '-',
    item.product_location || '-',
    item.quantity.toString(),
    item.returned_quantity > 0 ? item.returned_quantity.toString() : '-',
    (item.quantity - item.returned_quantity).toString()
  ]);

  // Add the table to the PDF
  autoTable(doc, {
    startY: yPosition,
    head: [['Produto', 'Departamento', 'Localização', 'Quantidade', 'Devolvido', 'Total']],
    body: tableData,
    theme: 'striped',
    headStyles: { fillColor: getClubPrimaryColorRgb() },
    didDrawPage: (data) => {
      // Add header on each page
      if (data.pageNumber > 1) {
        doc.setFontSize(10);
        doc.text(title, 14, 10);
        doc.setFontSize(8);
        doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, 14, 15);
      }
    }
  });

  // Get the Y position after the table
  yPosition = doc.lastAutoTable.finalY + 15;

  // Add notes if available
  if (request.requester_notes) {
    doc.setFontSize(11);
    doc.text('Observações do Solicitante:', 14, yPosition);
    yPosition += 6;
    doc.setFontSize(10);

    // Split long notes into multiple lines
    const splitNotes = doc.splitTextToSize(request.requester_notes, 180);
    doc.text(splitNotes, 14, yPosition);
    yPosition += (splitNotes.length * 5) + 10;
  }

  if (request.delivery_notes) {
    doc.setFontSize(11);
    doc.text('Observações de Entrega:', 14, yPosition);
    yPosition += 6;
    doc.setFontSize(10);

    // Split long notes into multiple lines
    const splitNotes = doc.splitTextToSize(request.delivery_notes, 180);
    doc.text(splitNotes, 14, yPosition);
    yPosition += (splitNotes.length * 5) + 10;
  }

  // Add signature areas
  doc.setFontSize(11);
  doc.text('Assinaturas:', 14, yPosition);
  yPosition += 10;

  // Add requester signature if available
  if (request.requester_signature_url) {
    try {
      const img = new Image();

      // Create a promise to handle image loading
      const loadSignature = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            // Add the signature image
            doc.addImage(img, 'PNG', 30, yPosition, 60, 30);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });

      // Start loading the image
      img.src = request.requester_signature_url;

      // Wait for the image to load and be processed
      await loadSignature;

      doc.text('Assinatura do Solicitante', 40, yPosition + 40);
    } catch (signatureError) {
      console.error("Error adding requester signature to PDF:", signatureError);
      // Add a placeholder for signature
      doc.text('____________________________', 30, yPosition + 20);
      doc.text('Assinatura do Solicitante', 40, yPosition + 30);
    }
  } else {
    // Add a placeholder for signature
    doc.text('____________________________', 30, yPosition + 20);
    doc.text('Assinatura do Solicitante', 40, yPosition + 30);
  }

  // Add delivery signature if available
  if (request.delivery_signature_url) {
    try {
      const img = new Image();

      // Create a promise to handle image loading
      const loadSignature = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            // Add the signature image
            doc.addImage(img, 'PNG', 120, yPosition, 60, 30);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });

      // Start loading the image
      img.src = request.delivery_signature_url;

      // Wait for the image to load and be processed
      await loadSignature;

      doc.text('Assinatura do Responsável pela Entrega', 110, yPosition + 40);
    } catch (signatureError) {
      console.error("Error adding delivery signature to PDF:", signatureError);
      // Add a placeholder for signature
      doc.text('____________________________', 120, yPosition + 20);
      doc.text('Assinatura do Responsável pela Entrega', 110, yPosition + 30);
    }
  } else {
    // Add a placeholder for signature
    doc.text('____________________________', 120, yPosition + 20);
    doc.text('Assinatura do Responsável pela Entrega', 110, yPosition + 30);
  }

  // Add date and time at the bottom
  const now = new Date();
  const dateStr = now.toLocaleDateString('pt-BR');
  const timeStr = now.toLocaleTimeString('pt-BR');

  doc.setFontSize(8);
  doc.text(`Documento gerado em ${dateStr} às ${timeStr}`, 14, 280);

  // Return the PDF as a blob
  return doc.output('blob');
}

/**
 * Generates a shopping list report for low stock items
 * @param products The low stock products
 * @param clubInfo Club information
 * @param threshold The threshold used for low stock
 * @param filename Optional filename for the PDF
 */
export async function generateShoppingListReport(
  products: any[],
  clubInfo: ClubInfo,
  threshold: number = 5,
  filename: string = 'lista-compras.pdf'
): Promise<Blob> {
  // Create a new PDF document
  const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

  // Add title
  const title = `Lista de Compras - Produtos com Estoque Baixo`;
  doc.setFontSize(18);
  doc.text(title, 14, 22);

  // Add club information
  doc.setFontSize(12);
  doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, 14, 30);

  if (clubInfo.address) {
    doc.text(`Endereço: ${clubInfo.address}`, 14, 36);
  }

  if (clubInfo.phone) {
    doc.text(`Telefone: ${clubInfo.phone}`, 14, 42);
  }

  // Try to add club logo if available
  if (clubInfo.logo_url) {
    try {
      const img = new Image();

      // Create a promise to handle image loading
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            // Calculate aspect ratio to maintain proportions
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;

            // Add the image to the PDF (right-aligned)
            doc.addImage(img, 'PNG', 170, 15, imgWidth, imgHeight);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });

      // Start loading the image
      img.src = clubInfo.logo_url;

      // Wait for the image to load and be processed
      await loadImage;
    } catch (logoError) {
      console.error("Error adding logo to PDF:", logoError);
    }
  }

  // Add information about low stock
  doc.setFontSize(11);
  doc.text(`Produtos com quantidade inferior à quantidade mínima configurada.`, 14, 50);

  // Prepare data for the table
  const tableData = products.map(product => [
    product.name,
    product.quantity.toString(),
    product.department,
    product.location || '-',
    product.description || '-'
  ]);

  // Add the table to the PDF
  autoTable(doc, {
    startY: 60,
    head: [['Produto', 'Quantidade', 'Departamento', 'Localização', 'Descrição']],
    body: tableData,
    theme: 'striped',
    headStyles: { fillColor: getClubPrimaryColorRgb() },
    didDrawPage: (data) => {
      // Add header on each page
      if (data.pageNumber > 1) {
        doc.setFontSize(10);
        doc.text(title, 14, 10);
        doc.setFontSize(8);
        doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, 14, 15);
      }
    }
  });

  // Add date and time at the bottom
  const now = new Date();
  const dateStr = now.toLocaleDateString('pt-BR');
  const timeStr = now.toLocaleTimeString('pt-BR');

  doc.setFontSize(8);
  doc.text(`Documento gerado em ${dateStr} às ${timeStr}`, 14, 280);

  // Return the PDF as a blob
  return doc.output('blob');
}

/**
 * Generates a shopping list report for low stock items by department
 * @param products The low stock products
 * @param departments List of departments
 * @param clubInfo Club information
 * @param filename Optional filename for the PDF
 */
export async function generateShoppingListReportByDepartment(
  products: any[],
  departments: string[],
  clubInfo: ClubInfo,
  filename: string = 'lista-compras-por-departamento.pdf'
): Promise<Blob> {
  // Create a new PDF document
  const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

  // Add title
  const title = 'Lista de Compras por Departamento';
  doc.setFontSize(18);
  doc.text(title, 14, 22);

  // Add club information
  doc.setFontSize(12);
  doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, 14, 30);

  if (clubInfo.address) {
    doc.text(`Endereço: ${clubInfo.address}`, 14, 36);
  }

  if (clubInfo.phone) {
    doc.text(`Telefone: ${clubInfo.phone}`, 14, 42);
  }

  // Try to add club logo if available
  if (clubInfo.logo_url) {
    try {
      const img = new Image();

      // Create a promise to handle image loading
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            // Calculate aspect ratio to maintain proportions
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;

            // Add the image to the PDF (right-aligned)
            doc.addImage(img, 'PNG', 170, 15, imgWidth, imgHeight);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });

      // Start loading the image
      img.src = clubInfo.logo_url;

      // Wait for the image to load and be processed
      await loadImage;
    } catch (logoError) {
      console.error("Error adding logo to PDF:", logoError);
    }
  }

  // Add information about low stock
  doc.setFontSize(11);
  doc.text(`Produtos com quantidade inferior à quantidade mínima configurada.`, 14, 50);

  // Initial Y position for the first department
  let yPosition = 60;

  // For each department, add a section
  for (let i = 0; i < departments.length; i++) {
    const department = departments[i];

    // Filter products by department
    const departmentProducts = products.filter(product => product.department === department);

    // If there are no products in this department, skip
    if (departmentProducts.length === 0) continue;

    // Add department title
    if (i > 0) {
      // Check if there's enough space for the next department
      if (yPosition > 250) {
        doc.addPage();
        yPosition = 20;
      } else {
        yPosition += 10;
      }
    }

    doc.setFontSize(14);
    doc.text(`Departamento: ${department}`, 14, yPosition);
    yPosition += 10;

    // Prepare data for the table
    const tableData = departmentProducts.map(product => [
      product.name,
      product.quantity.toString(),
      product.location || '-',
      product.description || '-'
    ]);

    // Add the table to the PDF
    autoTable(doc, {
      startY: yPosition,
      head: [['Produto', 'Quantidade', 'Localização', 'Descrição']],
      body: tableData,
      theme: 'striped',
      headStyles: { fillColor: getClubPrimaryColorRgb() },
      didDrawPage: (data) => {
        // Add header on each page
        if (data.pageNumber > 1) {
          doc.setFontSize(10);
          doc.text(title, 14, 10);
          doc.setFontSize(8);
          doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, 14, 15);
        }
      }
    });

    // Update Y position for the next department
    const docWithTable = doc as jsPDFWithAutoTable;
    yPosition = docWithTable.lastAutoTable.finalY + 10;
  }

  // Add date and time at the bottom
  const now = new Date();
  const dateStr = now.toLocaleDateString('pt-BR');
  const timeStr = now.toLocaleTimeString('pt-BR');

  doc.setFontSize(8);
  doc.text(`Documento gerado em ${dateStr} às ${timeStr}`, 14, 280);

  // Return the PDF as a blob
  return doc.output('blob');
}

// Helper function to get status label
function getStatusLabel(status: string): string {
  switch (status) {
    case 'pending':
      return 'Pendente';
    case 'approved':
      return 'Aprovado';
    case 'rejected':
      return 'Rejeitado';
    case 'completed':
      return 'Concluído';
    default:
      return status;
  }
}
