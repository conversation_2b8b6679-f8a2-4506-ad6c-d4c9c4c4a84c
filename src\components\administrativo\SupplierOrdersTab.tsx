import { useState, useEffect } from "react";
import {
  Card,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
  CardDescription
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { toast } from "@/components/ui/use-toast";
import {
  Plus,
  Trash2,
  Calendar,
  DollarSign,
  FileText,
  AlertCircle
} from "lucide-react";
import { SupplierOrder, deleteSupplierOrder, getSupplierOrders } from "@/api/api";
import { useUser } from "@/context/UserContext";
import { NovoSupplierOrderDialog } from "@/components/administrativo/NovoSupplierOrderDialog";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import { formatDate, formatCurrency } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";

interface SupplierOrdersTabProps {
  supplierId: number;
  supplierName: string;
  clubId: number;
  onRefresh?: () => void;
}

export function SupplierOrdersTab({
  supplierId,
  supplierName,
  clubId,
  onRefresh
}: SupplierOrdersTabProps) {
  const [orders, setOrders] = useState<SupplierOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [novoOrderDialogOpen, setNovoOrderDialogOpen] = useState(false);
  const [excluirOrderDialogOpen, setExcluirOrderDialogOpen] = useState(false);
  const [orderSelecionado, setOrderSelecionado] = useState<SupplierOrder | null>(null);
  const { user } = useUser();

  // Carregar pedidos do fornecedor
  useEffect(() => {
    fetchOrders();
  }, [supplierId, clubId]);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await getSupplierOrders(clubId, supplierId);
      setOrders(data);
    } catch (err) {
      console.error("Erro ao carregar pedidos:", err);
      setError(err instanceof Error ? err.message : "Erro ao carregar pedidos");
    } finally {
      setLoading(false);
    }
  };

  // Função para excluir um pedido
  const handleDeleteOrder = async () => {
    if (!orderSelecionado || !user) return;

    try {
      await deleteSupplierOrder(clubId, user.id, orderSelecionado.id);

      toast({
        title: "Sucesso",
        description: "Pedido excluído com sucesso",
      });

      // Atualizar a lista de pedidos
      fetchOrders();

      // Chamar callback de atualização se fornecido
      if (onRefresh) {
        onRefresh();
      }
    } catch (err) {
      console.error("Erro ao excluir pedido:", err);
      toast({
        title: "Erro",
        description: err instanceof Error ? err.message : "Erro ao excluir pedido",
        variant: "destructive",
      });
    } finally {
      setExcluirOrderDialogOpen(false);
      setOrderSelecionado(null);
    }
  };

  // Função para obter a cor do status da conta
  const getStatusColor = (status: string | undefined) => {
    if (!status) return "bg-gray-100 text-gray-700 border-gray-200";

    switch (status) {
      case "pago":
        return "bg-green-50 text-green-700 border-green-200";
      case "recebido":
        return "bg-primary/10 text-primary border-primary/20";
      case "pendente":
      default:
        return "bg-yellow-50 text-yellow-700 border-yellow-200";
    }
  };

  // Função para obter o label do status
  const getStatusLabel = (status: string | undefined) => {
    if (!status) return "Não vinculado";

    switch (status) {
      case "pago":
        return "Pago";
      case "recebido":
        return "Recebido";
      case "pendente":
      default:
        return "Pendente";
    }
  };

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Pedidos</CardTitle>
          <CardDescription>
            Gerenciar pedidos do fornecedor {supplierName}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-red-500">Erro ao carregar pedidos: {error}</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Pedidos</CardTitle>
          <CardDescription>
            Gerenciar pedidos do fornecedor {supplierName}
          </CardDescription>
        </div>
        <Button onClick={() => {
          setOrderSelecionado(null);
          setNovoOrderDialogOpen(true);
        }}>
          <Plus className="h-4 w-4 mr-2" />
          Adicionar Pedido
        </Button>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="text-center py-4">Carregando pedidos...</div>
        ) : orders.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            Nenhum pedido cadastrado para este fornecedor.
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Descrição</TableHead>
                  <TableHead>Data da Compra</TableHead>
                  <TableHead>Valor</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {orders.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center">
                        <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                        {order.description}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                        {formatDate(order.purchase_date)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <DollarSign className="h-4 w-4 mr-2 text-muted-foreground" />
                        {formatCurrency(order.amount)}
                      </div>
                    </TableCell>
                    <TableCell>
                      {order.financial_account_id ? (
                        <Badge
                          variant="outline"
                          className={getStatusColor(order.account_status)}
                        >
                          {getStatusLabel(order.account_status)}
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="bg-gray-100 text-gray-700 border-gray-200">
                          Não vinculado
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="text-red-500 hover:text-red-700 hover:bg-red-50"
                          onClick={() => {
                            setOrderSelecionado(order);
                            setExcluirOrderDialogOpen(true);
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>

      {/* Diálogos */}
      <NovoSupplierOrderDialog
        open={novoOrderDialogOpen}
        onOpenChange={setNovoOrderDialogOpen}
        clubId={clubId}
        supplierId={supplierId}
        supplierName={supplierName}
        onSuccess={fetchOrders}
      />

      {orderSelecionado && (
        <ConfirmDialog
          open={excluirOrderDialogOpen}
          onOpenChange={setExcluirOrderDialogOpen}
          title="Excluir Pedido"
          description={`Tem certeza que deseja excluir o pedido "${orderSelecionado.description}"? Esta ação não pode ser desfeita.`}
          onConfirm={handleDeleteOrder}
        />
      )}
    </Card>
  );
}
