import { jsPDF } from "jspdf";
import html2canvas from "html2canvas";
import { Player, ClubInfo } from "@/api/api";

/**
 * Gera um relatório de jogadores emprestados em PDF
 * @param players Lista de jogadores emprestados
 * @param clubInfo Informações do clube
 * @param filename Nome do arquivo PDF
 */
export async function generateLoanReport(
  players: Player[],
  clubInfo: ClubInfo,
  filename: string = 'relatorio-emprestados.pdf'
): Promise<void> {
  // Filtrar apenas jogadores emprestados
  const loanedPlayers = players.filter(player => player.status === "emprestado");

  // Ordenar jogadores por nome
  const sortedPlayers = [...loanedPlayers].sort((a, b) => a.name.localeCompare(b.name));

  // Criar um elemento temporário para renderizar o relatório
  const reportElement = document.createElement('div');
  reportElement.style.width = '210mm'; // Largura A4
  reportElement.style.padding = '10mm';
  reportElement.style.backgroundColor = 'white';
  reportElement.style.fontFamily = 'Arial, sans-serif';

  // Cabeçalho com informações do clube
  const header = document.createElement('div');
  header.style.display = 'flex';
  header.style.alignItems = 'center';
  header.style.marginBottom = '20px';

  // Logo do clube
  const logoDiv = document.createElement('div');
  logoDiv.style.width = '80px';
  logoDiv.style.height = '80px';
  logoDiv.style.marginRight = '20px';

  if (clubInfo.logo_url) {
    const logo = document.createElement('img');
    logo.src = clubInfo.logo_url;
    logo.style.width = '100%';
    logo.style.height = '100%';
    logo.style.objectFit = 'contain';
    logoDiv.appendChild(logo);
  } else {
    logoDiv.style.backgroundColor = '#f3f4f6';
    logoDiv.style.display = 'flex';
    logoDiv.style.alignItems = 'center';
    logoDiv.style.justifyContent = 'center';
    logoDiv.textContent = clubInfo.name?.substring(0, 2) || 'CL';
  }

  // Informações do clube
  const clubInfoDiv = document.createElement('div');

  const clubName = document.createElement('h1');
  clubName.textContent = clubInfo.name || 'Nome do Clube';
  clubName.style.margin = '0 0 5px 0';
  clubName.style.fontSize = '40px'; // Aumentado para 40px

  const clubAddress = document.createElement('p');
  clubAddress.textContent = clubInfo.address ? `${clubInfo.address}, ${clubInfo.zip_code || ''}` : '';
  clubAddress.style.margin = '0 0 3px 0';
  clubAddress.style.fontSize = '14px';

  const clubPhone = document.createElement('p');
  clubPhone.textContent = clubInfo.phone || '';
  clubPhone.style.margin = '0';
  clubPhone.style.fontSize = '14px';

  clubInfoDiv.appendChild(clubName);
  clubInfoDiv.appendChild(clubAddress);
  clubInfoDiv.appendChild(clubPhone);

  header.appendChild(logoDiv);
  header.appendChild(clubInfoDiv);
  reportElement.appendChild(header);

  // Título do relatório
  const title = document.createElement('h2');
  title.textContent = 'Relatório de Jogadores Emprestados';
  title.style.textAlign = 'center';
  title.style.margin = '20px 0';
  reportElement.appendChild(title);

  // Data do relatório
  const date = document.createElement('p');
  date.textContent = `Data: ${new Date().toLocaleDateString()}`;
  date.style.textAlign = 'right';
  date.style.margin = '10px 0 20px';
  reportElement.appendChild(date);

  // Verificar se há jogadores emprestados
  if (sortedPlayers.length === 0) {
    const noPlayers = document.createElement('p');
    noPlayers.textContent = 'Nenhum jogador emprestado no momento.';
    noPlayers.style.textAlign = 'center';
    noPlayers.style.margin = '30px 0';
    noPlayers.style.fontStyle = 'italic';
    reportElement.appendChild(noPlayers);
  } else {
    // Tabela de jogadores emprestados
    const table = document.createElement('table');
    table.style.width = '100%';
    table.style.borderCollapse = 'collapse';

    // Cabeçalho da tabela
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');

    const headers = ['Nome Completo', 'Data de Nascimento', 'Clube Destino', 'Fim do Empréstimo'];

    headers.forEach(headerText => {
      const th = document.createElement('th');
      th.textContent = headerText;
      th.style.padding = '8px';
      th.style.textAlign = 'left';
      th.style.borderBottom = '1px solid #e5e7eb';
      th.style.backgroundColor = '#f9fafb';
      headerRow.appendChild(th);
    });

    thead.appendChild(headerRow);
    table.appendChild(thead);

    // Corpo da tabela
    const tbody = document.createElement('tbody');

    sortedPlayers.forEach(player => {
      const row = document.createElement('tr');

      // Nome Completo
      const tdName = document.createElement('td');
      tdName.textContent = player.name;
      tdName.style.padding = '8px';
      tdName.style.borderBottom = '1px solid #e5e7eb';
      row.appendChild(tdName);

      // Data de Nascimento
      const tdBirthdate = document.createElement('td');
      tdBirthdate.textContent = player.birthdate
        ? new Date(player.birthdate).toLocaleDateString()
        : '-';
      tdBirthdate.style.padding = '8px';
      tdBirthdate.style.borderBottom = '1px solid #e5e7eb';
      row.appendChild(tdBirthdate);

      // Clube Destino
      const tdLoanClub = document.createElement('td');
      tdLoanClub.textContent = player.loan_club_name || '-';
      tdLoanClub.style.padding = '8px';
      tdLoanClub.style.borderBottom = '1px solid #e5e7eb';
      row.appendChild(tdLoanClub);

      // Fim do Empréstimo
      const tdLoanEnd = document.createElement('td');
      tdLoanEnd.textContent = player.loan_end_date
        ? new Date(player.loan_end_date).toLocaleDateString()
        : '-';
      tdLoanEnd.style.padding = '8px';
      tdLoanEnd.style.borderBottom = '1px solid #e5e7eb';
      row.appendChild(tdLoanEnd);

      tbody.appendChild(row);
    });

    table.appendChild(tbody);
    reportElement.appendChild(table);
  }

  // Criar o cabeçalho como um elemento separado para repetição em cada página
  const headerElement = document.createElement('div');
  headerElement.style.width = '210mm'; // Largura A4
  headerElement.style.padding = '10mm 10mm 0 10mm'; // Remover padding inferior para evitar espaço extra
  headerElement.style.backgroundColor = 'white';
  headerElement.style.fontFamily = 'Arial, sans-serif';

  // Clonar apenas o logo e informações do clube para o cabeçalho, sem o título do relatório
  const headerClone = document.createElement('div');
  headerClone.style.display = 'flex';
  headerClone.style.alignItems = 'center';
  headerClone.style.marginBottom = '10px';

  // Clonar o logo
  const logoClone = logoDiv.cloneNode(true);
  headerClone.appendChild(logoClone);

  // Clonar as informações do clube
  const clubInfoClone = clubInfoDiv.cloneNode(true);
  headerClone.appendChild(clubInfoClone);

  headerElement.appendChild(headerClone);

  // Remover o cabeçalho original do elemento principal para evitar duplicação
  reportElement.removeChild(header);

  // Adicionar os elementos ao documento temporariamente
  document.body.appendChild(reportElement);
  document.body.appendChild(headerElement);

  // Importar a função generatePDF do módulo pdfGenerator
  const { generatePDF } = await import('./pdfGenerator');

  // Gerar o PDF com suporte a múltiplas páginas e cabeçalho repetido
  try {
    await generatePDF(reportElement, filename, { orientation: 'portrait' }, headerElement);
  } finally {
    // Remover os elementos temporários
    document.body.removeChild(reportElement);
    document.body.removeChild(headerElement);
  }
}
