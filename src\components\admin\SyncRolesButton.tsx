import { useState } from "react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, RefreshCw } from "lucide-react";
import { useCurrentClubId } from "@/context/ClubContext";
import { checkAndFixRoleInconsistencies } from "@/api/api";

interface SyncRolesButtonProps {
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
  onSuccess?: (result: { checked: number; fixed: number; errors: number }) => void;
}

export function SyncRolesButton({
  variant = "outline",
  size = "default",
  className,
  onSuccess
}: SyncRolesButtonProps) {
  const clubId = useCurrentClubId();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const handleSync = async () => {
    try {
      setIsLoading(true);
      
      // Usar a função de API para verificar e corrigir inconsistências
      const result = await checkAndFixRoleInconsistencies(clubId);
      
      toast({
        title: "Sincronização concluída",
        description: `${result.checked} usuários verificados, ${result.fixed} corrigidos, ${result.errors} erros`,
      });
      
      if (onSuccess) {
        onSuccess(result);
      }
    } catch (error: any) {
      console.error("Erro ao sincronizar roles:", error);
      toast({
        title: "Erro",
        description: error.message || "Ocorreu um erro ao sincronizar os papéis dos usuários",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      className={className}
      onClick={handleSync}
      disabled={isLoading}
    >
      {isLoading ? (
        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
      ) : (
        <RefreshCw className="h-4 w-4 mr-2" />
      )}
      Sincronizar Papéis
    </Button>
  );
}
