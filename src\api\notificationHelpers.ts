import { supabase } from "@/integrations/supabase/client";
import { Notification } from "./notifications";

/**
 * Checks if a similar notification already exists for the user on the same day
 * @param userId User ID
 * @param clubId Club ID
 * @param title Notification title
 * @param type Notification type
 * @param referenceId Reference ID (optional)
 * @returns True if a similar notification exists
 */
export async function checkSimilarNotificationExists(
  userId: string,
  clubId: number,
  title: string,
  type: string,
  referenceId?: string
): Promise<boolean> {
  try {
    // Get today's date in YYYY-MM-DD format
    const today = new Date().toISOString().split('T')[0];
    
    // Build the query
    let query = supabase
      .from("notifications")
      .select("id")
      .eq("user_id", userId)
      .eq("club_id", clubId)
      .eq("title", title)
      .eq("type", type)
      .gte("created_at", `${today}T00:00:00`)
      .lte("created_at", `${today}T23:59:59`);
    
    // Add reference_id filter if provided
    if (referenceId) {
      query = query.eq("reference_id", referenceId);
    }
    
    const { data, error } = await query;
    
    if (error) {
      console.error("Error checking for similar notifications:", error);
      return false; // Assume no similar notification exists in case of error
    }
    
    return data && data.length > 0;
  } catch (error) {
    console.error("Error in checkSimilarNotificationExists:", error);
    return false; // Assume no similar notification exists in case of error
  }
}

/**
 * Creates a notification only if a similar one doesn't already exist today
 * @param notification Notification data
 * @returns Created notification or null if similar exists
 */
export async function createUniqueNotification(
  notification: Omit<Notification, "id" | "created_at" | "read">
): Promise<Notification | null> {
  try {
    // Check if similar notification exists
    const exists = await checkSimilarNotificationExists(
      notification.user_id,
      notification.club_id,
      notification.title,
      notification.type,
      notification.reference_id
    );
    
    // If similar notification exists, don't create a new one
    if (exists) {
      console.log("Similar notification already exists, skipping creation");
      return null;
    }
    
    // Create the notification
    const { data, error } = await supabase
      .from("notifications")
      .insert({
        ...notification,
        read: false,
      })
      .select()
      .single();
    
    if (error) {
      throw new Error(`Error creating notification: ${error.message}`);
    }
    
    return data;
  } catch (error: any) {
    console.error("Error in createUniqueNotification:", error);
    throw new Error(error.message || "Error creating unique notification");
  }
}
