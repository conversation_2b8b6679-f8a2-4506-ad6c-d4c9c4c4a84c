
import { useState, useMemo } from 'react';

interface PaginationOptions<T> {
  data: T[];
  pageSize?: number;
  initialPage?: number;
}

interface PaginationResult<T> {
  items: T[];
  pageSize: number;
  currentPage: number;
  totalPages: number;
  totalItems: number;
  setPage: (page: number) => void;
  setPageSize: (pageSize: number) => void;
  nextPage: () => void;
  prevPage: () => void;
  firstPage: () => void;
  lastPage: () => void;
  canPrevPage: boolean;
  canNextPage: boolean;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

export function usePagination<T>({ 
  data, 
  pageSize = 10, 
  initialPage = 1 
}: PaginationOptions<T>): PaginationResult<T> {
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [pageLimit, setPageLimit] = useState(pageSize);
  const [isLoading, setIsLoading] = useState(false);
  
  const totalItems = data.length;
  const totalPages = useMemo(() => Math.ceil(totalItems / pageLimit), [totalItems, pageLimit]);
  
  // Ensure current page is in bounds
  if (currentPage > totalPages) {
    setCurrentPage(Math.max(1, totalPages));
  }
  
  // Calculate current page items
  const paginatedItems = useMemo(() => {
    const start = (currentPage - 1) * pageLimit;
    const end = start + pageLimit;
    return data.slice(start, Math.min(end, totalItems));
  }, [data, currentPage, pageLimit, totalItems]);
  
  const setPage = (page: number) => {
    const pageNumber = Math.max(1, Math.min(page, totalPages));
    setCurrentPage(pageNumber);
  };
  
  const setPageSize = (size: number) => {
    setPageLimit(size);
    // Adjust current page to maintain approximate position
    const currentFirstItem = (currentPage - 1) * pageLimit;
    const newPage = Math.floor(currentFirstItem / size) + 1;
    setCurrentPage(Math.max(1, Math.min(newPage, Math.ceil(totalItems / size))));
  };
  
  const nextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(prev => prev + 1);
    }
  };
  
  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(prev => prev - 1);
    }
  };
  
  const firstPage = () => {
    setCurrentPage(1);
  };
  
  const lastPage = () => {
    setCurrentPage(totalPages);
  };
  
  return {
    items: paginatedItems,
    pageSize: pageLimit,
    currentPage,
    totalPages,
    totalItems,
    setPage,
    setPageSize,
    nextPage,
    prevPage,
    firstPage,
    lastPage,
    canPrevPage: currentPage > 1,
    canNextPage: currentPage < totalPages,
    isLoading,
    setIsLoading
  };
}
