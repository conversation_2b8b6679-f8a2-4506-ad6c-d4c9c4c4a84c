# Atualização da Documentação da API

## Novas Funcionalidades (Versão 2.0)

### 1. Sistema de Upload de Imagens e Documentos

#### Funções para Upload de Imagens

- `uploadProfileImage(userId: string, file: File): Promise<string>`
  - Faz upload de uma imagem de perfil para o Supabase Storage
  - Retorna a URL pública da imagem

#### Funções para Documentos de Jogadores

- `uploadDocument(clubId: number, playerId: string, documentType: string, file: File): Promise<PlayerDocument>`
  - Faz upload de um documento de jogador
  - Retorna o documento registrado
- `getPlayerDocuments(clubId: number, playerId: string): Promise<PlayerDocument[]>`
  - Obtém os documentos de um jogador
  - Retorna uma lista de documentos
- `verifyDocument(documentId: number, userId: string, status: "verified" | "rejected"): Promise<PlayerDocument>`
  - Verifica um documento de jogador
  - Retorna o documento atualizado
- `deleteDocument(documentId: number): Promise<boolean>`
  - Exclui um documento de jogador
  - Retorna true se o documento foi excluído com sucesso
- `getPendingDocuments(clubId: number): Promise<PlayerDocument[]>`
  - Obtém os documentos pendentes de verificação
  - Retorna uma lista de documentos pendentes
- `checkRequiredDocuments(clubId: number, playerId: string): Promise<Record<string, { status: "missing" | "pending" | "verified" | "rejected" }>>`
  - Verifica se um jogador tem todos os documentos obrigatórios
  - Retorna um objeto com o status de cada documento obrigatório

#### Constantes para Documentos

- `DOCUMENT_TYPES`: Tipos de documentos disponíveis
- `DOCUMENT_LABELS`: Rótulos para os tipos de documentos

### 2. Sistema de Departamentos e Permissões

#### Funções para Departamentos

- `getDepartments(clubId: number): Promise<Department[]>`
  - Obtém todos os departamentos de um clube
  - Retorna uma lista de departamentos
- `createDepartment(clubId: number, name: string, description?: string): Promise<Department>`
  - Cria um novo departamento
  - Retorna o departamento criado
- `updateDepartment(departmentId: number, name: string, description?: string): Promise<Department>`
  - Atualiza um departamento
  - Retorna o departamento atualizado
- `deleteDepartment(departmentId: number): Promise<boolean>`
  - Exclui um departamento
  - Retorna true se o departamento foi excluído com sucesso
- `getDepartmentUsers(clubId: number, departmentId: number): Promise<UserDepartment[]>`
  - Obtém todos os usuários de um departamento
  - Retorna uma lista de usuários do departamento
- `getUserDepartments(clubId: number, userId: string): Promise<UserDepartment[]>`
  - Obtém todos os departamentos de um usuário
  - Retorna uma lista de departamentos do usuário
- `addUserToDepartment(clubId: number, userId: string, departmentId: number, role: string): Promise<UserDepartment>`
  - Adiciona um usuário a um departamento
  - Retorna a associação criada
- `removeUserFromDepartment(userDepartmentId: number): Promise<boolean>`
  - Remove um usuário de um departamento
  - Retorna true se o usuário foi removido com sucesso
- `updateUserDepartmentRole(userDepartmentId: number, role: string): Promise<UserDepartment>`
  - Atualiza o papel de um usuário em um departamento
  - Retorna a associação atualizada

#### Funções para Permissões

- `getUserPermissions(clubId: number, userId: string): Promise<UserPermissions | null>`
  - Obtém as permissões de um usuário em um clube
  - Retorna as permissões do usuário
- `updateUserPermissions(clubId: number, userId: string, permissions: Record<string, boolean>): Promise<boolean>`
  - Atualiza as permissões de um usuário em um clube
  - Retorna true se as permissões foram atualizadas com sucesso
- `createUserInvitation(clubId: number, email: string, role: string, departmentId?: number, permissions?: Record<string, boolean>): Promise<UserInvitation>`
  - Cria um convite para um novo usuário
  - Retorna o convite criado
- `getUserInvitationByToken(token: string): Promise<UserInvitation | null>`
  - Obtém um convite pelo token
  - Retorna o convite ou null se não encontrado
- `acceptUserInvitation(token: string, userId: string): Promise<boolean>`
  - Aceita um convite de usuário
  - Retorna true se o convite foi aceito com sucesso
- `cancelUserInvitation(invitationId: number): Promise<boolean>`
  - Cancela um convite de usuário
  - Retorna true se o convite foi cancelado com sucesso
- `getClubInvitations(clubId: number): Promise<UserInvitation[]>`
  - Obtém todos os convites de um clube
  - Retorna uma lista de convites
- `hasPermission(clubId: number, userId: string, permission: string): Promise<boolean>`
  - Verifica se um usuário tem uma permissão específica
  - Retorna true se o usuário tem a permissão

### 3. Sistema Financeiro para Jogadores

#### Funções para Finanças

- `getPlayerFinancialData(clubId: number, playerId: string): Promise<PlayerFinancialData>`
  - Obtém os dados financeiros de um jogador
  - Retorna os dados financeiros do jogador
- `addPlayerFinancialEntry(clubId: number, playerId: string, month: number, year: number, amount: number, description: string): Promise<PlayerFinancialData>`
  - Adiciona uma entrada financeira para um jogador
  - Retorna os dados financeiros atualizados
- `removePlayerFinancialEntry(clubId: number, playerId: string, month: number, year: number, index: number): Promise<PlayerFinancialData>`
  - Remove uma entrada financeira de um jogador
  - Retorna os dados financeiros atualizados
- `calculateMonthlyBalance(financialData: PlayerFinancialData, month: number, year: number): number`
  - Calcula o saldo financeiro de um jogador para um mês específico
  - Retorna o saldo do mês
- `calculateTotalBalance(financialData: PlayerFinancialData): number`
  - Calcula o saldo financeiro total de um jogador
  - Retorna o saldo total
- `getYearlyFinancialData(financialData: PlayerFinancialData, year: number): Record<number, PlayerFinancialEntry[]>`
  - Obtém os dados financeiros de um jogador para um ano específico
  - Retorna os dados financeiros do ano

### 4. Integração com APIs Externas

#### Funções para Integração com CEP

- `getAddressByCEP(cep: string): Promise<{ cep: string; logradouro: string; complemento: string; bairro: string; localidade: string; uf: string; ibge: string; gia: string; ddd: string; siafi: string; }>`
  - Busca endereço pelo CEP usando a API ViaCEP
  - Retorna os dados do endereço
- `formatCEP(cep: string): string`
  - Formata um CEP adicionando a máscara
  - Retorna o CEP formatado
- `formatCPF(cpf: string): string`
  - Formata um CPF adicionando a máscara
  - Retorna o CPF formatado
- `validateCPF(cpf: string): boolean`
  - Valida um CPF
  - Retorna true se o CPF for válido
- `getFirstName(fullName: string): string`
  - Extrai o primeiro nome de um nome completo
  - Retorna o primeiro nome

## Tipos de Dados

### Documentos

```typescript
type PlayerDocument = {
  id: number;
  club_id: number;
  player_id: string;
  document_type: string;
  file_url: string;
  status: 'pending' | 'verified' | 'rejected';
  uploaded_at: string;
  verified_at: string | null;
  verified_by: string | null;
  verifier_name?: string;
};
```

### Departamentos

```typescript
type Department = {
  id: number;
  club_id: number;
  name: string;
  description: string | null;
  created_at: string;
};

type UserDepartment = {
  id: number;
  club_id: number;
  user_id: string;
  department_id: number;
  role: string;
  created_at: string;
  department_name?: string;
  user_name?: string;
};
```

### Permissões

```typescript
type UserInvitation = {
  id: number;
  club_id: number;
  email: string;
  role: string;
  department_id: number | null;
  permissions: Record<string, any>;
  token: string;
  status: 'pending' | 'accepted' | 'expired';
  created_at: string;
  expires_at: string;
  department_name?: string;
};

type UserPermissions = {
  role: string;
  permissions: Record<string, boolean>;
};
```

### Finanças

````typescript
type PlayerFinancialEntry = {
  month: number;
  year: number;
  amount: number;
  description: string;
};

type PlayerFinancialData = {
  [key: string]: PlayerFinancialEntry[]; // Chave no formato "YYYY-MM"
};

## Novas Funcionalidades (Versão 3.0)

### 1. Sistema de Permissões Baseado em Papéis

#### Funções para Gerenciamento de Usuários
- `getClubUsers(clubId: number): Promise<ClubUser[]>`
  - Obtém todos os usuários de um clube
  - Retorna uma lista de usuários
- `removeUserFromClub(clubId: number, userId: string): Promise<boolean>`
  - Remove um usuário de um clube
  - Retorna true se o usuário foi removido com sucesso
- `getUserProfile(userId: string): Promise<UserProfile>`
  - Obtém o perfil de um usuário
  - Retorna o perfil do usuário
- `updateUserProfile(userId: string, profile: Partial<UserProfile>): Promise<UserProfile>`
  - Atualiza o perfil de um usuário
  - Retorna o perfil atualizado

#### Funções para Contas de Jogadores
- `createPlayerWithAccount(clubId: number, playerId: string, email: string, password?: string): Promise<boolean>`
  - Cria uma conta de usuário para um jogador
  - Se password for fornecido, cria a conta diretamente
  - Se password não for fornecido, envia um convite por email
  - Retorna true se a conta foi criada com sucesso

### 2. Componentes de Interface

#### Componentes de Usuário
- `UserAvatar`: Componente para exibir o avatar do usuário
- `AvatarUpload`: Componente para fazer upload do avatar do usuário
- `UserList`: Componente para listar os usuários do clube
- `UserPermissions`: Componente para gerenciar as permissões de um usuário

#### Componentes de Jogador
- `PlayerAccountForm`: Componente para criar uma conta para um jogador
- `PlayerDocumentViewer`: Componente para visualizar os documentos de um jogador

#### Componentes de Departamento
- `DepartmentList`: Componente para listar os departamentos
- `DepartmentForm`: Componente para adicionar e editar departamentos
- `DepartmentUsers`: Componente para gerenciar os usuários de um departamento
- `AddUserToDepartment`: Componente para adicionar usuários a um departamento

### 3. Páginas

#### Páginas de Usuário
- `PerfilUsuario`: Página para gerenciar o perfil do usuário
- `Usuarios`: Página para administrar os usuários do clube

#### Páginas de Departamento
- `Departamentos`: Página para administrar os departamentos do clube

## Tipos de Dados

### Usuários
```typescript
type ClubUser = {
  id: string;
  name: string;
  email: string;
  role: string;
  profile_image?: string;
  created_at: string;
};

type UserProfile = {
  id: string;
  name: string;
  email: string;
  profile_image?: string;
  phone?: string;
  role?: string;
};
````
