import { supabase } from "@/integrations/supabase/client";

/**
 * Migra os resultados de partidas antigas para o novo formato (win, loss, draw)
 * @param clubId ID do clube
 */
export async function migrateMatchResults(clubId: number): Promise<void> {
  try {
    // Buscar todas as partidas que não têm o resultado no formato correto
    const { data, error } = await supabase
      .from("matches")
      .select("*")
      .eq("club_id", clubId)
      .not("result", "in", '("win","loss","draw")');

    if (error) {
      console.error("Erro ao buscar partidas para migração:", error);
      return;
    }

    if (!data || data.length === 0) {
      console.log("Nenhuma partida para migrar");
      return;
    }

    console.log(`Migrando ${data.length} partidas...`);

    // Para cada partida, atualizar o resultado
    for (const match of data) {
      let result: "win" | "loss" | "draw";
      
      // Se o resultado está no formato "X-Y" (ex: "2-1")
      if (typeof match.result === "string" && match.result.includes("-")) {
        const [homeScore, awayScore] = match.result.split("-").map(Number);
        
        // Se o jogo é em casa
        if (match.notes?.includes("|casa|")) {
          if (homeScore > awayScore) {
            result = "win";
          } else if (homeScore < awayScore) {
            result = "loss";
          } else {
            result = "draw";
          }
        } 
        // Se o jogo é fora
        else {
          if (homeScore < awayScore) {
            result = "win";
          } else if (homeScore > awayScore) {
            result = "loss";
          } else {
            result = "draw";
          }
        }
      } 
      // Se o resultado não está no formato esperado, usar os campos score_home e score_away
      else {
        const homeScore = match.score_home || 0;
        const awayScore = match.score_away || 0;
        
        // Se o jogo é em casa
        if (match.notes?.includes("|casa|")) {
          if (homeScore > awayScore) {
            result = "win";
          } else if (homeScore < awayScore) {
            result = "loss";
          } else {
            result = "draw";
          }
        } 
        // Se o jogo é fora
        else {
          if (homeScore < awayScore) {
            result = "win";
          } else if (homeScore > awayScore) {
            result = "loss";
          } else {
            result = "draw";
          }
        }
      }

      // Atualizar o resultado
      const { error: updateError } = await supabase
        .from("matches")
        .update({ result })
        .eq("id", match.id)
        .eq("club_id", clubId);

      if (updateError) {
        console.error(`Erro ao atualizar partida ${match.id}:`, updateError);
      }
    }

    console.log("Migração concluída com sucesso!");
  } catch (e) {
    console.error("Erro ao migrar resultados:", e);
  }
}
