import { supabase } from "@/integrations/supabase/client";

export interface ClubUser {
  id: string;
  name: string;
  email: string;
  role: string;
  profile_image?: string;
  created_at: string;
  is_collaborator?: boolean; // Indica se é um colaborador sem conta de usuário
}

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  profile_image?: string;
  phone?: string;
  role?: string;
}

/**
 * Obtém todos os usuários de um clube
 * @param clubId ID do clube
 * @param excludeRoles Papéis a serem excluídos da lista (opcional)
 * @param includeCollaborators Se true, inclui colaboradores que não têm conta de usuário (opcional)
 * @returns Lista de usuários
 */
export async function getClubUsers(
  clubId: number,
  excludeRoles: string[] = [],
  includeCollaborators: boolean = false
): Promise<ClubUser[]> {
  try {
    console.log(`Buscando usuários para o clube ${clubId}`);

    // Primeiro, buscar os membros do clube com seus papéis
    const { data: memberData, error: memberError } = await supabase
      .from("club_members")
      .select("user_id, role, created_at")
      .eq("club_id", clubId);

    if (memberError) {
      throw new Error(`Erro ao buscar membros do clube: ${memberError.message}`);
    }

    // Se não houver membros, verificar se devemos incluir colaboradores
    if ((!memberData || memberData.length === 0) && !includeCollaborators) {
      return [];
    }

    // Filtrar membros com base nos papéis a serem excluídos
    const filteredMembers = excludeRoles.length > 0
      ? memberData?.filter(member => !excludeRoles.includes(member.role)) || []
      : memberData || [];

    // Extrair os IDs dos usuários
    const userIds = filteredMembers.map(member => member.user_id).filter(Boolean);

    // Se não houver IDs válidos e não devemos incluir colaboradores, retornar array vazio
    if (userIds.length === 0 && !includeCollaborators) {
      return [];
    }

    // Buscar informações dos usuários
    let userData: any[] = [];
    if (userIds.length > 0) {
      const { data, error: userError } = await supabase
        .from("users")
        .select("id, email, name, profile_image")
        .in("id", userIds);

      if (userError) {
        throw new Error(`Erro ao buscar dados dos usuários: ${userError.message}`);
      }

      userData = data || [];
    }

    // Combinar os dados de membros e usuários
    const users: ClubUser[] = [];

    for (const member of filteredMembers) {
      if (!member.user_id) continue;

      const user = userData?.find(u => u.id === member.user_id);

      if (user) {
        users.push({
          id: user.id,
          name: user.name || '',
          email: user.email || '',
          role: member.role || '',
          profile_image: user.profile_image,
          created_at: member.created_at
        });
      }
    }

    // Se solicitado, incluir colaboradores que não têm conta de usuário
    if (includeCollaborators) {
      try {
        // Buscar colaboradores
        const { data: collaborators, error: collabError } = await supabase
          .from("collaborators_view")
          .select("*")
          .eq("club_id", clubId)
          .is("user_id", null); // Apenas colaboradores sem conta de usuário

        if (collabError) {
          console.error("Erro ao buscar colaboradores:", collabError);
        } else if (collaborators && collaborators.length > 0) {
          // Filtrar colaboradores com base nos papéis a serem excluídos
          const filteredCollaborators = excludeRoles.length > 0
            ? collaborators.filter(collab => !excludeRoles.includes(collab.role))
            : collaborators;

          // Adicionar colaboradores à lista de usuários
          for (const collab of filteredCollaborators) {
            users.push({
              id: `collab_${collab.id}`, // ID temporário para colaboradores sem conta
              name: collab.full_name,
              email: collab.email || '',
              role: collab.role,
              profile_image: collab.image,
              created_at: collab.created_at,
              is_collaborator: true // Marcar como colaborador
            });
          }
        }
      } catch (collabError) {
        console.error("Erro ao incluir colaboradores:", collabError);
      }
    }

    return users;
  } catch (error: any) {
    console.error("Erro ao buscar usuários:", error);
    // Retornar array vazio em caso de erro para evitar quebrar a página
    return [];
  }
}

/**
 * Remove um usuário de um clube
 * @param clubId ID do clube
 * @param userId ID do usuário
 * @returns true se o usuário foi removido com sucesso
 */
export async function removeUserFromClub(clubId: number, userId: string): Promise<boolean> {
  try {
    console.log(`Removendo usuário ${userId} do clube ${clubId} (tipo: ${typeof clubId})`);

    // Garantir que clubId seja tratado como número
    const clubIdNumber = Number(clubId);

    if (isNaN(clubIdNumber)) {
      throw new Error(`ID do clube inválido: ${clubId}`);
    }

    // Remover usuário do clube (tabela club_members)
    const { error } = await supabase
      .from("club_members")
      .delete()
      .eq("user_id", userId)
      .eq("club_id", clubIdNumber);

    if (error) {
      console.error("Erro ao remover usuário do clube:", error);
      throw new Error(`Erro ao remover usuário do clube: ${error.message}`);
    }

    // Remover usuário dos departamentos
    await supabase
      .from("user_departments")
      .delete()
      .eq("user_id", userId)
      .eq("club_id", clubIdNumber);

    // Se o usuário for um jogador, remover a associação com a conta de jogador
    const { data: player, error: playerError } = await supabase
      .from("players")
      .select("id")
      .eq("user_id", userId)
      .eq("club_id", clubIdNumber)
      .maybeSingle();

    if (!playerError && player) {
      // Atualizar o jogador para remover a associação com o usuário
      await supabase
        .from("players")
        .update({ user_id: null })
        .eq("id", player.id)
        .eq("club_id", clubIdNumber);
    }

    // Remover da tabela player_accounts se existir
    await supabase
      .from("player_accounts")
      .delete()
      .eq("user_id", userId)
      .eq("club_id", clubIdNumber);

    return true;
  } catch (error: any) {
    console.error("Erro ao remover usuário do clube:", error);
    throw new Error(error.message || "Erro ao remover usuário do clube");
  }
}

/**
 * Obtém o perfil de um usuário
 * @param userId ID do usuário
 * @returns Perfil do usuário
 */
export async function getUserProfile(userId: string): Promise<UserProfile> {
  try {
    const { data, error } = await supabase
      .from("users")
      .select("*")
      .eq("id", userId)
      .single();

    if (error) {
      throw new Error(`Erro ao buscar perfil do usuário: ${error.message}`);
    }

    return data as UserProfile;
  } catch (error: any) {
    console.error("Erro ao buscar perfil do usuário:", error);
    throw new Error(error.message || "Erro ao buscar perfil do usuário");
  }
}

/**
 * Atualiza o perfil de um usuário
 * @param userId ID do usuário
 * @param profile Dados do perfil a serem atualizados
 * @returns Perfil atualizado
 */
export async function updateUserProfile(
  userId: string,
  profile: Partial<UserProfile>
): Promise<UserProfile> {
  try {
    // Verificar se o usuário existe
    const { data: existingUser, error: checkError } = await supabase
      .from("users")
      .select("*")
      .eq("id", userId)
      .single();

    if (checkError) {
      throw new Error(`Erro ao verificar usuário: ${checkError.message}`);
    }

    // Tentar atualizar diretamente com SQL para garantir que a atualização funcione
    if (profile.profile_image) {
      try {
        const { data: sqlData, error: sqlError } = await supabase
          .rpc('update_user_profile_image', {
            user_id: userId,
            image_url: profile.profile_image
          });

        if (!sqlError) {
          // Se a atualização SQL funcionou, atualizar o restante dos campos normalmente
          const { profile_image, ...restProfile } = profile;

          if (Object.keys(restProfile).length > 0) {
            await supabase
              .from("users")
              .update(restProfile)
              .eq("id", userId);
          }

          // Buscar o usuário atualizado
          const { data: updatedUser, error: fetchError } = await supabase
            .from("users")
            .select("*")
            .eq("id", userId)
            .single();

          if (fetchError) {
            throw new Error(`Erro ao buscar usuário atualizado: ${fetchError.message}`);
          }

          return updatedUser as UserProfile;
        }
      } catch (sqlErr) {
        // Continuar com o método padrão
      }
    }

    // Método padrão de atualização
    const { data, error } = await supabase
      .from("users")
      .update(profile)
      .eq("id", userId)
      .select()
      .single();

    if (error) {
      throw new Error(`Erro ao atualizar perfil do usuário: ${error.message}`);
    }

    return data as UserProfile;
  } catch (error: any) {
    console.error("Erro ao atualizar perfil do usuário:", error);
    throw new Error(error.message || "Erro ao atualizar perfil do usuário");
  }
}
