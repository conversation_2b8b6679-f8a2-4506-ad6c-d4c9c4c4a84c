
import { useState } from "react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Calendar, Clock, MapPin, Users } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { TaskDialog } from "@/components/modals/TaskDialog";
import { GameDialog } from "@/components/modals/GameDialog";
import { TrainingDialog } from "@/components/modals/TrainingDialog";
import type { AgendaEvent, Training, UpcomingMatch } from "@/api/api";
import { useAgendaStore } from "@/store/useAgendaStore";
import { useCurrentClubId } from "@/context/ClubContext";

interface AgendaEventListProps {
  events: (Omit<AgendaEvent, "date" | "endTime"> & { date: Date; endTime: Date })[];
}

const getEventTypeStyles = (type: string) => {
  const styles: Record<string, { color: string, bg: string, label: string }> = {
    treino: { color: "text-green-600", bg: "bg-green-50", label: "Treino" },
    jogo: { color: "text-primary", bg: "bg-primary/10", label: "Jogo" },
    reuniao: { color: "text-purple-600", bg: "bg-purple-50", label: "Reunião" },
    medico: { color: "text-red-600", bg: "bg-red-50", label: "Médico" },
    viagem: { color: "text-amber-600", bg: "bg-amber-50", label: "Viagem" },
    outro: { color: "text-gray-600", bg: "bg-gray-50", label: "Outro" },
  };

  return styles[type] || styles.outro;
};

export function AgendaEventList({ events }: AgendaEventListProps) {
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [openDetailsDialog, setOpenDetailsDialog] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<AgendaEventListProps["events"][number] | null>(null);
  const { updateAgendaEvent, deleteAgendaEvent, loading } = useAgendaStore();
  const clubId = useCurrentClubId();

  if (events.length === 0) {
    return null;
  }

  // Ordenar eventos por horário
  const sortedEvents = [...events].sort((a, b) => a.date.getTime() - b.date.getTime());

  const handleEdit = (event: AgendaEventListProps["events"][number]) => {
    setSelectedEvent(event);
    setOpenEditDialog(true);
  };

  const handleViewDetails = (event: AgendaEventListProps["events"][number]) => {
    setSelectedEvent(event);
    setOpenDetailsDialog(true);
  };

  // Função para mapear um evento genérico para o tipo Training esperado pelo dialog
  function mapAgendaEventToTraining(event: AgendaEventListProps["events"][number]): Training {
    return {
      id: event.id,
      club_id: clubId,
      name: event.title, // AgendaEvent usa title, Training usa name
      date: event.date.toISOString(),
      type: event.type,
      status: "agendado",
      progress: 0,
      coach: "",
      description: event.description,
      location: event.location,
      time: event.time || "",
      participants: event.participants?.length || 0,
    };
  }

  // Função para mapear um evento genérico para o tipo UpcomingMatch esperado pelo dialog
  function mapAgendaEventToUpcomingMatch(event: AgendaEventListProps["events"][number]): UpcomingMatch {
    return {
      id: event.id,
      club_id: clubId,
      opponent: event.title, // Supondo que title do evento é o adversário
      competition: event.description || "Amistoso",
      date: event.date.toISOString(),
      time: event.time || "",
      location: event.location,
      type: "casa", // valor padrão, pois AgendaEvent não tem esse campo
    };
  }

  // Função para obter o componente de diálogo adequado com base no tipo de evento
  const getDialogComponent = (isEdit: boolean) => {
    if (!selectedEvent) return null;

    const dialogProps = {
      open: isEdit ? openEditDialog : openDetailsDialog,
      onOpenChange: isEdit ? setOpenEditDialog : setOpenDetailsDialog,
      editMode: true,
      clubId: clubId,
    };

    switch (selectedEvent.type) {
      case 'treino':
        return <TrainingDialog {...dialogProps} training={mapAgendaEventToTraining(selectedEvent)} />;
      case 'jogo':
        return <GameDialog {...dialogProps} game={mapAgendaEventToUpcomingMatch(selectedEvent)} />;
      default:
        return <TaskDialog {...dialogProps} task={selectedEvent as any} />;
    }
  };

  // Função para editar um evento
  const handleEditSave = async (eventId: number, data: Partial<AgendaEventListProps["events"][number]>) => {
    // Converter campos Date para string antes de enviar ao store
    const dataToSend: Partial<AgendaEvent> = {
      ...data,
      date: data.date instanceof Date ? data.date.toISOString() : undefined,
      endTime: data.endTime instanceof Date ? data.endTime.toISOString() : undefined,
    };
    await updateAgendaEvent(clubId, eventId, dataToSend);
    setOpenEditDialog(false);
  };

  // Função para deletar um evento
  const handleDelete = async (eventId: number) => {
    await deleteAgendaEvent(clubId, eventId);
    setOpenEditDialog(false);
  };

  return (
    <div className="space-y-4">
      {sortedEvents.map((event) => {
        const { color, bg, label } = getEventTypeStyles(event.type);

        return (
          <Card key={event.id} className="overflow-hidden">
            <div className={`h-1 w-full ${bg}`} />
            <CardContent className="pt-6">
              <div className="flex items-start justify-between">
                <h3 className="font-medium text-lg">{event.title}</h3>
                <Badge className={`${color} ${bg} border-transparent`}>
                  {label}
                </Badge>
              </div>

              <CardDescription className="mt-2 space-y-2">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {format(event.date, 'HH:mm', { locale: ptBR })} - {format(event.endTime, 'HH:mm', { locale: ptBR })}
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <span>{event.location}</span>
                </div>

                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span>{event.participants.join(", ")}</span>
                </div>

                <div className="mt-2 text-sm text-foreground">{event.description}</div>
              </CardDescription>
            </CardContent>
          </Card>
        );
      })}

      {selectedEvent && getDialogComponent(true)}
      {selectedEvent && getDialogComponent(false)}
    </div>
  );
}
