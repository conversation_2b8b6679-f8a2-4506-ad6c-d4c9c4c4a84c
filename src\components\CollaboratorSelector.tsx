import { useState, useEffect } from "react";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Collaborator, getCollaborators } from "@/api/api";
import { useCurrentClubId } from "@/context/ClubContext";

interface CollaboratorSelectorProps {
  value: string;
  onChange: (value: string) => void;
  label?: string;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  excludeWithUsers?: boolean; // Se true, exclui colaboradores que já têm usuários associados
}

export function CollaboratorSelector({
  value,
  onChange,
  label = "Colaborador",
  placeholder = "Selecione um colaborador",
  disabled = false,
  required = false,
  excludeWithUsers = false,
}: CollaboratorSelectorProps) {
  const clubId = useCurrentClubId();
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCollaborators = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await getCollaborators(clubId);

        // Filtrar colaboradores que já têm usuários associados, se necessário
        const filteredData = excludeWithUsers
          ? data.filter(collab => !collab.user_id)
          : data;

        setCollaborators(filteredData);
      } catch (err: any) {
        console.error("Erro ao carregar colaboradores:", err);
        setError(err.message || "Erro ao carregar colaboradores");
      } finally {
        setLoading(false);
      }
    };

    fetchCollaborators();
  }, [clubId, excludeWithUsers]);

  return (
    <div className="space-y-2">
      {label && <Label htmlFor="collaborator-selector">{label}{required && <span className="text-red-500 ml-1">*</span>}</Label>}
      <Select
        value={value}
        onValueChange={onChange}
        disabled={disabled || loading || collaborators.length === 0}
      >
        <SelectTrigger id="collaborator-selector" className={error ? "border-red-500" : ""}>
          <SelectValue placeholder={loading ? "Carregando..." : placeholder} />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="none">Nenhum colaborador</SelectItem>
          {collaborators.map((collaborator) => (
            <SelectItem key={collaborator.id} value={collaborator.id.toString()}>
              {collaborator.full_name} - {collaborator.role}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {error && <p className="text-red-500 text-sm">{error}</p>}
      {excludeWithUsers && collaborators.length === 0 && !loading && !error && (
        <p className="text-amber-500 text-sm">
          Não há colaboradores disponíveis sem usuários associados.
        </p>
      )}
    </div>
  );
}
