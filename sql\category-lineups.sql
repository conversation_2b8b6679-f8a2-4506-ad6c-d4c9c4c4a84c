-- Add category_id to matches table
ALTER TABLE matches
ADD COLUMN category_id INTEGER REFERENCES categories(id);

-- Create category_lineups table
CREATE TABLE category_lineups (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  category_id INTEGER REFERENCES categories(id) NOT NULL,
  formation TEXT NOT NULL DEFAULT '4-4-2',
  lineup JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(club_id, category_id)
);

-- Create index for faster lookups
CREATE INDEX idx_category_lineups_club_category ON category_lineups(club_id, category_id);

-- Add trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_category_lineups_timestamp
BEFORE UPDATE ON category_lineups
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
