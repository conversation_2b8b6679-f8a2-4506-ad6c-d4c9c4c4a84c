import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useYouthPlayersStore } from "@/store/useYouthPlayersStore";
import { differenceInYears, parseISO } from "date-fns";
import type { YouthPlayer } from "@/api/api";

export interface YouthPlayerDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  player?: YouthPlayer;
  editMode?: boolean;
  clubId: number; // Adicionando o clubId como propriedade obrigatória
}

export function YouthPlayerDialog({ open, onOpenChange, player, editMode, clubId }: YouthPlayerDialogProps) {
  const [name, setName] = useState(player?.name || "");
  const [birthdate, setBirthdate] = useState(player?.birthdate || "");
  const [position, setPosition] = useState(player?.position || "");
  const [height, setHeight] = useState(player?.height?.toString() || "");
  const [weight, setWeight] = useState(player?.weight?.toString() || "");
  const [nationality, setNationality] = useState(player?.nationality || "");
  const [number, setNumber] = useState(player?.number?.toString() || "");
  const [status, setStatus] = useState(player?.status || "ativo");
  const [category, setCategory] = useState(player?.category || "Sub-20");
  const [potential, setPotential] = useState(player?.potential?.toString() || "70");
  const [since, setSince] = useState(player?.since || new Date().toISOString().split('T')[0]);
  const [error, setError] = useState("");

  const { addYouthPlayer, updateYouthPlayer, loading } = useYouthPlayersStore();

  const handleSave = async () => {
    if (!name || !position || !birthdate) {
      setError("Preencha todos os campos obrigatórios.");
      return;
    }

    const playerData = {
      name,
      position,
      birthdate,
      age: birthdate ? differenceInYears(new Date(), parseISO(birthdate)) : 16,
      height: height ? parseFloat(height) : undefined,
      weight: weight ? parseFloat(weight) : undefined,
      nationality,
      number: number ? parseInt(number, 10) : 0,
      status,
      club_id: clubId,
      category,
      potential: potential ? parseInt(potential, 10) : 70,
      since,
    };

    try {
      if (editMode && player?.id) {
        await updateYouthPlayer(clubId, player.id, playerData);
      } else {
        await addYouthPlayer(clubId, playerData);
      }
      onOpenChange(false);
    } catch (err: unknown) {
      const error = err as Error;
      setError(error.message || "Erro ao salvar jogador da base");
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[550px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{editMode ? "Editar Jogador da Base" : "Adicionar Jogador da Base"}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div>
            <Label htmlFor="name">Nome</Label>
            <Input 
              id="name"
              placeholder="Nome completo" 
              value={name} 
              onChange={e => setName(e.target.value)} 
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="position">Posição</Label>
              <Select value={position} onValueChange={setPosition}>
                <SelectTrigger id="position">
                  <SelectValue placeholder="Posição" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Goleiro">Goleiro</SelectItem>
                  <SelectItem value="Zagueiro">Zagueiro</SelectItem>
                  <SelectItem value="Lateral Direito">Lateral Direito</SelectItem>
                  <SelectItem value="Lateral Esquerdo">Lateral Esquerdo</SelectItem>
                  <SelectItem value="Volante">Volante</SelectItem>
                  <SelectItem value="Meio-campo">Meio-campo</SelectItem>
                  <SelectItem value="Atacante">Atacante</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="category">Categoria</Label>
              <Select value={category} onValueChange={setCategory}>
                <SelectTrigger id="category">
                  <SelectValue placeholder="Categoria" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Sub-15">Sub-15</SelectItem>
                  <SelectItem value="Sub-17">Sub-17</SelectItem>
                  <SelectItem value="Sub-20">Sub-20</SelectItem>
                  <SelectItem value="Sub-23">Sub-23</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="birthdate">Data de Nascimento</Label>
              <Input 
                id="birthdate"
                type="date" 
                value={birthdate} 
                onChange={e => setBirthdate(e.target.value)} 
              />
            </div>
            
            <div>
              <Label htmlFor="number">Número</Label>
              <Input 
                id="number"
                type="number"
                min="1"
                max="99"
                placeholder="Número" 
                value={number} 
                onChange={e => setNumber(e.target.value)} 
              />
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="height">Altura (cm)</Label>
              <Input 
                id="height"
                type="number"
                min="120"
                max="220"
                placeholder="Altura em cm" 
                value={height} 
                onChange={e => setHeight(e.target.value)} 
              />
            </div>
            
            <div>
              <Label htmlFor="weight">Peso (kg)</Label>
              <Input 
                id="weight"
                type="number"
                min="30"
                max="120"
                placeholder="Peso em kg" 
                value={weight} 
                onChange={e => setWeight(e.target.value)} 
              />
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="since">No clube desde</Label>
              <Input 
                id="since"
                type="date" 
                value={since} 
                onChange={e => setSince(e.target.value)} 
              />
            </div>
            
            <div>
              <Label htmlFor="nationality">Nacionalidade</Label>
              <Input 
                id="nationality"
                placeholder="Nacionalidade" 
                value={nationality} 
                onChange={e => setNationality(e.target.value)} 
              />
            </div>
          </div>
          
          <div>
            <Label htmlFor="potential">Potencial (1-100)</Label>
            <Input 
              id="potential"
              type="range"
              min="1"
              max="100"
              value={potential}
              onChange={e => setPotential(e.target.value)}
              className="w-full" 
            />
            <div className="text-center mt-1">{potential}</div>
          </div>
          
          <div>
            <Label htmlFor="status">Status</Label>
            <Select value={status} onValueChange={setStatus}>
              <SelectTrigger id="status">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ativo">Ativo</SelectItem>
                <SelectItem value="lesionado">Lesionado</SelectItem>
                <SelectItem value="suspenso">Suspenso</SelectItem>
                <SelectItem value="emprestado">Emprestado</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {error && <div className="text-red-500 text-sm">{error}</div>}
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button 
            onClick={handleSave} 
            disabled={!name || !position || !birthdate || loading}
          >
            {editMode ? "Atualizar" : "Adicionar"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
