-- Create player_accounts table to track player accounts and their expiration dates
CREATE TABLE IF NOT EXISTS player_accounts (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id),
  player_id UUID REFERENCES players(id),
  user_id UUID REFERENCES auth.users(id),
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add index for faster lookups
CREATE INDEX IF NOT EXISTS idx_player_accounts_player_id ON player_accounts(player_id);
CREATE INDEX IF NOT EXISTS idx_player_accounts_user_id ON player_accounts(user_id);

-- Add function to update player account expiration date when contract end date changes
CREATE OR REPLACE FUNCTION update_player_account_expiration()
RETURNS TRIGGER AS $$
BEGIN
  -- If contract_end_date is updated and the player has a user account
  IF (NEW.contract_end_date IS DISTINCT FROM OLD.contract_end_date) AND (NEW.user_id IS NOT NULL) THEN
    -- Update the player_accounts table with the new expiration date
    UPDATE player_accounts
    SET expires_at = NEW.contract_end_date
    WHERE player_id = NEW.id AND club_id = NEW.club_id;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update player account expiration date
DROP TRIGGER IF EXISTS update_player_account_expiration_trigger ON players;
CREATE TRIGGER update_player_account_expiration_trigger
AFTER UPDATE ON players
FOR EACH ROW
WHEN (NEW.contract_end_date IS DISTINCT FROM OLD.contract_end_date)
EXECUTE FUNCTION update_player_account_expiration();
