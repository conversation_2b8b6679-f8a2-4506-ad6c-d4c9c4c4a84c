import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { toast } from "@/hooks/use-toast";
import { jsPDF } from "jspdf";
import autoTable from 'jspdf-autotable';
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { useCurrentClubId } from "@/context/ClubContext";
import { getClubInfo } from "@/api";
import { getCashFlowByDateRange, CashFlowEntry } from "@/api/financialReports";
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

interface CashFlowReportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// Interface for jsPDF with autoTable
interface jsPDFWithAutoTable extends jsPDF {
  autoTable: typeof autoTable;
  internal: {
    getNumberOfPages: () => number;
    pageSize: {
      width: number;
      height: number;
    };
  };
}

export function CashFlowReportDialog({ 
  open, 
  onOpenChange
}: CashFlowReportDialogProps) {
  const [startDate, setStartDate] = useState<Date>(new Date());
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [loading, setLoading] = useState(false);
  const clubId = useCurrentClubId();

  const handleGenerate = async () => {
    if (!clubId) {
      toast({
        title: "Erro",
        description: "ID do clube não encontrado.",
        variant: "destructive",
      });
      return;
    }

    if (!startDate || !endDate) {
      toast({
        title: "Erro",
        description: "Por favor, selecione as datas de início e fim.",
        variant: "destructive",
      });
      return;
    }

    if (startDate > endDate) {
      toast({
        title: "Erro",
        description: "A data de início deve ser anterior à data de fim.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);

    try {
      // Get club info
      const clubInfo = await getClubInfo(clubId);
      
      // Get cash flow data
      const startDateStr = format(startDate, "yyyy-MM-dd");
      const endDateStr = format(endDate, "yyyy-MM-dd");
      const cashFlowEntries = await getCashFlowByDateRange(clubId, startDateStr, endDateStr);

      // Create PDF
      const doc = new jsPDF("portrait", "mm", "a4") as jsPDFWithAutoTable;
      
      // Add header
      const pageWidth = doc.internal.pageSize.width;
      doc.setFontSize(18);
      doc.text("Relatório de Fluxo de Caixa", pageWidth / 2, 15, { align: "center" });
      
      // Add club info
      doc.setFontSize(12);
      doc.text(clubInfo.name, pageWidth / 2, 25, { align: "center" });
      if (clubInfo.address) {
        doc.setFontSize(10);
        doc.text(clubInfo.address, pageWidth / 2, 30, { align: "center" });
      }

      // Add period and generation date
      doc.setFontSize(10);
      doc.text(`Período: ${format(startDate, "dd/MM/yyyy", { locale: ptBR })} a ${format(endDate, "dd/MM/yyyy", { locale: ptBR })}`, 15, 40);
      doc.text(`Gerado em: ${format(new Date(), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}`, 15, 45);

      // Calculate summary
      const totalReceitas = cashFlowEntries
        .filter(entry => entry.type === 'receita')
        .reduce((sum, entry) => sum + entry.amount, 0);
      
      const totalDespesas = cashFlowEntries
        .filter(entry => entry.type === 'despesa')
        .reduce((sum, entry) => sum + entry.amount, 0);

      const saldoFinal = cashFlowEntries.length > 0 ? cashFlowEntries[cashFlowEntries.length - 1].running_balance : 0;

      // Add summary
      doc.setFontSize(12);
      doc.text("Resumo do Período:", 15, 55);
      
      doc.setFontSize(10);
      doc.text(`Total de Receitas: R$ ${totalReceitas.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, 15, 62);
      doc.text(`Total de Despesas: R$ ${totalDespesas.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, 15, 67);
      doc.text(`Saldo do Período: R$ ${(totalReceitas - totalDespesas).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, 15, 72);
      doc.text(`Saldo Final: R$ ${saldoFinal.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, 15, 77);

      // Prepare table data
      const tableData = cashFlowEntries.map(entry => [
        format(new Date(entry.date), "dd/MM/yyyy", { locale: ptBR }),
        entry.type === 'receita' ? 'Receita' : 'Despesa',
        entry.description,
        entry.category,
        entry.type === 'receita' 
          ? `R$ ${entry.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`
          : '',
        entry.type === 'despesa' 
          ? `R$ ${entry.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`
          : '',
        `R$ ${entry.running_balance.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`
      ]);

      // Add table
      autoTable(doc, {
        head: [["Data", "Tipo", "Descrição", "Categoria", "Receita", "Despesa", "Saldo"]],
        body: tableData,
        startY: 85,
        styles: {
          fontSize: 8,
          cellPadding: 2,
        },
        headStyles: {
          fillColor: [41, 128, 185],
          textColor: 255,
          fontStyle: "bold",
        },
        alternateRowStyles: {
          fillColor: [240, 240, 240],
        },
        columnStyles: {
          4: { halign: 'right', textColor: [0, 128, 0] }, // Receita - verde
          5: { halign: 'right', textColor: [255, 0, 0] }, // Despesa - vermelho
          6: { halign: 'right', fontStyle: 'bold' }, // Saldo - negrito
        },
        margin: { left: 15, right: 15 },
      });

      // Add page numbers
      const pageCount = doc.internal.getNumberOfPages();
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setFontSize(8);
        doc.text(`Página ${i} de ${pageCount}`, pageWidth / 2, doc.internal.pageSize.height - 10, { align: "center" });
      }
      
      // Save the PDF
      const reportTitle = `Fluxo_de_Caixa_${format(startDate, "dd-MM-yyyy")}_a_${format(endDate, "dd-MM-yyyy")}.pdf`;
      doc.save(reportTitle);
      
      toast({
        title: "Relatório gerado",
        description: "O relatório de fluxo de caixa foi gerado com sucesso.",
      });

      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao gerar relatório:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao gerar o relatório.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Relatório de Fluxo de Caixa</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="startDate">Data de Início</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !startDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, "dd/MM/yyyy", { locale: ptBR }) : "Selecione a data"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={(date) => date && setStartDate(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div>
              <Label htmlFor="endDate">Data de Fim</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !endDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "dd/MM/yyyy", { locale: ptBR }) : "Selecione a data"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={(date) => date && setEndDate(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleGenerate} disabled={loading}>
            {loading ? "Gerando..." : "Gerar Relatório"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
