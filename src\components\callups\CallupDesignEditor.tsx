import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import {
  ImagePlus,
  Trash2,
  Plus,
  Users,
  Calendar,
  Home,
  Shirt,
  Award,
  UserCog,
  Stethoscope,
  Briefcase,
  Info
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { Callup, CallupPlayer } from "@/api/callups";
import { Category } from "@/api/api";
import { ClubInfo } from "@/api/api";
import { ClubUser } from "@/api/api";
import { Player } from "@/api/api";
import { Collaborator } from "@/api/collaborators";
import { CallupImageUpload } from "./CallupImageUpload";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";

interface CallupDesignEditorProps {
  callup: Partial<Callup>;
  onCallupChange: (callup: Partial<Callup>) => void;
  players: CallupPlayer[];
  onPlayersChange: (players: CallupPlayer[]) => void;
  categoryPlayers: Player[];
  clubUsers: ClubUser[];
  collaborators: Collaborator[];
  clubInfo: ClubInfo | null;
  categories: Category[];
}

export function CallupDesignEditor({
  callup,
  onCallupChange,
  players,
  onPlayersChange,
  categoryPlayers,
  clubUsers,
  collaborators,
  clubInfo,
  categories
}: CallupDesignEditorProps) {
  const [activeTab, setActiveTab] = useState("images");
  const [showPlayerDialog, setShowPlayerDialog] = useState(false);
  const [showStaffDialog, setShowStaffDialog] = useState(false);
  const [selectedFields, setSelectedFields] = useState({
    players: true,
    schedule: true,
    hotel: true,
    notices: true,
    staff: true,
    technical: true,
    executive: true
  });

  // Função para atualizar um campo do callup
  const updateCallup = (field: string, value: any) => {
    onCallupChange({
      ...callup,
      [field]: value
    });
  };

  // Função para adicionar um jogador à convocação
  const addPlayer = (playerId: string, role: string = "Atleta") => {
    const newPlayer: CallupPlayer = {
      id: 0, // Será definido pelo backend
      club_id: callup.club_id || 0,
      callup_id: callup.id || 0,
      player_id: playerId,
      role: role,
      player_name: categoryPlayers.find(p => p.id === playerId)?.name || ""
    };

    onPlayersChange([...players, newPlayer]);
  };

  // Função para adicionar um membro da comissão à convocação
  const addStaff = (userId: string, role: string) => {
    // Verificar se o ID é de um colaborador (começa com número) ou de um usuário
    const isCollaborator = !isNaN(Number(userId));

    let userName = "";

    if (isCollaborator) {
      // Buscar o nome do colaborador
      const collaborator = collaborators.find(c => c.id.toString() === userId);
      userName = collaborator?.full_name || "";
    } else {
      // Buscar o nome do usuário
      userName = clubUsers.find(u => u.id === userId)?.name || "";
    }

    const newStaff: CallupPlayer = {
      id: 0, // Será definido pelo backend
      club_id: callup.club_id || 0,
      callup_id: callup.id || 0,
      user_id: userId,
      role: role,
      user_name: userName
    };

    onPlayersChange([...players, newStaff]);
  };

  // Função para remover um membro da convocação
  const removeCallupMember = (index: number) => {
    const updatedPlayers = [...players];
    updatedPlayers.splice(index, 1);
    onPlayersChange(updatedPlayers);
  };

  // Função para adicionar um jogador manualmente
  const addManualPlayer = (name: string, role: string = "Atleta") => {
    const newPlayer: CallupPlayer = {
      id: 0,
      club_id: callup.club_id || 0,
      callup_id: callup.id || 0,
      role: role,
      player_name: name
    };

    onPlayersChange([...players, newPlayer]);
  };

  // Função para adicionar um membro da comissão manualmente
  const addManualStaff = (name: string, role: string) => {
    const newStaff: CallupPlayer = {
      id: 0,
      club_id: callup.club_id || 0,
      callup_id: callup.id || 0,
      role: role,
      user_name: name
    };

    onPlayersChange([...players, newStaff]);
  };

  // Função para atualizar os campos selecionados
  const toggleField = (field: string, checked: boolean) => {
    setSelectedFields({
      ...selectedFields,
      [field]: checked
    });
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-3 w-full">
          <TabsTrigger value="images">Imagens</TabsTrigger>
          <TabsTrigger value="content">Conteúdo</TabsTrigger>
          <TabsTrigger value="layout">Layout</TabsTrigger>
        </TabsList>

        <TabsContent value="images" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Emblema do Clube Mandante</CardTitle>
              </CardHeader>
              <CardContent>
                <CallupImageUpload
                  imageUrl={callup.home_club_logo || ""}
                  onImageChange={(url) => updateCallup("home_club_logo", url)}
                  maxWidth={200}
                  maxHeight={200}
                  clubId={clubInfo?.id}
                  imageType="home-logo"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Emblema do Clube Visitante</CardTitle>
              </CardHeader>
              <CardContent>
                <CallupImageUpload
                  imageUrl={callup.away_club_logo || ""}
                  onImageChange={(url) => updateCallup("away_club_logo", url)}
                  maxWidth={200}
                  maxHeight={200}
                  clubId={clubInfo?.id}
                  imageType="away-logo"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Imagem da Competição</CardTitle>
              </CardHeader>
              <CardContent>
                <CallupImageUpload
                  imageUrl={callup.competition_image || ""}
                  onImageChange={(url) => updateCallup("competition_image", url)}
                  maxWidth={200}
                  maxHeight={200}
                  clubId={clubInfo?.id}
                  imageType="competition"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Imagem do Estádio</CardTitle>
              </CardHeader>
              <CardContent>
                <CallupImageUpload
                  imageUrl={callup.stadium_image || ""}
                  onImageChange={(url) => updateCallup("stadium_image", url)}
                  maxWidth={200}
                  maxHeight={200}
                  clubId={clubInfo?.id}
                  imageType="stadium"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Imagem do Hotel</CardTitle>
              </CardHeader>
              <CardContent>
                <CallupImageUpload
                  imageUrl={callup.hotel_image || ""}
                  onImageChange={(url) => updateCallup("hotel_image", url)}
                  maxWidth={200}
                  maxHeight={200}
                  clubId={clubInfo?.id}
                  imageType="hotel"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Imagem do Uniforme</CardTitle>
              </CardHeader>
              <CardContent>
                <CallupImageUpload
                  imageUrl={callup.uniform_image || ""}
                  onImageChange={(url) => updateCallup("uniform_image", url)}
                  maxWidth={200}
                  maxHeight={200}
                  clubId={clubInfo?.id}
                  imageType="uniform"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Imagem do Ônibus</CardTitle>
              </CardHeader>
              <CardContent>
                <CallupImageUpload
                  imageUrl={callup.bus_image || ""}
                  onImageChange={(url) => updateCallup("bus_image", url)}
                  maxWidth={200}
                  maxHeight={200}
                  clubId={clubInfo?.id}
                  imageType="bus"
                />
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Patrocinador 1</CardTitle>
              </CardHeader>
              <CardContent>
                <CallupImageUpload
                  imageUrl={callup.sponsor_image1 || ""}
                  onImageChange={(url) => updateCallup("sponsor_image1", url)}
                  maxWidth={200}
                  maxHeight={100}
                  clubId={clubInfo?.id}
                  imageType="sponsor1"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Patrocinador 2</CardTitle>
              </CardHeader>
              <CardContent>
                <CallupImageUpload
                  imageUrl={callup.sponsor_image2 || ""}
                  onImageChange={(url) => updateCallup("sponsor_image2", url)}
                  maxWidth={200}
                  maxHeight={100}
                  clubId={clubInfo?.id}
                  imageType="sponsor2"
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="content" className="space-y-4">
          {/* Conteúdo da convocação */}
          <Card>
            <CardHeader>
              <CardTitle>Programação do Jogo</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                placeholder="Detalhes da programação do jogo..."
                value={callup.match_schedule || ""}
                onChange={(e) => updateCallup("match_schedule", e.target.value)}
                className="min-h-[150px]"
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Controle de Hospedagem</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                placeholder="Informações sobre hospedagem..."
                value={callup.hotel_control || ""}
                onChange={(e) => updateCallup("hotel_control", e.target.value)}
                className="min-h-[150px]"
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Jogadores Convocados</CardTitle>
              <Button size="sm" onClick={() => setShowPlayerDialog(true)}>
                <Plus className="h-4 w-4 mr-1" />
                Adicionar
              </Button>
            </CardHeader>
            <CardContent>
              {players.filter(p => p.role === "Atleta").length === 0 ? (
                <div className="text-center py-4 text-muted-foreground">
                  Nenhum jogador convocado
                </div>
              ) : (
                <div className="space-y-2">
                  {players
                    .filter(p => p.role === "Atleta")
                    .map((player, index) => (
                      <div key={index} className="flex items-center justify-between border-b pb-2">
                        <div>{player.player_name || player.user_name}</div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeCallupMember(players.indexOf(player))}
                        >
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      </div>
                    ))}
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Comissão Técnica e Staff</CardTitle>
              <Button size="sm" onClick={() => setShowStaffDialog(true)}>
                <Plus className="h-4 w-4 mr-1" />
                Adicionar
              </Button>
            </CardHeader>
            <CardContent>
              {players.filter(p => p.role !== "Atleta").length === 0 ? (
                <div className="text-center py-4 text-muted-foreground">
                  Nenhum membro da comissão adicionado
                </div>
              ) : (
                <div className="space-y-2">
                  {players
                    .filter(p => p.role !== "Atleta")
                    .map((staff, index) => (
                      <div key={index} className="flex items-center justify-between border-b pb-2">
                        <div className="flex flex-col">
                          <span>{staff.player_name || staff.user_name}</span>
                          <span className="text-xs text-muted-foreground">{staff.role}</span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeCallupMember(players.indexOf(staff))}
                        >
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      </div>
                    ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="layout" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Campos a Exibir no Relatório</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="players"
                    checked={selectedFields.players}
                    onCheckedChange={(checked) => toggleField("players", checked as boolean)}
                  />
                  <Label htmlFor="players" className="flex items-center">
                    <Users className="h-4 w-4 mr-2" />
                    Jogadores Convocados
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="schedule"
                    checked={selectedFields.schedule}
                    onCheckedChange={(checked) => toggleField("schedule", checked as boolean)}
                  />
                  <Label htmlFor="schedule" className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2" />
                    Programação do Jogo
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="hotel"
                    checked={selectedFields.hotel}
                    onCheckedChange={(checked) => toggleField("hotel", checked as boolean)}
                  />
                  <Label htmlFor="hotel" className="flex items-center">
                    <Home className="h-4 w-4 mr-2" />
                    Controle de Hospedagem
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="notices"
                    checked={selectedFields.notices}
                    onCheckedChange={(checked) => toggleField("notices", checked as boolean)}
                  />
                  <Label htmlFor="notices" className="flex items-center">
                    <Info className="h-4 w-4 mr-2" />
                    Avisos Importantes
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="staff"
                    checked={selectedFields.staff}
                    onCheckedChange={(checked) => toggleField("staff", checked as boolean)}
                  />
                  <Label htmlFor="staff" className="flex items-center">
                    <UserCog className="h-4 w-4 mr-2" />
                    Staff
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="technical"
                    checked={selectedFields.technical}
                    onCheckedChange={(checked) => toggleField("technical", checked as boolean)}
                  />
                  <Label htmlFor="technical" className="flex items-center">
                    <Stethoscope className="h-4 w-4 mr-2" />
                    Comissão Técnica
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="executive"
                    checked={selectedFields.executive}
                    onCheckedChange={(checked) => toggleField("executive", checked as boolean)}
                  />
                  <Label htmlFor="executive" className="flex items-center">
                    <Briefcase className="h-4 w-4 mr-2" />
                    Diretoria Executiva
                  </Label>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Diálogo para adicionar jogadores */}
      <AddPlayerDialog
        open={showPlayerDialog}
        onOpenChange={setShowPlayerDialog}
        players={categoryPlayers}
        onAddPlayer={addPlayer}
        onAddManualPlayer={addManualPlayer}
        existingPlayers={players.filter(p => p.role === "Atleta")}
      />

      {/* Diálogo para adicionar membros da comissão */}
      <AddStaffDialog
        open={showStaffDialog}
        onOpenChange={setShowStaffDialog}
        users={clubUsers}
        collaborators={collaborators}
        onAddStaff={addStaff}
        onAddManualStaff={addManualStaff}
        existingStaff={players.filter(p => p.role !== "Atleta")}
      />
    </div>
  );
}

// Componente para adicionar jogadores
interface AddPlayerDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  players: Player[];
  onAddPlayer: (playerId: string, role?: string) => void;
  onAddManualPlayer: (name: string, role?: string) => void;
  existingPlayers: CallupPlayer[];
}

function AddPlayerDialog({
  open,
  onOpenChange,
  players,
  onAddPlayer,
  onAddManualPlayer,
  existingPlayers
}: AddPlayerDialogProps) {
  const [activeTab, setActiveTab] = useState("registered");
  const [manualName, setManualName] = useState("");
  const [searchTerm, setSearchTerm] = useState("");

  const filteredPlayers = players.filter(player =>
    player.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
    !existingPlayers.some(p => p.player_id === player.id)
  );

  const handleAddManualPlayer = () => {
    if (manualName.trim()) {
      onAddManualPlayer(manualName.trim());
      setManualName("");
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Adicionar Jogador</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="registered">Jogadores Cadastrados</TabsTrigger>
            <TabsTrigger value="manual">Adicionar Manualmente</TabsTrigger>
          </TabsList>

          <TabsContent value="registered" className="space-y-4">
            <Input
              placeholder="Buscar jogador..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />

            <ScrollArea className="h-[300px]">
              {filteredPlayers.length === 0 ? (
                <div className="text-center py-4 text-muted-foreground">
                  Nenhum jogador encontrado
                </div>
              ) : (
                <div className="space-y-2">
                  {filteredPlayers.map((player) => (
                    <div key={player.id} className="flex items-center justify-between border-b pb-2">
                      <div>{player.name}</div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          onAddPlayer(player.id);
                          onOpenChange(false);
                        }}
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        Adicionar
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </TabsContent>

          <TabsContent value="manual" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="manual-name">Nome do Jogador</Label>
              <Input
                id="manual-name"
                placeholder="Digite o nome do jogador..."
                value={manualName}
                onChange={(e) => setManualName(e.target.value)}
              />
            </div>

            <Button
              onClick={handleAddManualPlayer}
              disabled={!manualName.trim()}
              className="w-full"
            >
              Adicionar Jogador
            </Button>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}

// Componente para adicionar membros da comissão
interface AddStaffDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  users: ClubUser[];
  collaborators: Collaborator[];
  onAddStaff: (userId: string, role: string) => void;
  onAddManualStaff: (name: string, role: string) => void;
  existingStaff: CallupPlayer[];
}

function AddStaffDialog({
  open,
  onOpenChange,
  users,
  collaborators,
  onAddStaff,
  onAddManualStaff,
  existingStaff
}: AddStaffDialogProps) {
  const [activeTab, setActiveTab] = useState("registered");
  const [manualName, setManualName] = useState("");
  const [manualRole, setManualRole] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRole, setSelectedRole] = useState("Técnico");

  const staffRoles = [
    "Técnico",
    "Auxiliar Técnico",
    "Preparador Físico",
    "Fisioterapeuta",
    "Médico",
    "Massagista",
    "Roupeiro",
    "Diretor",
    "Presidente"
  ];

  // Filter collaborators
  const filteredCollaborators = collaborators.filter(collab =>
    collab.full_name.toLowerCase().includes(searchTerm.toLowerCase()) &&
    !existingStaff.some(s => s.user_id === collab.id.toString())
  );

  // Convert collaborators to a format compatible with the component
  const collaboratorsAsUsers = filteredCollaborators.map(collab => ({
    id: collab.id.toString(),
    name: collab.full_name,
    role: collab.role,
    isCollaborator: true
  }));

  const handleAddManualStaff = () => {
    if (manualName.trim() && manualRole.trim()) {
      onAddManualStaff(manualName.trim(), manualRole.trim());
      setManualName("");
      setManualRole("");
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Adicionar Membro da Comissão</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="registered">Membros Cadastrados</TabsTrigger>
            <TabsTrigger value="manual">Adicionar Manualmente</TabsTrigger>
          </TabsList>

          <TabsContent value="registered" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="role">Função</Label>
              <select
                id="role"
                className="w-full p-2 border rounded-md"
                value={selectedRole}
                onChange={(e) => setSelectedRole(e.target.value)}
              >
                {staffRoles.map((role) => (
                  <option key={role} value={role}>
                    {role}
                  </option>
                ))}
              </select>
            </div>

            <Input
              placeholder="Buscar membro..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />

            <ScrollArea className="h-[250px]">
              {collaboratorsAsUsers.length === 0 ? (
                <div className="text-center py-4 text-muted-foreground">
                  Nenhum membro encontrado
                </div>
              ) : (
                <div className="space-y-2">
                  {collaboratorsAsUsers.map((user) => (
                    <div key={user.id} className="flex items-center justify-between border-b pb-2">
                      <div>{user.name}</div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          onAddStaff(user.id, selectedRole);
                          onOpenChange(false);
                        }}
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        Adicionar
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </TabsContent>

          <TabsContent value="manual" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="manual-name">Nome</Label>
              <Input
                id="manual-name"
                placeholder="Digite o nome..."
                value={manualName}
                onChange={(e) => setManualName(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="manual-role">Função</Label>
              <Input
                id="manual-role"
                placeholder="Digite a função..."
                value={manualRole}
                onChange={(e) => setManualRole(e.target.value)}
                list="role-suggestions"
              />
              <datalist id="role-suggestions">
                {staffRoles.map((role) => (
                  <option key={role} value={role} />
                ))}
              </datalist>
            </div>

            <Button
              onClick={handleAddManualStaff}
              disabled={!manualName.trim() || !manualRole.trim()}
              className="w-full"
            >
              Adicionar Membro
            </Button>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
