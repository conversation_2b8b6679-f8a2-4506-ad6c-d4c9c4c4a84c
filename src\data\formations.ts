
export type FormationSlots = {
  goalkeeper: number;
  defender: number;
  midfielder: number;
  attacker: number;
};

export interface Formation {
  name: string;
  schema: FormationSlots;
  display?: string;
}

export const formations: Formation[] = [
  { name: "4-4-2", schema: { goalkeeper: 1, defender: 4, midfielder: 4, attacker: 2 }, display: "4-4-2" },
  { name: "4-3-3", schema: { goalkeeper: 1, defender: 4, midfielder: 3, attacker: 3 }, display: "4-3-3" },
  { name: "3-5-2", schema: { goalkeeper: 1, defender: 3, midfielder: 5, attacker: 2 }, display: "3-5-2" },
  { name: "5-3-2", schema: { goalkeeper: 1, defender: 5, midfielder: 3, attacker: 2 }, display: "5-3-2" },
  { name: "4-2-3-1", schema: { goalkeeper: 1, defender: 4, midfielder: 5, attacker: 1 }, display: "4-2-3-1" },
];
