-- Função para confirmar o email de um usuário diretamente no banco de dados
-- Esta função deve ser executada no banco de dados do Supabase
-- Ela atualiza o campo email_confirmed_at na tabela auth.users

CREATE OR REPLACE FUNCTION confirm_user_email(user_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE auth.users
  SET email_confirmed_at = NOW()
  WHERE id = user_id;
END;
$$ LANGUAGE plpgsql;

-- Exemplo de uso:
-- SELECT confirm_user_email('user-uuid-here');
