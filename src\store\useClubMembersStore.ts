import { create } from "zustand";
import { ClubMember, getClubMembers, addUserToClub } from "../api/api";
import { removeUserFromClub } from "../api/users";

interface ClubMembersState {
  members: ClubMember[];
  loading: boolean;
  error: string | null;
  fetchMembers: (clubId: number) => Promise<void>;
  addMember: (userId: string, clubId: number, role: string) => Promise<void>;
  removeMember: (userId: string, clubId: number) => Promise<void>;
}

export const useClubMembersStore = create<ClubMembersState>((set, get) => ({
  members: [],
  loading: false,
  error: null,

  fetchMembers: async (clubId: number) => {
    set({ loading: true, error: null });
    try {
      // Garantir que clubId seja tratado como número
      const clubIdNumber = Number(clubId);

      if (isNaN(clubIdNumber)) {
        throw new Error(`ID do clube inválido: ${clubId}`);
      }

      const members = await getClubMembers(clubIdNumber);
      set({ members, loading: false });
    } catch (err: unknown) {
      console.error("Erro ao buscar membros do clube:", err);
      set({ error: err instanceof Error ? err.message : "Erro ao buscar membros do clube", loading: false });
    }
  },

  addMember: async (userId: string, clubId: number, role: string) => {
    set({ loading: true, error: null });
    try {
      // Garantir que clubId seja tratado como número
      const clubIdNumber = Number(clubId);

      if (isNaN(clubIdNumber)) {
        throw new Error(`ID do clube inválido: ${clubId}`);
      }

      const member = await addUserToClub(userId, clubIdNumber, role);
      set({ members: [...get().members, member], loading: false });
    } catch (err: unknown) {
      console.error("Erro ao adicionar membro:", err);
      set({ error: err instanceof Error ? err.message : "Erro ao adicionar membro", loading: false });
    }
  },

  removeMember: async (userId: string, clubId: number) => {
    set({ loading: true, error: null });
    try {
      // Garantir que clubId seja tratado como número
      const clubIdNumber = Number(clubId);

      if (isNaN(clubIdNumber)) {
        throw new Error(`ID do clube inválido: ${clubId}`);
      }

      await removeUserFromClub(clubIdNumber, userId);
      set({ members: get().members.filter(m => !(m.userId === userId && m.clubId === clubIdNumber)), loading: false });
    } catch (err: unknown) {
      console.error("Erro ao remover membro:", err);
      set({ error: err instanceof Error ? err.message : "Erro ao remover membro", loading: false });
    }
  },
}));
