import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import { useTasksStore } from "@/store/useTasksStore";
import { toast } from "@/hooks/use-toast";

// Nenhum uso do tipo Task ou função da API atualmente. Removido o import não utilizado e adicionado comentário para futura integração se necessário.
// import type { Task } from "@/api/api";

interface NovaTarefaDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function NovaTarefaDialog({ open, onOpenChange, clubId }: NovaTarefaDialogProps & { clubId: number }) {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [error, setError] = useState("");
  const { addTask, loading } = useTasksStore();

  const handleSave = async () => {
    if (!title.trim()) {
      setError("O título é obrigatório.");
      return;
    }
    setError("");
    try {
      await addTask(clubId, {
        title,
        completed: false,
        description,
        club_id: clubId,
        status: ""
      });
      toast({ title: "Tarefa criada com sucesso!", variant: "default" });
      setTitle("");
      setDescription("");
      onOpenChange(false);
    } catch (e) {
      setError("Erro ao salvar tarefa.");
      toast({ title: "Erro ao salvar tarefa.", variant: "destructive" });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Nova Tarefa</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <Input placeholder="Título da tarefa*" value={title} onChange={e => setTitle(e.target.value)} />
          <Input placeholder="Descrição" value={description} onChange={e => setDescription(e.target.value)} />
          {error && <div className="text-red-500 text-sm mt-1">{error}</div>}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancelar</Button>
          <Button onClick={handleSave} disabled={loading}>
            {loading ? <span className="loader mr-2" /> : null}
            Salvar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
