import { useState, useEffect } from 'react';
import { useCategoriesStore } from '@/store/useCategoriesStore';
import { useCurrentClubId } from '@/context/ClubContext';
import { Category } from '@/api/api';

export function useSelectedCategory(): {
  selectedCategoryId: number | null;
  selectedCategory: Category | null;
  setSelectedCategoryId: (id: number | null) => void;
  loading: boolean;
} {
  const clubId = useCurrentClubId();
  const { categories, fetchCategories, loading } = useCategoriesStore();
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);

  // Carregar categorias
  useEffect(() => {
    fetchCategories(clubId);
  }, [clubId, fetchCategories]);

  // Inicializar com o valor do localStorage
  useEffect(() => {
    const storedCategoryId = localStorage.getItem('selectedCategoryId');
    if (storedCategoryId) {
      setSelectedCategoryId(parseInt(storedCategoryId, 10));
    }
  }, []);

  // Atualizar o localStorage quando a categoria selecionada mudar
  useEffect(() => {
    if (selectedCategoryId) {
      localStorage.setItem('selectedCategoryId', selectedCategoryId.toString());
    } else {
      localStorage.removeItem('selectedCategoryId');
    }
  }, [selectedCategoryId]);

  // Atualizar a categoria selecionada quando as categorias forem carregadas
  useEffect(() => {
    if (categories.length > 0 && selectedCategoryId) {
      const category = categories.find(c => c.id === selectedCategoryId);
      setSelectedCategory(category || null);
    } else {
      setSelectedCategory(null);
    }
  }, [categories, selectedCategoryId]);

  return {
    selectedCategoryId,
    selectedCategory,
    setSelectedCategoryId,
    loading
  };
}
