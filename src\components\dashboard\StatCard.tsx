
import { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface StatCardProps {
  title: string;
  value: string;
  description?: string;
  icon: LucideIcon;
  trend?: "up" | "down" | "neutral";
  trendValue?: string;
  className?: string;
  onClick?: () => void;
  clickable?: boolean;
}

export function StatCard({
  title,
  value,
  description,
  icon: Icon,
  trend,
  trendValue,
  className,
  onClick,
  clickable = false,
}: StatCardProps) {
  const cardContent = (
    <>
      <div className="flex justify-between">
        <div>
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <h3 className="text-2xl font-bold mt-1">{value}</h3>
          {description && (
            <p className="text-xs text-muted-foreground mt-1">{description}</p>
          )}
          {trend && (
            <div className="flex items-center mt-2">
              <div
                className={cn(
                  "text-xs font-medium flex items-center",
                  trend === "up" && "text-emerald-600",
                  trend === "down" && "text-rose-600",
                  trend === "neutral" && "text-gray-500"
                )}
              >
                {trend === "up" && (
                  <svg
                    className="w-3 h-3 mr-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 10l7-7m0 0l7 7m-7-7v18"
                    />
                  </svg>
                )}
                {trend === "down" && (
                  <svg
                    className="w-3 h-3 mr-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 14l-7 7m0 0l-7-7m7 7V3"
                    />
                  </svg>
                )}
                {trendValue}
              </div>
            </div>
          )}
        </div>
        <div className="bg-primary/10 p-3 rounded-lg">
          <Icon className="h-6 w-6 text-primary" />
        </div>
      </div>
      {clickable && (
        <div className="absolute inset-0 bg-black/5 opacity-0 hover:opacity-100 transition-opacity rounded-xl flex items-center justify-center">
          <span className="text-sm font-medium text-gray-700">Clique para ver detalhes</span>
        </div>
      )}
    </>
  );

  if (clickable && onClick) {
    return (
      <div
        className={cn(
          "bg-white p-6 rounded-xl shadow-sm border relative cursor-pointer hover:shadow-md transition-shadow",
          className
        )}
        onClick={onClick}
      >
        {cardContent}
      </div>
    );
  }

  return (
    <div className={cn("bg-white p-6 rounded-xl shadow-sm border", className)}>
      {cardContent}
    </div>
  );
}
