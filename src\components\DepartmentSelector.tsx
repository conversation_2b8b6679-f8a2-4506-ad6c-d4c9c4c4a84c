import { useState, useEffect } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { useCurrentClubId } from "@/context/ClubContext";
import { getDepartments, Department } from "@/api/api";

interface DepartmentSelectorProps {
  value: string;
  onChange: (value: string) => void;
  label?: string;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
}

export function DepartmentSelector({
  value,
  onChange,
  label = "Departamento",
  placeholder = "Selecione um departamento",
  disabled = false,
  required = false,
  className = "",
}: DepartmentSelectorProps) {
  const clubId = useCurrentClubId();
  const [departments, setDepartments] = useState<Department[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        setLoading(true);
        const data = await getDepartments(clubId);
        setDepartments(data);
        setError(null);
      } catch (err: any) {
        console.error("Erro ao carregar departamentos:", err);
        setError(err.message || "Erro ao carregar departamentos");
      } finally {
        setLoading(false);
      }
    };

    fetchDepartments();
  }, [clubId]);

  return (
    <div className={className}>
      {label && (
        <Label htmlFor="department-selector" className="mb-2 block">
          {label} {required && <span className="text-red-500">*</span>}
        </Label>
      )}
      <Select
        value={value}
        onValueChange={onChange}
        disabled={disabled || loading || departments.length === 0}
      >
        <SelectTrigger id="department-selector" className="w-full">
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          {departments.map((department) => (
            <SelectItem key={department.id} value={department.id.toString()}>
              {department.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
      {loading && <p className="text-gray-500 text-sm mt-1">Carregando departamentos...</p>}
      {!loading && departments.length === 0 && !error && (
        <p className="text-gray-500 text-sm mt-1">Nenhum departamento encontrado</p>
      )}
    </div>
  );
}
