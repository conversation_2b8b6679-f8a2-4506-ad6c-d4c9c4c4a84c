import { supabase } from "@/integrations/supabase/client";

/**
 * Sincroniza os roles dos colaboradores com os roles dos usuários correspondentes
 * @param clubId ID do clube
 * @returns Número de usuários atualizados
 */
export async function syncCollaboratorRoles(clubId: number): Promise<number> {
  try {
    console.log(`Sincronizando roles de colaboradores para o clube ${clubId}`);

    // Buscar todos os colaboradores que têm user_id
    const { data: collaborators, error: collaboratorsError } = await supabase
      .from("collaborators")
      .select("id, user_id, role")
      .eq("club_id", clubId)
      .not("user_id", "is", null);

    if (collaboratorsError) {
      console.error("Erro ao buscar colaboradores:", collaboratorsError);
      throw new Error(`Erro ao buscar colaboradores: ${collaboratorsError.message}`);
    }

    if (!collaborators || collaborators.length === 0) {
      console.log("Nenhum colaborador com user_id encontrado");
      return 0;
    }

    console.log(`Encontrados ${collaborators.length} colaboradores com user_id`);

    // Atualizar o role de cada usuário
    let updatedCount = 0;
    for (const collaborator of collaborators) {
      if (!collaborator.user_id || !collaborator.role) continue;

      try {
        const { error: updateError } = await supabase
          .from("users")
          .update({ role: collaborator.role })
          .eq("id", collaborator.user_id);

        if (updateError) {
          console.error(`Erro ao atualizar role do usuário ${collaborator.user_id}:`, updateError);
        } else {
          console.log(`Role do usuário ${collaborator.user_id} atualizado para ${collaborator.role}`);
          updatedCount++;
        }
      } catch (error) {
        console.error(`Erro ao atualizar usuário ${collaborator.user_id}:`, error);
      }
    }

    console.log(`${updatedCount} usuários atualizados com sucesso`);
    return updatedCount;
  } catch (error: any) {
    console.error("Erro ao sincronizar roles:", error);
    throw new Error(error.message || "Erro ao sincronizar roles");
  }
}

/**
 * Verifica e corrige inconsistências entre roles de colaboradores e usuários
 * @param clubId ID do clube
 * @returns Objeto com estatísticas de correções
 */
export async function checkAndFixRoleInconsistencies(clubId: number): Promise<{
  checked: number;
  fixed: number;
  errors: number;
}> {
  try {
    console.log(`Verificando inconsistências de roles para o clube ${clubId}`);

    // Buscar todos os colaboradores que têm user_id
    const { data: collaborators, error: collaboratorsError } = await supabase
      .from("collaborators")
      .select("id, user_id, role, full_name")
      .eq("club_id", clubId)
      .not("user_id", "is", null);

    if (collaboratorsError) {
      console.error("Erro ao buscar colaboradores:", collaboratorsError);
      throw new Error(`Erro ao buscar colaboradores: ${collaboratorsError.message}`);
    }

    if (!collaborators || collaborators.length === 0) {
      console.log("Nenhum colaborador com user_id encontrado");
      return { checked: 0, fixed: 0, errors: 0 };
    }

    // Extrair user_ids para buscar os usuários
    const userIds = collaborators.map(c => c.user_id).filter(Boolean) as string[];

    // Buscar usuários correspondentes
    const { data: users, error: usersError } = await supabase
      .from("users")
      .select("id, role, name")
      .in("id", userIds);

    if (usersError) {
      console.error("Erro ao buscar usuários:", usersError);
      throw new Error(`Erro ao buscar usuários: ${usersError.message}`);
    }

    if (!users || users.length === 0) {
      console.log("Nenhum usuário encontrado para os colaboradores");
      return { checked: 0, fixed: 0, errors: 0 };
    }

    // Criar um mapa de usuários para facilitar a busca
    const userMap = new Map();
    users.forEach(user => {
      userMap.set(user.id, user);
    });

    // Verificar e corrigir inconsistências
    let checked = 0;
    let fixed = 0;
    let errors = 0;

    for (const collaborator of collaborators) {
      if (!collaborator.user_id || !collaborator.role) continue;

      const user = userMap.get(collaborator.user_id);
      if (!user) {
        console.warn(`Usuário não encontrado para o colaborador ${collaborator.full_name} (ID: ${collaborator.id})`);
        continue;
      }

      checked++;

      // Verificar se há inconsistência
      if (user.role !== collaborator.role) {
        console.log(`Inconsistência encontrada: Colaborador ${collaborator.full_name} tem role "${collaborator.role}", mas usuário ${user.name} tem role "${user.role}"`);

        try {
          const { error: updateError } = await supabase
            .from("users")
            .update({ role: collaborator.role })
            .eq("id", collaborator.user_id);

          if (updateError) {
            console.error(`Erro ao corrigir role do usuário ${user.name}:`, updateError);
            errors++;
          } else {
            console.log(`Role do usuário ${user.name} corrigido de "${user.role}" para "${collaborator.role}"`);
            fixed++;
          }
        } catch (error) {
          console.error(`Erro ao atualizar usuário ${user.name}:`, error);
          errors++;
        }
      }
    }

    console.log(`Verificação concluída: ${checked} verificados, ${fixed} corrigidos, ${errors} erros`);
    return { checked, fixed, errors };
  } catch (error: any) {
    console.error("Erro ao verificar inconsistências:", error);
    throw new Error(error.message || "Erro ao verificar inconsistências");
  }
}
