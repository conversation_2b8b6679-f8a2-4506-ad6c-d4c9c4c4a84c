import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '@/integrations/supabase/client';
import { getSession } from '@/services/authService';
import { deleteSeason } from '@/api/seasonApi';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow DELETE method
  if (req.method !== 'DELETE') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Get the season ID from the URL
    const { id } = req.query;

    if (!id || Array.isArray(id)) {
      return res.status(400).json({ message: 'Invalid season ID' });
    }

    // Get the user's session to verify they're authenticated
    const session = await getSession(req);
    if (!session) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get the club ID from the session
    const { data: clubMember, error: clubMemberError } = await supabase
      .from('club_members')
      .select('club_id')
      .eq('user_id', session.user.id)
      .single();

    if (clubMemberError || !clubMember) {
      return res.status(403).json({ message: 'User does not have access to any club' });
    }

    const clubId = clubMember.club_id;

    // Check if the season exists and belongs to the user's club
    const { data: season, error: seasonError } = await supabase
      .from('seasons')
      .select('id')
      .eq('id', id)
      .eq('club_id', clubId)
      .single();

    if (seasonError || !season) {
      return res.status(404).json({ message: 'Season not found or does not belong to your club' });
    }

    // Use the deleteSeason function from the API
    try {
      await deleteSeason(clubId, parseInt(id));
    } catch (error: any) {
      // Handle specific error messages
      if (error.message.includes('partidas') || error.message.includes('competições')) {
        return res.status(400).json({ message: error.message });
      }

      // Handle other errors
      return res.status(500).json({ message: `Error deleting season: ${error.message}` });
    }

    // Return success
    return res.status(200).json({ message: 'Season deleted successfully' });
  } catch (error: any) {
    console.error('Error deleting season:', error);
    return res.status(500).json({ message: error.message || 'Internal server error' });
  }
}
