import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useMedicalRecordsStore } from "@/store/useMedicalRecordsStore";
import { Label } from "@/components/ui/label";
import { usePlayersStore } from "@/store/usePlayersStore";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useUser } from "@/context/UserContext";
import { useCurrentClubId } from "@/context/ClubContext";
import { getMedicalProfessionalByUserId, getMedicalProfessionals, MedicalProfessional, getCollaborators, Collaborator } from "@/api/api";
import { usePermission } from "@/hooks/usePermission";
import { CircleIcon } from "lucide-react";

interface NovoProntuarioDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  editRecord?: any;
}

export function NovoProntuarioDialog({ open, onOpenChange, clubId, editRecord }: NovoProntuarioDialogProps) {
  const [playerId, setPlayerId] = useState("");
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [description, setDescription] = useState("");
  const [symptoms, setSymptoms] = useState("");
  const [doctor, setDoctor] = useState("");
  const [doctorId, setDoctorId] = useState("");
  const [status, setStatus] = useState("Em tratamento");
  const [isCompleted, setIsCompleted] = useState(false);
  const [isReferral, setIsReferral] = useState(false);
  const [severity, setSeverity] = useState("normal"); // normal, warning, critical
  const [loadingMedicalProfessional, setLoadingMedicalProfessional] = useState(false);
  const [medicalProfessionals, setMedicalProfessionals] = useState<MedicalProfessional[]>([]);
  const [loadingMedicalProfessionals, setLoadingMedicalProfessionals] = useState(false);
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [loadingCollaborators, setLoadingCollaborators] = useState(false);
  const [patientType, setPatientType] = useState("player"); // player or collaborator
  const { addMedicalRecord, updateMedicalRecord, loading } = useMedicalRecordsStore();
  const { players, fetchPlayers, loading: loadingPlayers } = usePlayersStore();
  const { user } = useUser();
  const { role } = usePermission();

  useEffect(() => {
    if (open && clubId && players.length === 0 && !loadingPlayers) {
      fetchPlayers(clubId);
    }
  }, [open, clubId, fetchPlayers, players.length, loadingPlayers]);

  // Fetch collaborators
  useEffect(() => {
    const fetchCollaboratorsData = async () => {
      if (open && clubId) {
        try {
          setLoadingCollaborators(true);
          const data = await getCollaborators(clubId);
          setCollaborators(data);
        } catch (error) {
          console.error("Error fetching collaborators:", error);
        } finally {
          setLoadingCollaborators(false);
        }
      }
    };

    fetchCollaboratorsData();
  }, [open, clubId]);

  // Carregar profissionais médicos
  useEffect(() => {
    const fetchAllMedicalProfessionals = async () => {
      if (open && clubId) {
        try {
          setLoadingMedicalProfessionals(true);

          if (role === "medical" && user?.id) {
            // Se for médico, buscar apenas o próprio perfil
            const ownProfile = await getMedicalProfessionalByUserId(clubId, user.id);
            if (ownProfile) {
              setMedicalProfessionals([ownProfile]);
            } else {
              setMedicalProfessionals([]);
            }
          } else {
            // Para outros usuários, buscar todos os médicos
            const professionals = await getMedicalProfessionals(clubId);
            setMedicalProfessionals(professionals);
          }
        } catch (error) {
          console.error("Erro ao buscar profissionais médicos:", error);
        } finally {
          setLoadingMedicalProfessionals(false);
        }
      }
    };

    fetchAllMedicalProfessionals();
  }, [open, clubId, role, user?.id]);

  // Buscar o profissional médico associado ao usuário logado
  useEffect(() => {
    const fetchMedicalProfessional = async () => {
      if (open && user && user.id && !editRecord) {
        try {
          setLoadingMedicalProfessional(true);
          const medicalProfessional = await getMedicalProfessionalByUserId(clubId, user.id);

          if (medicalProfessional) {
            // Se o usuário logado for um profissional médico, preencher o campo automaticamente
            setDoctor(medicalProfessional.name);
            setDoctorId(medicalProfessional.id.toString());
          }
        } catch (error) {
          console.error("Erro ao buscar profissional médico:", error);
        } finally {
          setLoadingMedicalProfessional(false);
        }
      }
    };

    fetchMedicalProfessional();
  }, [open, user, clubId, editRecord]);

  // Efeito para carregar dados do registro para edição ou limpar o formulário quando aberto
  useEffect(() => {
    if (open && editRecord) {
      // Carregar dados do registro para edição
      setPlayerId(editRecord.player_id || "");
      setDate(editRecord.date || new Date().toISOString().split('T')[0]);
      setDescription(editRecord.description || "");
      setSymptoms(editRecord.symptoms || "");
      setDoctor(editRecord.doctor || "");
      setDoctorId(editRecord.doctor_id ? editRecord.doctor_id.toString() : "");
      setStatus(editRecord.status || "Em tratamento");
      setIsReferral(editRecord.is_referral || false);
      setSeverity(editRecord.severity || "normal");
      setPatientType("player"); // Assume player for existing records
      setIsCompleted(editRecord.completed || false);
    } else if (open && !doctorId) {
      // Limpar o formulário apenas quando aberto pela primeira vez
      // e não quando o médico é selecionado
      setPlayerId("");
      setDate(new Date().toISOString().split('T')[0]);
      setDescription("");
      setSymptoms("");
      // Não limpar o campo de médico aqui, pois ele será preenchido pelo efeito acima
      if (!doctor) {
        setDoctor("");
      }
      setStatus(role === "medical" ? "Em tratamento" : "Em tratamento");
      setIsReferral(role !== "medical");
      setSeverity("normal");
      setPatientType("player");
      setIsCompleted(false);
    }
    // Removemos doctor da lista de dependências para evitar que o formulário seja limpo
    // quando o médico é selecionado
  }, [open, editRecord, role]);

  const handleSave = async () => {
    if (!playerId || !description || !date) {
      return;
    }

    // Validar campos específicos para encaminhamento
    if (isReferral && !doctorId) {
      alert("Por favor, selecione um médico para o encaminhamento.");
      return;
    }

    // Dados comuns para criação ou atualização
    const recordData = {
      player_id: playerId,
      date,
      description,
      symptoms: symptoms || undefined,
      doctor,
      doctor_id: doctorId ? parseInt(doctorId) : undefined,
      // Status is now determined by the rehabilitation section, not in medical records
      status: isCompleted ? "Alta médica" : "Em tratamento", // Default status
      severity,
      club_id: clubId,
      is_referral: isReferral,
      referred_by: isReferral ? user?.name || user?.email : undefined,
      viewed: role === "medical" ? true : false,
      viewed_at: role === "medical" ? new Date().toISOString() : undefined,
      completed: isCompleted
    };

    try {
      let savedRecord;

      if (editRecord) {
        savedRecord = await updateMedicalRecord(clubId, editRecord.id, recordData);
      } else {
        savedRecord = await addMedicalRecord(clubId, recordData);
      }

      // Se for um encaminhamento, criar uma notificação para o médico
      if (isReferral && doctorId && !editRecord) {
        try {
          // Buscar o ID do usuário do médico
          const selectedDoctor = medicalProfessionals.find(p => p.id.toString() === doctorId);

          if (selectedDoctor && selectedDoctor.user_id) {
            // Buscar o nome do jogador
            const playerName = players.find(p => p.id === playerId)?.name || "Jogador";

            // Criar notificação
            await createMedicalNotification({
              club_id: clubId,
              user_id: selectedDoctor.user_id,
              title: "Novo encaminhamento médico",
              message: `Você recebeu um encaminhamento para ${playerName} com sintomas: ${symptoms || "Não especificados"}`,
              type: "medical_record",
              reference_id: savedRecord?.id?.toString(),
              reference_type: "medical_record"
            });
          }
        } catch (error) {
          console.error("Erro ao criar notificação:", error);
        }
      }

      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao salvar prontuário:", error);
      alert("Erro ao salvar prontuário. Por favor, tente novamente.");
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {editRecord
              ? "Editar Prontuário Médico"
              : role === "medical"
                ? "Novo Prontuário Médico"
                : "Encaminhar Paciente para Consulta Médica"}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <Label htmlFor="patient-type">Tipo de Paciente</Label>
            <Select value={patientType} onValueChange={setPatientType} disabled={!!editRecord}>
              <SelectTrigger id="patient-type">
                <SelectValue placeholder="Selecione o tipo de paciente" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="player">Jogador</SelectItem>
                <SelectItem value="collaborator">Colaborador</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {patientType === "player" ? (
            <div>
              <Label htmlFor="player">Jogador</Label>
              <Select value={playerId} onValueChange={setPlayerId} disabled={loadingPlayers || players.length === 0}>
                <SelectTrigger id="player">
                  <SelectValue placeholder={loadingPlayers ? "Carregando jogadores..." : "Selecione o jogador"} />
                </SelectTrigger>
                <SelectContent>
                  {players
                    .sort((a, b) => a.name.localeCompare(b.name)) // Sort alphabetically
                    .map((player) => (
                      <SelectItem key={player.id} value={player.id}>
                        {player.name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          ) : (
            <div>
              <Label htmlFor="collaborator">Colaborador</Label>
              <Select value={playerId} onValueChange={setPlayerId} disabled={loadingCollaborators || collaborators.length === 0}>
                <SelectTrigger id="collaborator">
                  <SelectValue placeholder={loadingCollaborators ? "Carregando colaboradores..." : "Selecione o colaborador"} />
                </SelectTrigger>
                <SelectContent>
                  {collaborators
                    .sort((a, b) => a.full_name.localeCompare(b.full_name)) // Sort alphabetically
                    .map((collaborator) => (
                      <SelectItem key={collaborator.id} value={collaborator.id.toString()}>
                        {collaborator.full_name} - {collaborator.role}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          )}
          <div>
            <Label htmlFor="date">Data</Label>
            <Input
              id="date"
              type="date"
              value={date}
              onChange={e => setDate(e.target.value)}
              disabled={isCompleted}
              className={isCompleted ? "bg-gray-100" : ""}
            />
          </div>

          {role !== "medical" && !editRecord && (
            <div>
              <Label htmlFor="symptoms">Sintomas ou Queixas</Label>
              <Textarea
                id="symptoms"
                value={symptoms}
                onChange={e => setSymptoms(e.target.value)}
                placeholder="Descreva os sintomas ou queixas do jogador"
              />
            </div>
          )}

          <div>
            <Label htmlFor="description">
              {role === "medical" ? "Descrição" : "Observações Adicionais"}
            </Label>
            <Textarea
              id="description"
              value={description}
              onChange={e => setDescription(e.target.value)}
              placeholder={role === "medical" ? "Descrição do atendimento" : "Observações adicionais sobre o jogador"}
              disabled={isCompleted}
              className={isCompleted ? "bg-gray-100" : ""}
            />
            {isCompleted && (
              <p className="text-xs text-amber-600 mt-1">Este prontuário foi concluído e não pode ser editado.</p>
            )}
          </div>
          <div>
            <Label htmlFor="doctor">Médico</Label>
            {role === "medical" ? (
              // Para médicos: campo de texto somente leitura com o nome do médico logado
              <>
                <Input
                  id="doctor"
                  value={doctor}
                  placeholder={loadingMedicalProfessional ? "Carregando..." : "Nome do médico"}
                  readOnly={true}
                  className="bg-gray-50"
                />
                <p className="text-xs text-muted-foreground mt-1">Preenchido automaticamente com seu nome</p>
              </>
            ) : (
              // Para outros usuários: select com todos os médicos disponíveis
              <>
                <Select
                  value={doctorId}
                  onValueChange={(value) => {
                    // Apenas atualizar o ID e nome do médico, sem afetar outros campos
                    setDoctorId(value);
                    const selectedDoctor = medicalProfessionals.find(p => p.id.toString() === value);
                    if (selectedDoctor) {
                      setDoctor(selectedDoctor.name);
                    }
                  }}
                  disabled={loadingMedicalProfessionals || medicalProfessionals.length === 0}
                >
                  <SelectTrigger id="doctor-select">
                    <SelectValue placeholder={loadingMedicalProfessionals ? "Carregando médicos..." : "Selecione o médico"} />
                  </SelectTrigger>
                  <SelectContent>
                    {medicalProfessionals.map((professional) => (
                      <SelectItem key={professional.id} value={professional.id.toString()}>
                        {professional.name} ({professional.role})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {medicalProfessionals.length === 0 && !loadingMedicalProfessionals && (
                  <p className="text-xs text-amber-600 mt-1">Nenhum profissional médico cadastrado</p>
                )}
              </>
            )}
          </div>
          {/* Status field removed as requested */}

          {role === "medical" && (
            <div>
              <Label htmlFor="severity">Situação do Paciente</Label>
              <div className={`flex items-center gap-4 mt-2 ${isCompleted ? "opacity-70" : ""}`}>
                <div
                  className={`flex flex-col items-center ${isCompleted ? "" : "cursor-pointer"} ${severity === "normal" ? "opacity-100" : "opacity-50"}`}
                  onClick={() => !isCompleted && setSeverity("normal")}
                >
                  <CircleIcon className="h-6 w-6 text-green-500 mb-1" />
                  <span className={`text-xs ${severity === "normal" ? "text-green-500 font-medium" : ""}`}>Liberado</span>
                </div>
                <div
                  className={`flex flex-col items-center ${isCompleted ? "" : "cursor-pointer"} ${severity === "warning" ? "opacity-100" : "opacity-50"}`}
                  onClick={() => !isCompleted && setSeverity("warning")}
                >
                  <CircleIcon className="h-6 w-6 text-yellow-500 mb-1" />
                  <span className={`text-xs ${severity === "warning" ? "text-yellow-500 font-medium" : ""}`}>Em transição</span>
                </div>
                <div
                  className={`flex flex-col items-center ${isCompleted ? "" : "cursor-pointer"} ${severity === "critical" ? "opacity-100" : "opacity-50"}`}
                  onClick={() => !isCompleted && setSeverity("critical")}
                >
                  <CircleIcon className="h-6 w-6 text-red-500 mb-1" />
                  <span className={`text-xs ${severity === "critical" ? "text-red-500 font-medium" : ""}`}>Mantém tratamento</span>
                </div>
              </div>
            </div>
          )}
        </div>
        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          {editRecord && role === "medical" && !isCompleted && (
            <Button
              variant="destructive"
              className="w-full sm:w-auto"
              onClick={() => {
                setIsCompleted(true);
                handleSave();
              }}
              disabled={loading}
            >
              Concluir Tratamento
            </Button>
          )}
          <div className="flex gap-2 w-full sm:w-auto">
            <Button variant="outline" onClick={() => onOpenChange(false)} className="flex-1 sm:flex-none">
              Cancelar
            </Button>
            <Button
              onClick={handleSave}
              disabled={!playerId || !description || !date || loading || (isReferral && !doctorId) || isCompleted}
              className="flex-1 sm:flex-none"
            >
              {editRecord
                ? "Salvar Alterações"
                : role === "medical"
                  ? "Salvar Prontuário"
                  : "Encaminhar para Consulta"}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
