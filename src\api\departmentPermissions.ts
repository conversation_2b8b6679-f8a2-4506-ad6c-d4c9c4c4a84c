import { supabase } from "@/integrations/supabase/client";
import { withPermission, withAuditLog } from "./middleware";
import { DEPARTMENT_PERMISSIONS } from "@/constants/permissions";

/**
 * Obtém as permissões de um departamento
 * @param clubId ID do clube
 * @param departmentId ID do departamento
 * @param userId ID do usuário que está fazendo a requisição
 * @returns Permissões do departamento
 */
export async function getDepartmentPermissions(
  clubId: number,
  departmentId: number,
  userId: string
): Promise<Record<string, boolean>> {
  return withPermission(
    clubId,
    userId,
    DEPARTMENT_PERMISSIONS.VIEW,
    async () => {
      const { data, error } = await supabase
        .from("departments")
        .select("permissions")
        .eq("club_id", clubId)
        .eq("id", departmentId)
        .single();

      if (error) {
        throw new Error(`Erro ao obter permissões do departamento: ${error.message}`);
      }

      return data?.permissions || {};
    }
  );
}

/**
 * Atualiza as permissões de um departamento
 * @param clubId ID do clube
 * @param departmentId ID do departamento
 * @param permissions Novas permissões
 * @param userId ID do usuário que está fazendo a requisição
 * @returns true se as permissões foram atualizadas com sucesso
 */
export async function updateDepartmentPermissions(
  clubId: number,
  departmentId: number,
  permissions: Record<string, boolean>,
  userId: string
): Promise<boolean> {
  return withPermission(
    clubId,
    userId,
    DEPARTMENT_PERMISSIONS.EDIT,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "department.update_permissions",
        { department_id: departmentId },
        async () => {
          const { error } = await supabase
            .from("departments")
            .update({ permissions })
            .eq("club_id", clubId)
            .eq("id", departmentId);

          if (error) {
            throw new Error(`Erro ao atualizar permissões do departamento: ${error.message}`);
          }

          return true;
        }
      );
    }
  );
}

/**
 * Obtém as permissões de um usuário em um departamento específico
 * @param clubId ID do clube
 * @param userId ID do usuário
 * @param departmentId ID do departamento
 * @param requestUserId ID do usuário que está fazendo a requisição
 * @returns Permissões do usuário no departamento
 */
export async function getUserDepartmentPermissions(
  clubId: number,
  userId: string,
  departmentId: number,
  requestUserId: string
): Promise<Record<string, boolean>> {
  return withPermission(
    clubId,
    requestUserId,
    DEPARTMENT_PERMISSIONS.VIEW,
    async () => {
      const { data, error } = await supabase
        .from("user_departments")
        .select("permissions")
        .eq("club_id", clubId)
        .eq("user_id", userId)
        .eq("department_id", departmentId)
        .single();

      if (error) {
        throw new Error(`Erro ao obter permissões do usuário no departamento: ${error.message}`);
      }

      return data?.permissions || {};
    }
  );
}

/**
 * Atualiza as permissões de um usuário em um departamento específico
 * @param clubId ID do clube
 * @param userId ID do usuário
 * @param departmentId ID do departamento
 * @param permissions Novas permissões
 * @param requestUserId ID do usuário que está fazendo a requisição
 * @returns true se as permissões foram atualizadas com sucesso
 */
export async function updateUserDepartmentPermissions(
  clubId: number,
  userId: string,
  departmentId: number,
  permissions: Record<string, boolean>,
  requestUserId: string
): Promise<boolean> {
  return withPermission(
    clubId,
    requestUserId,
    DEPARTMENT_PERMISSIONS.EDIT,
    async () => {
      return withAuditLog(
        clubId,
        requestUserId,
        "department.update_user_permissions",
        { department_id: departmentId, user_id: userId },
        async () => {
          const { error } = await supabase
            .from("user_departments")
            .update({ permissions })
            .eq("club_id", clubId)
            .eq("user_id", userId)
            .eq("department_id", departmentId);

          if (error) {
            throw new Error(`Erro ao atualizar permissões do usuário no departamento: ${error.message}`);
          }

          return true;
        }
      );
    }
  );
}
