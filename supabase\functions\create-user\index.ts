// supabase/functions/create-user/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Verificar autorização
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Não autorizado' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Criar cliente Supabase com chave de serviço
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      { auth: { autoRefreshToken: false, persistSession: false } }
    )

    // Obter dados do corpo da requisição
    const { email, password, name, role, clubName, clubId } = await req.json()

    // Verificar campos obrigatórios
    if (!email || !name || !role) {
      return new Response(
        JSON.stringify({ error: 'Campos obrigatórios faltando' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Verificar se o email já está em uso
    const { data: existingUsers, error: checkError } = await supabaseAdmin
      .from("users")
      .select("id")
      .eq("email", email);

    if (checkError) {
      return new Response(
        JSON.stringify({ error: `Erro ao verificar email: ${checkError.message}` }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    if (existingUsers && existingUsers.length > 0) {
      return new Response(
        JSON.stringify({
          success: true,
          userId: existingUsers[0].id,
          message: "Usuário já existe"
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Criar usuário no Supabase Auth
    console.log(`Criando usuário para ${email} com email_confirm=true`);
    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password: password,
      email_confirm: true, // Confirmar email automaticamente
      user_metadata: { name, role }
    })

    if (authError) {
      console.error(`Erro ao criar usuário via API admin: ${authError.message}`, authError);

      // Verificar se o erro está relacionado ao envio de email
      if (authError.message.includes('confirmation email') || authError.message.includes('email rate limit')) {
        console.error('Erro relacionado ao envio de email detectado. Tentando método alternativo...');

        // Tentar atualizar diretamente o campo email_confirmed_at no banco de dados
        try {
          // Verificar se o usuário foi criado apesar do erro
          if (authData?.user?.id) {
            console.log(`Usuário criado, mas com erro de email. Tentando confirmar email manualmente para: ${authData.user.id}`);

            // Executar SQL diretamente para confirmar o email
            const { error: confirmError } = await supabaseAdmin.rpc('confirm_user_email', {
              user_id: authData.user.id
            });

            if (!confirmError) {
              console.log(`Email confirmado manualmente com sucesso para: ${authData.user.id}`);
              // Continuar com o fluxo normal
              return new Response(
                JSON.stringify({
                  success: true,
                  userId: authData.user.id,
                  message: "Usuário criado com sucesso (email confirmado manualmente)"
                }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
              )
            } else {
              console.error(`Erro ao confirmar email manualmente: ${confirmError.message}`);
            }
          }
        } catch (confirmError) {
          console.error('Erro ao tentar confirmar email manualmente:', confirmError);
        }
      }

      return new Response(
        JSON.stringify({ error: `Erro ao criar usuário: ${authError.message}` }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const userId = authData.user.id

    // Criar registro na tabela users
    const { error: userCreateError } = await supabaseAdmin
      .from("users")
      .insert({
        id: userId,
        email,
        name,
        role,
        first_login: true
      })

    if (userCreateError) {
      return new Response(
        JSON.stringify({ error: `Erro ao criar registro de usuário: ${userCreateError.message}` }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Se o clubId foi fornecido, adicionar o usuário ao clube
    if (clubId) {
      try {
        // Verificar se o usuário já é membro do clube
        const { data: existingMember, error: memberCheckError } = await supabaseAdmin
          .from("club_members")
          .select("id")
          .eq("club_id", clubId)
          .eq("user_id", userId)
          .maybeSingle();

        if (memberCheckError) {
          console.error("Erro ao verificar associação do usuário ao clube:", memberCheckError);
        }

        // Se o usuário ainda não é membro do clube, adicioná-lo
        if (!existingMember) {
          const { error: memberError } = await supabaseAdmin
            .from("club_members")
            .insert({
              club_id: clubId,
              user_id: userId,
              role: role,
              status: "ativo"
            });

          if (memberError) {
            console.error("Erro ao adicionar usuário ao clube:", memberError);
            // Não lançamos erro aqui para não interromper o fluxo principal
          }
        }
      } catch (error) {
        console.error("Erro ao adicionar usuário ao clube:", error);
        // Não lançamos erro aqui para não interromper o fluxo principal
      }
    }

    // Retornar sucesso
    return new Response(
      JSON.stringify({
        success: true,
        userId,
        message: "Usuário criado com sucesso"
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
