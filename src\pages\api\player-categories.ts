import { NextApiRequest, NextApiResponse } from 'next';
import { assignPlayerToCategory } from '@/api/api';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { clubId, playerId, categoryId } = req.body;

    if (!clubId || !playerId || !categoryId) {
      return res.status(400).json({ message: 'Missing required fields' });
    }

    const result = await assignPlayerToCategory(clubId, playerId, categoryId);
    return res.status(200).json(result);
  } catch (error: any) {
    console.error('Error associating player to category:', error);
    return res.status(500).json({ message: error.message || 'Internal server error' });
  }
}
