import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { format, parseISO } from "date-fns";
import { ptBR } from "date-fns/locale";
import { CalendarIcon, FileText, Pencil, Trash2 } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { useTreatmentEvolutionsStore } from "@/store/useTreatmentEvolutionsStore";
import { TreatmentEvolutionForm } from "./TreatmentEvolutionForm";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { usePermission } from "@/hooks/usePermission";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";

interface TreatmentEvolutionListProps {
  recordId: number;
  title?: string;
  description?: string;
  showAddButton?: boolean;
}

export function TreatmentEvolutionList({
  recordId,
  title = "Evolução do Tratamento",
  description = "Acompanhe a evolução do tratamento",
  showAddButton = true,
}: TreatmentEvolutionListProps) {
  const { toast } = useToast();
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { role, can } = usePermission();
  const {
    evolutions,
    loading,
    error,
    fetchEvolutions,
    deleteEvolution,
  } = useTreatmentEvolutionsStore();

  // State for delete confirmation dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [evolutionToDelete, setEvolutionToDelete] = useState<number | null>(null);

  // State for evolution details dialog
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [selectedEvolution, setSelectedEvolution] = useState<any | null>(null);

  // Load evolutions when component mounts
  useEffect(() => {
    if (clubId && recordId) {
      loadEvolutions();
    }
  }, [clubId, recordId]);

  // Load evolutions
  const loadEvolutions = async () => {
    try {
      await fetchEvolutions(clubId, recordId);
    } catch (error) {
      console.error("Erro ao carregar evoluções:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar as evoluções do tratamento",
        variant: "destructive",
      });
    }
  };

  // Handle evolution deletion
  const handleDeleteEvolution = async () => {
    if (!evolutionToDelete) return;

    try {
      await deleteEvolution(clubId, user?.id || "", evolutionToDelete);
      toast({
        title: "Evolução excluída",
        description: "A evolução do tratamento foi excluída com sucesso",
      });
      setDeleteDialogOpen(false);
      setEvolutionToDelete(null);
    } catch (error) {
      console.error("Erro ao excluir evolução:", error);
      toast({
        title: "Erro",
        description: "Não foi possível excluir a evolução do tratamento",
        variant: "destructive",
      });
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Alta médica":
        return <Badge className="bg-green-500">Alta médica</Badge>;
      case "Liberado": // For backward compatibility
        return <Badge className="bg-green-500">Alta médica</Badge>;
      case "Em tratamento":
        return <Badge className="bg-amber-500">Em tratamento</Badge>;
      case "Treina e joga":
        return <Badge className="bg-primary">Treina e joga</Badge>;
      case "Treina e trata": // For backward compatibility
        return <Badge className="bg-primary">Treina e joga</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>{title}</CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
          {showAddButton && role === "medical" && (
            <TreatmentEvolutionForm recordId={recordId} onEvolutionAdded={loadEvolutions} />
          )}
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <p>Carregando evoluções...</p>
            </div>
          ) : error ? (
            <div className="flex justify-center py-8">
              <p className="text-red-500">{error}</p>
            </div>
          ) : evolutions.length === 0 ? (
            <div className="flex justify-center py-8">
              <p className="text-muted-foreground">Nenhuma evolução registrada</p>
            </div>
          ) : (
            <div className="space-y-4">
              {evolutions.map((evolution) => (
                <Card key={evolution.id} className="overflow-hidden">
                  <CardHeader className="bg-muted p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">
                          {format(parseISO(evolution.date), "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
                        </span>
                        {getStatusBadge(evolution.status)}
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedEvolution(evolution);
                            setDetailsDialogOpen(true);
                          }}
                        >
                          <FileText className="h-4 w-4" />
                        </Button>
                        {role === "medical" && can("medical.treatment.delete") && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-500 hover:text-red-700"
                            onClick={() => {
                              setEvolutionToDelete(evolution.id);
                              setDeleteDialogOpen(true);
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="space-y-2">
                      <p className="font-medium">Descrição:</p>
                      <p className="text-sm text-muted-foreground">{evolution.description}</p>

                      {evolution.procedures && evolution.procedures.length > 0 && (
                        <>
                          <p className="font-medium">Procedimentos:</p>
                          <ul className="list-disc list-inside text-sm text-muted-foreground">
                            {evolution.procedures.map((procedure: string, index: number) => (
                              <li key={index}>{procedure}</li>
                            ))}
                          </ul>
                        </>
                      )}

                      {evolution.response && (
                        <>
                          <p className="font-medium">Resposta ao tratamento:</p>
                          <p className="text-sm text-muted-foreground">{evolution.response}</p>
                        </>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Excluir Evolução</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>Tem certeza que deseja excluir esta evolução do tratamento? Esta ação não pode ser desfeita.</p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Cancelar
            </Button>
            <Button variant="destructive" onClick={handleDeleteEvolution}>
              Excluir
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Evolution Details Dialog */}
      <Dialog open={detailsDialogOpen} onOpenChange={setDetailsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Detalhes da Evolução</DialogTitle>
          </DialogHeader>
          {selectedEvolution && (
            <ScrollArea className="max-h-[60vh]">
              <div className="space-y-4 p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">
                      {format(parseISO(selectedEvolution.date), "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
                    </span>
                  </div>
                  {getStatusBadge(selectedEvolution.status)}
                </div>

                <Separator />

                <div>
                  <h3 className="font-semibold text-lg">Descrição</h3>
                  <p className="mt-1">{selectedEvolution.description}</p>
                </div>

                {selectedEvolution.procedures && selectedEvolution.procedures.length > 0 && (
                  <div>
                    <h3 className="font-semibold text-lg">Procedimentos Realizados</h3>
                    <ul className="mt-1 list-disc list-inside">
                      {selectedEvolution.procedures.map((procedure: string, index: number) => (
                        <li key={index}>{procedure}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {selectedEvolution.response && (
                  <div>
                    <h3 className="font-semibold text-lg">Resposta ao Tratamento</h3>
                    <p className="mt-1">{selectedEvolution.response}</p>
                  </div>
                )}

                {selectedEvolution.signature_url && (
                  <div>
                    <h3 className="font-semibold text-lg">Assinatura Digital</h3>
                    <div className="mt-2">
                      <img
                        src={selectedEvolution.signature_url}
                        alt="Assinatura Digital"
                        className="max-h-24 border rounded-md"
                      />
                    </div>
                  </div>
                )}
              </div>
            </ScrollArea>
          )}
          <DialogFooter>
            <Button onClick={() => setDetailsDialogOpen(false)}>
              Fechar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
