import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import { useTrainingsStore } from "@/store/useTrainingsStore";
import type { Training } from "@/api/api";
import { toast } from "@/hooks/use-toast";

interface TrainingDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  training?: Training;
}

export function TrainingDialog({ open, onOpenChange, training, clubId }: TrainingDialogProps & { clubId: number }) {
  const updateTraining = useTrainingsStore(state => state.updateTraining);
  const deleteTraining = useTrainingsStore(state => state.deleteTraining);
  const [title, setTitle] = useState(training?.name || "");
  const [date, setDate] = useState(training?.date || "");
  const [type, setType] = useState(training?.type || "");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleSave = async () => {
    if (!title.trim()) {
      setError("O título do treino é obrigatório.");
      return;
    }
    if (!date) {
      setError("A data é obrigatória.");
      return;
    }
    setError("");
    setIsLoading(true);
    try {
      await updateTraining(clubId, training.id, {
        club_id: clubId,
        name: title,
        type,
        date,
      });
      toast({ title: "Treino atualizado com sucesso!", variant: "default" });
      onOpenChange(false);
    } catch (e) {
      setError("Erro ao atualizar treino.");
      toast({ title: "Erro ao atualizar treino.", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!training) return;
    setIsLoading(true);
    try {
      await deleteTraining(clubId, training.id);
      toast({ title: "Treino excluído com sucesso!", variant: "default" });
      onOpenChange(false);
    } catch (e) {
      setError("Erro ao excluir treino.");
      toast({ title: "Erro ao excluir treino.", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Adicionar/Editar Treino</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <Input placeholder="Título*" value={title} onChange={e => setTitle(e.target.value)} />
          <Input type="date" value={date} onChange={e => setDate(e.target.value)} />
          <Input placeholder="Tipo" value={type} onChange={e => setType(e.target.value)} />
          {error && <div className="text-red-500 text-sm mt-1">{error}</div>}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancelar</Button>
          {training && (
            <Button variant="destructive" onClick={handleDelete} disabled={isLoading}>
              Excluir
            </Button>
          )}
          <Button onClick={handleSave} disabled={!title.trim() || !date || isLoading}>
            {isLoading ? <span className="loader mr-2" /> : null}
            Salvar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
