import { useState, useEffect } from "react";
import { getDashboardStats, DashboardStats } from "@/api/dashboardStats";

interface UseDashboardStatsReturn {
  stats: DashboardStats | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * Hook to manage dashboard statistics
 * @param clubId Club ID
 * @param selectedCategoryId Optional category ID to filter data
 * @returns Dashboard statistics state and actions
 */
export function useDashboardStats(
  clubId: number | null,
  selectedCategoryId?: number
): UseDashboardStatsReturn {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = async () => {
    if (!clubId) {
      setStats(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const dashboardStats = await getDashboardStats(clubId);
      
      // If a category is selected, filter the data
      if (selectedCategoryId && selectedCategoryId > 0) {
        const categoryData = dashboardStats.playersByCategory.find(
          (cat) => cat.categoryId === selectedCategoryId
        );
        
        // Update stats to show only the selected category
        setStats({
          ...dashboardStats,
          totalActivePlayers: categoryData?.playerCount || 0,
          playersByCategory: categoryData ? [categoryData] : [],
        });
      } else {
        setStats(dashboardStats);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erro ao buscar estatísticas";
      setError(errorMessage);
      console.error("Error fetching dashboard stats:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, [clubId, selectedCategoryId]);

  return {
    stats,
    loading,
    error,
    refetch: fetchStats,
  };
}
