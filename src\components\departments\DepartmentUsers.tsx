import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "@/components/ui/use-toast";
import { Plus, Trash2 } from "lucide-react";
import { useCurrentClubId } from "@/context/ClubContext";
import {
  getDepartmentUsers,
  removeUserFromDepartment,
  Department,
  UserDepartment,
} from "@/api/api";

interface DepartmentUsersProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  department?: Department;
  onAddUser?: () => void;
}

export function DepartmentUsers({
  open,
  onOpenChange,
  department,
  onAddUser,
}: DepartmentUsersProps) {
  const clubId = useCurrentClubId();
  const [users, setUsers] = useState<UserDepartment[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");

  // Carregar usuários do departamento
  useEffect(() => {
    const fetchUsers = async () => {
      if (!department) return;

      try {
        setLoading(true);
        const data = await getDepartmentUsers(clubId, department.id);
        setUsers(data);
      } catch (err: any) {
        console.error("Erro ao carregar usuários do departamento:", err);
        toast({
          title: "Erro",
          description: "Não foi possível carregar os usuários do departamento",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    if (open && department) {
      fetchUsers();
    }
  }, [clubId, department, open]);

  // Filtrar usuários com base na pesquisa
  const filteredUsers = users.filter((user) =>
    user.user_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.role.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Função para remover um usuário do departamento
  const handleRemoveUser = async (userDepartment: UserDepartment) => {
    if (!confirm(`Tem certeza que deseja remover ${userDepartment.user_name} do departamento?`)) {
      return;
    }

    try {
      await removeUserFromDepartment(userDepartment.id);
      
      // Atualizar lista de usuários
      setUsers(users.filter((u) => u.id !== userDepartment.id));
      
      toast({
        title: "Sucesso",
        description: "Usuário removido do departamento com sucesso",
      });
    } catch (err: any) {
      console.error("Erro ao remover usuário do departamento:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao remover usuário do departamento",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>
            Usuários do Departamento: {department?.name}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="flex justify-between items-center">
            <Input
              placeholder="Buscar usuário..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="max-w-sm"
            />
            <Button onClick={onAddUser}>
              <Plus className="h-4 w-4 mr-1" />
              Adicionar Usuário
            </Button>
          </div>

          {loading ? (
            <div className="flex justify-center items-center h-40">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : filteredUsers.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              {searchQuery
                ? "Nenhum usuário encontrado para a pesquisa"
                : "Nenhum usuário neste departamento"}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Função</TableHead>
                  <TableHead className="w-[100px] text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell className="font-medium">{user.user_name}</TableCell>
                    <TableCell>{user.role}</TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveUser(user)}
                        className="text-red-500 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
