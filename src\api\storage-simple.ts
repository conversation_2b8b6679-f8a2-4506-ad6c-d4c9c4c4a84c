import { supabase } from "@/integrations/supabase/client";
import { v4 as uuidv4 } from "uuid";

/**
 * Faz upload de uma imagem de convocação para o Supabase Storage
 * @param clubId ID do clube
 * @param file Arquivo de imagem
 * @param imageType Tipo da imagem (ex: 'logo', 'uniforme', 'onibus')
 * @returns URL pública da imagem
 */
export async function uploadCallupImage(
  clubId: string | number,
  file: File,
  imageType: string
): Promise<string> {
  try {
    console.log(`Uploading ${imageType} image:`, file.name, file.type, file.size);

    // Limitar tamanho do arquivo (5MB)
    const MAX_SIZE = 5 * 1024 * 1024; // 5MB
    if (file.size > MAX_SIZE) {
      throw new Error("O arquivo deve ter no máximo 5MB");
    }

    // Gerar nome único para o arquivo
    const fileExt = file.name.split('.').pop() || 'jpg';
    const fileName = `${imageType}-${clubId}-${uuidv4()}.${fileExt}`;
    const filePath = `callups/club-${clubId}/${fileName}`;

    // Fazer upload do arquivo
    const { error: uploadError } = await supabase.storage
      .from('profileimages')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true,
      });

    if (uploadError) {
      throw new Error(`Erro ao fazer upload da imagem: ${uploadError.message}`);
    }

    // Obter URL pública do arquivo
    const { data: urlData } = supabase.storage
      .from('profileimages')
      .getPublicUrl(filePath);

    // Adicionar timestamp para evitar cache
    const timestamp = new Date().getTime();
    const publicUrl = `${urlData.publicUrl}?t=${timestamp}`;

    console.log('Imagem enviada com sucesso:', publicUrl);
    return publicUrl;
  } catch (error) {
    console.error('Erro no upload da imagem de convocação:', error);
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido ao fazer upload da imagem';
    throw new Error(errorMessage);
  }
}


/**
 * Faz upload de uma imagem de logo do clube para o Supabase Storage
 * @param clubId ID do clube
 * @param file Arquivo de imagem
 * @returns URL da imagem
 */
export async function uploadClubLogo(clubId: string, file: File): Promise<string> {
  try {
    console.log("Uploading file:", file.name, file.type, file.size);

    // Limitar tamanho do arquivo (5MB)
    const MAX_SIZE = 5 * 1024 * 1024; // 5MB
    if (file.size > MAX_SIZE) {
      throw new Error("O arquivo deve ter no máximo 5MB");
    }

    // Gerar nome único para o arquivo
    const fileExt = file.name.split(".").pop();
    const fileName = `file-${clubId}-${uuidv4()}.${fileExt}`;
    const filePath = `club-${clubId}/${fileName}`;

    // Fazer upload do arquivo
    const { error } = await supabase.storage
      .from("profileimages") // Usando o bucket existente
      .upload(filePath, file, {
        cacheControl: "3600",
        upsert: true,
      });

    if (error) {
      throw new Error(`Erro ao fazer upload do logo: ${error.message}`);
    }

    // Obter URL pública do arquivo
    const { data: urlData } = supabase.storage
      .from("profileimages") // Usando o bucket existente
      .getPublicUrl(filePath);

    // Adicionar timestamp para evitar cache
    const timestamp = new Date().getTime();
    const publicUrl = `${urlData.publicUrl}?t=${timestamp}`;

    return publicUrl;
  } catch (error) {
    console.error("Erro ao fazer upload do logo do clube:", error);
    const errorMessage = error instanceof Error ? error.message : "Erro ao fazer upload do logo do clube";
    throw new Error(errorMessage);
  }
}
