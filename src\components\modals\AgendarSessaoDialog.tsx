import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState, useEffect } from "react";
import { useRehabSessionsStore } from "@/store/useRehabSessionsStore";
import { usePlayersStore } from "@/store/usePlayersStore";
import { toast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useUser } from "@/context/UserContext";
import { usePermission } from "@/hooks/usePermission";
import { getMedicalProfessionalByUserId, getCollaborators, Collaborator } from "@/api/api";

interface AgendarSessaoDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  session?: any; // Para edição
}

export function AgendarSessaoDialog({ open, onOpenChange, clubId, session }: AgendarSessaoDialogProps) {
  const isEdit = !!session;
  const [playerId, setPlayerId] = useState(session?.player_id || "");
  const [activity, setActivity] = useState(session?.activity || "");
  const [date, setDate] = useState(session?.date || "");
  const [time, setTime] = useState(session?.time || "");
  const [duration, setDuration] = useState(session?.duration ? String(session.duration) : "");
  const [professionalId, setProfessionalId] = useState(session?.professional_id || "");
  const [location, setLocation] = useState(session?.location || "");
  const [notes, setNotes] = useState(session?.notes || "");
  const [treatmentDescription, setTreatmentDescription] = useState<string>("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [medicalProfessionals, setMedicalProfessionals] = useState<any[]>([]);
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [loadingCollaborators, setLoadingCollaborators] = useState(false);
  const [patientType, setPatientType] = useState("player"); // player or collaborator
  const { addRehabSession, updateRehabSession } = useRehabSessionsStore();
  const { players, fetchPlayers } = usePlayersStore();
  const { user } = useUser();
  const { role } = usePermission();

  // Função para buscar profissionais médicos
  const fetchMedicalProfessionals = async () => {
    try {
      if (role === "medical" && user?.id) {
        // Se for médico, buscar apenas o próprio perfil
        const ownProfile = await getMedicalProfessionalByUserId(clubId, user.id);
        if (ownProfile) {
          setMedicalProfessionals([ownProfile]);
          // Pré-selecionar o próprio médico
          setProfessionalId(ownProfile.id.toString());
        } else {
          setMedicalProfessionals([]);
        }
      } else {
        // Para outros usuários, buscar todos os médicos
        const { data, error } = await supabase
          .from('medical_professionals')
          .select('*')
          .eq('club_id', clubId)
          .order('name');

        if (error) throw error;
        setMedicalProfessionals(data || []);
      }
    } catch (error) {
      console.error('Erro ao buscar profissionais médicos:', error);
    }
  };

  // Função para buscar colaboradores
  const fetchCollaboratorsData = async () => {
    if (open && clubId) {
      try {
        setLoadingCollaborators(true);
        const data = await getCollaborators(clubId);
        setCollaborators(data);
      } catch (error) {
        console.error("Error fetching collaborators:", error);
      } finally {
        setLoadingCollaborators(false);
      }
    }
  };

  useEffect(() => {
    if (open && clubId) {
      if (players.length === 0) {
        fetchPlayers(clubId);
      }
      fetchMedicalProfessionals();
      fetchCollaboratorsData();
    }

    if (isEdit && session) {
      setPlayerId(session.player_id || "");
      setActivity(session.activity || "");
      setDate(session.date || "");
      setTime(session.time || "");
      setDuration(session.duration ? String(session.duration) : "");
      setProfessionalId(session.professional_id || "");
      setLocation(session.location || "");
      setNotes(session.notes || "");
      // Reset treatment description input field
      setTreatmentDescription("");
      setPatientType("player"); // Assume player for existing sessions
    }
  }, [open, clubId, isEdit, session]);

  // Update location when professional changes
  useEffect(() => {
    if (professionalId) {
      const selectedProfessional = medicalProfessionals.find(p => p.id === Number(professionalId));
      if (selectedProfessional && selectedProfessional.address) {
        setLocation(selectedProfessional.address);
      }
    }
  }, [professionalId, medicalProfessionals]);

  const handleSave = async () => {
    if (!playerId) {
      setError("Selecione o atleta.");
      return;
    }
    if (!activity.trim()) {
      setError("A atividade é obrigatória.");
      return;
    }
    if (!date) {
      setError("A data é obrigatória.");
      return;
    }
    if (!time) {
      setError("O horário é obrigatório.");
      return;
    }
    if (!duration) {
      setError("A duração é obrigatória.");
      return;
    }
    if (!professionalId) {
      setError("Selecione o profissional médico.");
      return;
    }
    setError("");
    setIsLoading(true);
    try {
      // Prepare treatment description array
      let updatedTreatmentDescriptions: string[] = [];

      if (isEdit && session?.id) {
        // If editing, get existing treatment descriptions and add new one if provided
        updatedTreatmentDescriptions = session.treatment_description || [];
        if (treatmentDescription.trim()) {
          const today = new Date().toISOString().split('T')[0];
          updatedTreatmentDescriptions.push(`${today}: ${treatmentDescription.trim()}`);
        }

        // Obter informações do profissional selecionado
        const selectedProfessional = medicalProfessionals.find(p => p.id === Number(professionalId));

        await updateRehabSession(clubId, session.id, {
          player_id: playerId,
          activity,
          date,
          time,
          duration: Number(duration),
          professional_id: professionalId,
          professional: selectedProfessional?.name || "",
          location: location,
          status: session.status,
          notes,
          treatment_description: updatedTreatmentDescriptions
        });
        toast({ title: "Sessão atualizada com sucesso!", variant: "default" });
      } else {
        // If creating new, initialize with first treatment description if provided
        if (treatmentDescription.trim()) {
          const today = new Date().toISOString().split('T')[0];
          updatedTreatmentDescriptions = [`${today}: ${treatmentDescription.trim()}`];
        }

        // Obter informações do profissional selecionado
        const selectedProfessional = medicalProfessionals.find(p => p.id === Number(professionalId));

        await addRehabSession(clubId, {
          club_id: clubId,
          player_id: playerId,
          activity,
          date,
          time,
          duration: Number(duration),
          professional_id: professionalId,
          professional: selectedProfessional?.name || "",
          location: location,
          status: "Agendada",
          notes,
          treatment_description: updatedTreatmentDescriptions
        });
        toast({ title: "Sessão agendada com sucesso!", variant: "default" });
      }
      onOpenChange(false);
    } catch (e) {
      setError(isEdit ? "Erro ao atualizar sessão." : "Erro ao agendar sessão.");
      toast({ title: isEdit ? "Erro ao atualizar sessão." : "Erro ao agendar sessão.", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{isEdit ? "Editar Sessão" : "Agendar Sessão"}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="mb-4">
            <label className="text-sm font-medium mb-1 block">Tipo de Paciente</label>
            <select
              className="w-full border rounded p-2"
              value={patientType}
              onChange={e => setPatientType(e.target.value)}
              disabled={isEdit}
            >
              <option value="player">Jogador</option>
              <option value="collaborator">Colaborador</option>
            </select>
          </div>

          {patientType === "player" ? (
            <select
              className="w-full border rounded p-2"
              value={playerId}
              onChange={e => setPlayerId(e.target.value)}
            >
              <option value="">Selecione o jogador*</option>
              {players
                .sort((a, b) => a.name.localeCompare(b.name)) // Sort alphabetically
                .map((p) => (
                  <option key={p.id} value={p.id}>{p.name}</option>
                ))}
            </select>
          ) : (
            <select
              className="w-full border rounded p-2"
              value={playerId}
              onChange={e => setPlayerId(e.target.value)}
            >
              <option value="">Selecione o colaborador*</option>
              {collaborators
                .sort((a, b) => a.full_name.localeCompare(b.full_name)) // Sort alphabetically
                .map((c) => (
                  <option key={c.id} value={c.id.toString()}>{c.full_name} - {c.role}</option>
                ))}
            </select>
          )}
          <Input placeholder="Atividade*" value={activity} onChange={e => setActivity(e.target.value)} />
          <Input type="date" value={date} onChange={e => setDate(e.target.value)} />
          <Input type="time" value={time} onChange={e => setTime(e.target.value)} />
          <Input placeholder="Duração (min)*" value={duration} onChange={e => setDuration(e.target.value)} />

          {role === "medical" ? (
            // Para médicos: campo de texto somente leitura com o nome do médico logado
            <Input
              value={medicalProfessionals[0]?.name || ""}
              readOnly={true}
              className="bg-gray-50"
              placeholder="Seu perfil médico"
            />
          ) : (
            // Para outros usuários: select com todos os médicos disponíveis
            <select
              className="w-full border rounded p-2"
              value={professionalId}
              onChange={e => setProfessionalId(e.target.value)}
            >
              <option value="">Selecione o profissional médico*</option>
              {medicalProfessionals.map((p) => (
                <option key={p.id} value={p.id}>
                  {p.name} - {p.role} {p.address ? `(${p.address})` : ''}
                </option>
              ))}
            </select>
          )}

          {professionalId && (
            <div className="text-xs text-muted-foreground">
              {medicalProfessionals.find(p => p.id === Number(professionalId))?.address || 'Endereço não informado'}
            </div>
          )}
          <Input placeholder="Notas" value={notes} onChange={e => setNotes(e.target.value)} />

          {isEdit && (
            <div className="space-y-2">
              <label className="text-sm font-medium">
                Adicionar descrição do tratamento
                <span className="text-xs text-muted-foreground ml-1">(será adicionado ao histórico)</span>
              </label>
              <textarea
                className="w-full border rounded p-2 min-h-[80px]"
                placeholder="Descreva o tratamento realizado ou próximos passos..."
                value={treatmentDescription}
                onChange={e => setTreatmentDescription(e.target.value)}
              />

              {session?.treatment_description && session.treatment_description.length > 0 && (
                <div className="mt-2 border rounded p-2 bg-gray-50">
                  <h4 className="text-sm font-medium mb-1">Histórico de tratamento:</h4>
                  <div className="space-y-1 max-h-[150px] overflow-y-auto">
                    {session.treatment_description.map((entry, index) => (
                      <div key={index} className="text-xs border-b pb-1 last:border-0">
                        {entry}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {error && <div className="text-red-500 text-sm mt-1">{error}</div>}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancelar</Button>
          <Button onClick={handleSave} disabled={isLoading}>
            {isLoading ? <span className="loader mr-2" /> : null}
            {isEdit ? "Salvar" : "Agendar"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
