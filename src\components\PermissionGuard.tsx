import { ReactNode, useEffect, useState } from "react";
import { Navigate } from "react-router-dom";
import { usePermission } from "@/hooks/usePermission";
import { isAuthenticated } from "@/utils/auth";

interface PermissionGuardProps {
  children: ReactNode;
  permission?: string;
  permissions?: string[];
  requireAll?: boolean;
  fallbackPath?: string;
}

/**
 * Componente para proteger rotas com base em permissões
 * 
 * @param children Conteúdo a ser renderizado se o usuário tiver permissão
 * @param permission Permissão única necessária
 * @param permissions Lista de permissões (alternativa a permission)
 * @param requireAll Se true, o usuário precisa ter todas as permissões da lista. Se false, basta ter uma.
 * @param fallbackPath Caminho para redirecionar se o usuário não tiver permissão
 */
export function PermissionGuard({ 
  children, 
  permission, 
  permissions = [], 
  requireAll = false,
  fallbackPath = "/dashboard"
}: PermissionGuardProps) {
  const { isLoaded, can, canAny, canAll } = usePermission();
  const [hasAccess, setHasAccess] = useState(false);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Verificar se o usuário está autenticado
    if (!isAuthenticated()) {
      setHasAccess(false);
      setLoading(false);
      return;
    }
    
    // Aguardar o carregamento das permissões
    if (!isLoaded) {
      return;
    }
    
    // Verificar permissões
    if (permission) {
      setHasAccess(can(permission));
    } else if (permissions.length > 0) {
      setHasAccess(requireAll ? canAll(permissions) : canAny(permissions));
    } else {
      // Se não houver permissões especificadas, permitir acesso
      setHasAccess(true);
    }
    
    setLoading(false);
  }, [isLoaded, can, canAny, canAll, permission, permissions, requireAll]);
  
  // Enquanto estiver carregando, mostrar indicador de carregamento
  if (loading || !isLoaded) {
    return (
      <div className="flex justify-center items-center h-80">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-team-blue"></div>
      </div>
    );
  }
  
  // Se não tiver acesso, redirecionar
  if (!hasAccess) {
    return <Navigate to={fallbackPath} replace />;
  }
  
  // Se tiver acesso, renderizar o conteúdo
  return <>{children}</>;
}
