
import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useClubInfoStore } from "@/store/useClubInfoStore";
import { useToast } from "@/hooks/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { useTheme } from "@/context/ThemeContext";
import { ThemeSelector } from "@/components/ThemeSelector";
import { Upload, Image as ImageIcon } from "lucide-react";
import { uploadClubLogo } from "@/api/storage-simple";

export default function ConfiguracoesClube() {
  const { toast } = useToast();
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { setLogo, setClubName } = useTheme();

  const { clubInfo, loading, error, fetchClubInfo, updateClubInfo } = useClubInfoStore();

  const [name, setName] = useState("");
  const [stadium, setStadium] = useState("");
  const [foundedYear, setFoundedYear] = useState("");
  const [president, setPresident] = useState("");
  const [logoUrl, setLogoUrl] = useState("");
  const [address, setAddress] = useState("");
  const [zipCode, setZipCode] = useState("");
  const [phone, setPhone] = useState("");
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    fetchClubInfo(clubId);
  }, [fetchClubInfo, clubId]);

  useEffect(() => {
    if (clubInfo) {
      setName(clubInfo.name || "");
      setStadium(clubInfo.stadium || "");
      setFoundedYear(clubInfo.founded_year?.toString() || "");
      setPresident(clubInfo.president || "");
      setLogoUrl(clubInfo.logo_url || "");
      setAddress(clubInfo.address || "");
      setZipCode(clubInfo.zip_code || "");
      setPhone(clubInfo.phone || "");
    }
  }, [clubInfo]);

  // Função para adicionar um parâmetro de timestamp à URL para evitar cache
  const addCacheBuster = (url: string) => {
    const timestamp = new Date().getTime();
    // Verificar se a URL já tem parâmetros
    const hasParams = url.includes('?');
    const separator = hasParams ? '&' : '?';
    return `${url}${separator}t=${timestamp}`;
  };

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setUploading(true);

      // Fazer upload do arquivo
      const imageUrl = await uploadClubLogo(clubId.toString(), file);

      // Adicionar timestamp para evitar cache
      const urlWithTimestamp = addCacheBuster(imageUrl);

      // Atualizar a URL do logo
      setLogoUrl(urlWithTimestamp);

      // Limpar o cache da imagem
      const img = new Image();
      img.src = urlWithTimestamp;

      toast({
        title: "Logo enviado",
        description: "A imagem foi enviada com sucesso.",
      });
    } catch (err) {
      console.error("Erro ao fazer upload do logo:", err);
      toast({
        title: "Erro",
        description: err instanceof Error ? err.message : "Não foi possível enviar a imagem.",
        variant: "destructive",
      });
    } finally {
      setUploading(false);

      // Limpar o valor do input para permitir selecionar o mesmo arquivo novamente
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleSaveClub = async () => {
    try {
      const updatedClub = await updateClubInfo(clubId, {
        name,
        stadium,
        founded_year: foundedYear ? parseInt(foundedYear) : undefined,
        president,
        logo_url: logoUrl,
        address,
        zip_code: zipCode,
        phone,
      });

      // Atualizar o ThemeContext com as novas informações
      if (updatedClub) {
        if (updatedClub.name) {
          setClubName(updatedClub.name);
        }
        if (updatedClub.logo_url) {
          setLogo(updatedClub.logo_url);
        }
      }

      toast({
        title: "Clube atualizado",
        description: "As configurações do clube foram salvas com sucesso.",
      });
    } catch (err) {
      toast({
        title: "Erro",
        description: "Não foi possível salvar as alterações.",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-team-blue mx-auto mb-4"></div>
          <p className="text-muted-foreground">Carregando configurações...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Configurações</h1>
        <p className="text-muted-foreground">
          Gerencie as configurações do clube, usuários e preferências
        </p>
      </div>

      <Tabs defaultValue="club" className="w-full">
        <TabsList>
          <TabsTrigger value="club">Clube</TabsTrigger>
          <TabsTrigger value="user">Usuário</TabsTrigger>
          <TabsTrigger value="appearance">Interface</TabsTrigger>
          <TabsTrigger value="notifications">Notificações</TabsTrigger>
        </TabsList>
        <TabsContent value="club" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Informações do Clube</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex flex-col md:flex-row gap-6">
                <div className="space-y-4">
                  <div className="flex flex-col items-center">
                    <Avatar className="h-24 w-24">
                      <AvatarImage
                        src={logoUrl}
                        alt={name}
                        key={logoUrl} // Forçar remontagem quando a URL mudar
                      />
                      <AvatarFallback className="bg-team-blue text-white text-lg">
                        {name?.slice(0, 2).toUpperCase() || "FC"}
                      </AvatarFallback>
                    </Avatar>

                    <div className="mt-2 space-y-2">
                      <input
                        type="file"
                        ref={fileInputRef}
                        onChange={handleFileUpload}
                        accept="image/*"
                        className="hidden"
                        disabled={uploading}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => fileInputRef.current?.click()}
                        disabled={uploading}
                        className="w-full"
                      >
                        {uploading ? (
                          <div className="flex items-center">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900 mr-2"></div>
                            <span>Enviando...</span>
                          </div>
                        ) : (
                          <div className="flex items-center">
                            <Upload className="h-4 w-4 mr-2" />
                            <span>Enviar logo</span>
                          </div>
                        )}
                      </Button>

                      {logoUrl && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            // Forçar atualização da imagem adicionando um novo timestamp
                            setLogoUrl(addCacheBuster(logoUrl.split('?')[0]));
                          }}
                          className="w-full text-xs"
                        >
                          <div className="flex items-center">
                            <ImageIcon className="h-3 w-3 mr-1" />
                            <span>Atualizar visualização</span>
                          </div>
                        </Button>
                      )}
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">URL do Logo</label>
                    <div className="flex gap-2">
                      <Input
                        placeholder="URL do Logo"
                        value={logoUrl}
                        onChange={(e) => setLogoUrl(e.target.value)}
                      />
                    </div>
                  </div>
                </div>
                <div className="flex-1 space-y-4">
                  <div>
                    <label className="text-sm font-medium">Nome do Clube</label>
                    <Input
                      placeholder="Nome do clube"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Estádio</label>
                    <Input
                      placeholder="Estádio"
                      value={stadium}
                      onChange={(e) => setStadium(e.target.value)}
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium">Ano de Fundação</label>
                      <Input
                        placeholder="Ano de fundação"
                        type="number"
                        min="1800"
                        max={new Date().getFullYear()}
                        value={foundedYear}
                        onChange={(e) => setFoundedYear(e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Presidente</label>
                      <Input
                        placeholder="Presidente"
                        value={president}
                        onChange={(e) => setPresident(e.target.value)}
                      />
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Endereço</label>
                    <Input
                      placeholder="Endereço completo"
                      value={address}
                      onChange={(e) => setAddress(e.target.value)}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium">CEP</label>
                      <Input
                        placeholder="CEP"
                        value={zipCode}
                        onChange={(e) => setZipCode(e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Telefone</label>
                      <Input
                        placeholder="Telefone"
                        value={phone}
                        onChange={(e) => setPhone(e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </div>
              {error && <div className="text-red-500 text-sm">{error}</div>}
              <div className="flex justify-end">
                <Button onClick={handleSaveClub} disabled={loading}>
                  Salvar alterações
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Outras configurações do clube aqui */}

        </TabsContent>
        <TabsContent value="user" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Perfil de Usuário</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Nome</label>
                  <Input value={user?.name || ""} readOnly />
                </div>
                <div>
                  <label className="text-sm font-medium">Email</label>
                  <Input value={user?.email || ""} readOnly />
                </div>
                <div>
                  <label className="text-sm font-medium">ID de Usuário</label>
                  <Input value={user?.id || ""} readOnly />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Outras abas aqui */}
        <TabsContent value="appearance">
          <Card>
            <CardHeader>
              <CardTitle>Interface</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-4">Cores do Clube</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Personalize as cores do seu clube para refletir a identidade visual da sua equipe.
                  Estas cores serão aplicadas em todo o sistema.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <ThemeSelector />
                  <div className="space-y-4">
                    <div className="p-4 border rounded-md">
                      <h4 className="font-medium mb-2">Visualização</h4>
                      <div className="flex flex-col gap-2">
                        <div className="flex items-center gap-2">
                          <div className="w-6 h-6 rounded-full" style={{ backgroundColor: 'var(--color-primary)' }}></div>
                          <span className="text-sm">Cor Primária</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-6 h-6 rounded-full" style={{ backgroundColor: 'var(--color-secondary)' }}></div>
                          <span className="text-sm">Cor Secundária</span>
                        </div>
                      </div>
                    </div>
                    <div className="p-4 border rounded-md">
                      <h4 className="font-medium mb-2">Exemplo</h4>
                      <div className="p-3 rounded-md" style={{ backgroundColor: 'var(--color-primary)' }}>
                        <div className="text-white font-medium">Cabeçalho</div>
                      </div>
                      <div className="mt-2 p-3 border rounded-md">
                        <div className="font-medium" style={{ color: 'var(--color-primary)' }}>Título</div>
                        <div className="text-sm mt-1">Conteúdo de exemplo</div>
                        <button
                          className="mt-2 px-3 py-1 text-sm rounded-md text-white"
                          style={{ backgroundColor: 'var(--color-primary)' }}
                        >
                          Botão
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle>Notificações</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Configurações de notificações estarão disponíveis em breve.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
