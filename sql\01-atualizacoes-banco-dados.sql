-- Atualizações no Banco de Dados para o Sistema de Cadastro e Gestão de Usuários

-- 1. Atualizar tabela de usuários para incluir papel e permissões
ALTER TABLE users 
ADD COLUMN role TEXT DEFAULT 'user',
ADD COLUMN permissions JSONB DEFAULT '{}',
ADD COLUMN profile_image TEXT;

-- 2. <PERSON><PERSON><PERSON> tabela de departamentos
CREATE TABLE departments (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id),
  name TEXT NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. <PERSON><PERSON>r tabela de associação entre usuários e departamentos
CREATE TABLE user_departments (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id),
  user_id UUID REFERENCES users(id),
  department_id INTEGER REFERENCES departments(id),
  role TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Atualizar tabela de jogadores para incluir associação com usuário e campos adicionais
ALTER TABLE players
ADD COLUMN user_id UUID REFERENCES users(id),
ADD COLUMN observation TEXT,
ADD COLUMN is_accommodated BOOLEAN DEFAULT FALSE,
ADD COLUMN accommodation_id INTEGER REFERENCES accommodations(id),
ADD COLUMN financial_data JSONB DEFAULT '{}';

-- 5. Criar tabela de documentos de jogadores
CREATE TABLE player_documents (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id),
  player_id UUID REFERENCES players(id),
  document_type TEXT NOT NULL,
  file_url TEXT NOT NULL,
  status TEXT DEFAULT 'pending',
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  verified_at TIMESTAMP WITH TIME ZONE,
  verified_by UUID REFERENCES users(id)
);

-- 6. Criar tabela de convites para novos usuários
CREATE TABLE user_invitations (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id),
  email TEXT NOT NULL,
  role TEXT NOT NULL,
  department_id INTEGER REFERENCES departments(id),
  permissions JSONB DEFAULT '{}',
  token TEXT NOT NULL,
  status TEXT DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days')
);

-- 7. Criar políticas de segurança para o storage
-- Estas políticas devem ser executadas no console do Supabase ou via API

-- Criar bucket para imagens de perfil
-- INSERT INTO storage.buckets (id, name) VALUES ('profile_images', 'Profile Images');

-- Criar bucket para documentos de jogadores
-- INSERT INTO storage.buckets (id, name) VALUES ('player_documents', 'Player Documents');

-- Política para permitir leitura pública de imagens de perfil
-- CREATE POLICY "Public Read Access for Profile Images"
-- ON storage.objects FOR SELECT
-- USING (bucket_id = 'profile_images');

-- Política para permitir upload de imagens de perfil apenas para usuários autenticados
-- CREATE POLICY "Authenticated Users Can Upload Profile Images"
-- ON storage.objects FOR INSERT
-- TO authenticated
-- WITH CHECK (bucket_id = 'profile_images');

-- Política para permitir leitura de documentos apenas para usuários do mesmo clube
-- CREATE POLICY "Club Members Can Read Player Documents"
-- ON storage.objects FOR SELECT
-- USING (
--   bucket_id = 'player_documents' AND
--   EXISTS (
--     SELECT 1 FROM club_members
--     WHERE club_members.user_id = auth.uid() AND
--     club_members.club_id = (storage.objects.metadata->>'club_id')::integer
--   )
-- );

-- Política para permitir upload de documentos apenas para usuários do mesmo clube
-- CREATE POLICY "Club Members Can Upload Player Documents"
-- ON storage.objects FOR INSERT
-- TO authenticated
-- WITH CHECK (
--   bucket_id = 'player_documents' AND
--   EXISTS (
--     SELECT 1 FROM club_members
--     WHERE club_members.user_id = auth.uid() AND
--     club_members.club_id = (storage.objects.metadata->>'club_id')::integer
--   )
-- );
