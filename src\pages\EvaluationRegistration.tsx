import { useState, useEffect, useMemo } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { validatePlayerEvaluationInvitation, getPlayerEvaluationInvitation, usePlayerEvaluationInvitation } from "@/api/playerEvaluationInvitations";
import { createPlayer } from "@/api/players";
import { uploadDocument } from "@/api/documents";
import { supabase } from "@/integrations/supabase/client";
import { validateCPF } from "@/api/external";
import { FileUploader } from "@/components/ui/file-uploader";
import { FormTemplateDownloadLinks } from "@/components/forms/FormTemplateDownloadLinks";

export default function EvaluationRegistration() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { toast } = useToast();

  const token = searchParams.get("token") || "";

  const [isValid, setIsValid] = useState<boolean | null>(null);
  const [clubId, setClubId] = useState<number | null>(null);
  const [clubName, setClubName] = useState<string>("");
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Player form state
  const [name, setName] = useState("");
  const [birthdate, setBirthdate] = useState("");
  const [position, setPosition] = useState("");
  const [height, setHeight] = useState("");
  const [weight, setWeight] = useState("");
  const [nationality, setNationality] = useState("Brasileiro");
  const [cpf, setCpf] = useState("");
  const [rg, setRg] = useState("");
  const [phone, setPhone] = useState("");
  const [email, setEmail] = useState("");
  const [address, setAddress] = useState("");
  const [city, setCity] = useState("");
  const [state, setState] = useState("");
  const [zipCode, setZipCode] = useState("");
  const [fatherName, setFatherName] = useState("");
  const [motherName, setMotherName] = useState("");
  const [referredBy, setReferredBy] = useState("");

  // Document uploads
  const [photoFile, setPhotoFile] = useState<File | null>(null);
  const [rgFile, setRgFile] = useState<File | null>(null);
  const [cpfFile, setCpfFile] = useState<File | null>(null);
  const [birthCertificateFile, setBirthCertificateFile] = useState<File | null>(null);
  const [vaccinationCardFile, setVaccinationCardFile] = useState<File | null>(null);
  const [guardianDocumentFile, setGuardianDocumentFile] = useState<File | null>(null);
  const [schoolDeclarationFile, setSchoolDeclarationFile] = useState<File | null>(null);
  const [medicalCertFile, setMedicalCertFile] = useState<File | null>(null);
  const [evaluationFormFile, setEvaluationFormFile] = useState<File | null>(null);
  const [housingFormFile, setHousingFormFile] = useState<File | null>(null);
  const [liabilityWaiverFile, setLiabilityWaiverFile] = useState<File | null>(null);

  // Check if athlete is a minor (under 18)
  const isMinor = useMemo(() => {
    if (!birthdate) return false;

    const today = new Date();
    const birthDate = new Date(birthdate);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age < 18;
  }, [birthdate]);

  // Validate token on mount
  useEffect(() => {
    const validateToken = async () => {
      try {
        setLoading(true);

        if (!token) {
          setIsValid(false);
          setError("Token de convite não fornecido");
          return;
        }

        // Validate token
        const valid = await validatePlayerEvaluationInvitation(token);
        setIsValid(valid);

        if (!valid) {
          setError("Token de convite inválido ou expirado");
          return;
        }

        // Get invitation details
        const invitation = await getPlayerEvaluationInvitation(token);

        if (!invitation) {
          setIsValid(false);
          setError("Convite não encontrado");
          return;
        }

        setClubId(invitation.club_id);

        // Get club name
        const { data: clubData } = await supabase
          .from("club_info")
          .select("name")
          .eq("id", invitation.club_id)
          .single();

        if (clubData) {
          setClubName(clubData.name);
        }

        // Pre-fill email and CPF if available
        if (invitation.email) {
          setEmail(invitation.email);
        }

        if (invitation.cpf) {
          setCpf(invitation.cpf);
        }

        // Get the name of the collaborator who created the invitation
        if (invitation.created_by) {
          try {
            // First try to get from club_members
            const { data: memberData, error: memberError } = await supabase
              .from("club_members")
              .select("name")
              .eq("user_id", invitation.created_by)
              .eq("club_id", invitation.club_id)
              .single();

            if (memberData && memberData.name) {
              setReferredBy(memberData.name);
            } else {
              // If not found in club_members, try to get from auth.users
              const { data: userData, error: userError } = await supabase
                .from("users")
                .select("name")
                .eq("id", invitation.created_by)
                .single();

              if (userData && userData.name) {
                setReferredBy(userData.name);
              }
            }
          } catch (err) {
            console.error("Erro ao buscar nome do colaborador:", err);
            // Don't set error here, just log it
          }
        }
      } catch (err: any) {
        console.error("Erro ao validar token:", err);
        setIsValid(false);
        setError(err.message || "Erro ao validar token");
      } finally {
        setLoading(false);
      }
    };

    validateToken();
  }, [token]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!clubId) {
      setError("Clube não identificado");
      return;
    }

    // Validate required fields
    if (!name || !birthdate || !position || !cpf || !phone || !email || !height || !weight || !nationality || !rg || !fatherName || !motherName) {
      setError("Preencha todos os campos obrigatórios");
      return;
    }

    // Validate CPF
    if (!validateCPF(cpf)) {
      setError("CPF inválido");
      return;
    }

    // Validate required files
    if (!photoFile) {
      setError("A foto é obrigatória");
      return;
    }

    // We already have isMinor from the useMemo hook

    // Check for required documents for minors
    if (isMinor && !guardianDocumentFile) {
      setError("O documento do responsável é obrigatório para menores de 18 anos");
      return;
    }

    try {
      setSubmitting(true);
      setError(null);

      console.log("[DEBUG] Iniciando cadastro de jogador em avaliação");
      console.log("[DEBUG] Club ID:", clubId);

      // Calculate age from birthdate
      const calculateAge = (birthdate: string): number => {
        const today = new Date();
        const birthDate = new Date(birthdate);
        let age = today.getFullYear() - birthDate.getFullYear();
        const m = today.getMonth() - birthDate.getMonth();
        if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
          age--;
        }
        return age;
      };

      const age = calculateAge(birthdate);
      console.log("[DEBUG] Idade calculada:", age, "É menor de idade:", isMinor);

      // Preparar dados do jogador
      const playerData = {
        name,
        birthdate,
        position,
        age, // Add calculated age
        number: 0, // Add default jersey number (0) for players in evaluation
        height: height ? parseInt(height, 10) : undefined, // Use parseInt instead of parseFloat
        weight: weight ? parseFloat(weight) : undefined,
        nationality,
        status: "em avaliacao", // Set status to "em avaliacao"
        cpf_number: cpf,
        rg_number: rg,
        phone,
        email,
        address,
        city,
        state,
        zip_code: zipCode,
        father_name: fatherName,
        mother_name: motherName,
        referred_by: referredBy
      };

      console.log("[DEBUG] Dados do jogador:", playerData);

      // Create player
      const player = await createPlayer(
        clubId,
        playerData
      );

      // Upload photo and set as profile image
      if (photoFile) {
        // Upload as document
        await uploadDocument(clubId, player.id, "photo", photoFile);

        // Also upload as profile image
        try {
          const { data: uploadData, error: uploadError } = await supabase.storage
            .from('profileimages')
            .upload(`${clubId}/${player.id}/profile.${photoFile.name.split('.').pop()}`, photoFile, {
              cacheControl: '3600',
              upsert: true,
            });

          if (!uploadError) {
            // Get public URL
            const { data: urlData } = supabase.storage
              .from('profileimages')
              .getPublicUrl(`${clubId}/${player.id}/profile.${photoFile.name.split('.').pop()}`);

            // Update player with image URL
            await supabase
              .from("players")
              .update({ image: urlData.publicUrl })
              .eq("id", player.id)
              .eq("club_id", clubId);
          }
        } catch (err) {
          console.error("Erro ao definir foto de perfil:", err);
          // Don't throw error here to continue with registration
        }
      }

      // Upload RG
      if (rgFile) {
        await uploadDocument(clubId, player.id, "id_card", rgFile);
      }

      // Upload CPF
      if (cpfFile) {
        await uploadDocument(clubId, player.id, "cpf", cpfFile);
      }

      // Upload Birth Certificate
      if (birthCertificateFile) {
        await uploadDocument(clubId, player.id, "birth_certificate", birthCertificateFile);
      }

      // Upload Vaccination Card
      if (vaccinationCardFile) {
        await uploadDocument(clubId, player.id, "vaccination_card", vaccinationCardFile);
      }

      // Upload Guardian Document (for minors)
      if (guardianDocumentFile) {
        await uploadDocument(clubId, player.id, "guardian_document", guardianDocumentFile);
      }

      // Upload School Declaration
      if (schoolDeclarationFile) {
        await uploadDocument(clubId, player.id, "school_declaration", schoolDeclarationFile);
      }

      // Upload Medical Certificate
      if (medicalCertFile) {
        await uploadDocument(clubId, player.id, "medical_certificate", medicalCertFile);
      }

      // Upload Evaluation Form
      if (evaluationFormFile) {
        await uploadDocument(clubId, player.id, "evaluation_form", evaluationFormFile);
      }

      // Upload Housing Form
      if (housingFormFile) {
        await uploadDocument(clubId, player.id, "housing_authorization", housingFormFile);
      }

      // Upload Liability Waiver
      if (liabilityWaiverFile) {
        await uploadDocument(clubId, player.id, "liability_waiver", liabilityWaiverFile);
      }

      // Upload additional documents that are required for regular players
      // These would be added in the future as needed

      // Mark invitation as used
      await usePlayerEvaluationInvitation(token, player.id);

      toast({
        title: "Cadastro realizado com sucesso",
        description: "Seus dados foram enviados para pré cadastro. Você receberá um email com mais informações em breve.",
      });

      // Redirect to success page
      navigate("/evaluation-success");
    } catch (err: any) {
      console.error("Erro ao cadastrar jogador:", err);
      setError(err.message || "Erro ao cadastrar jogador");
      toast({
        title: "Erro",
        description: err.message || "Erro ao cadastrar jogador",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="container max-w-4xl py-10">
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-center items-center h-40">
              <p className="text-muted-foreground">Verificando convite...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show error if token is invalid
  if (!isValid) {
    return (
      <div className="container max-w-4xl py-10">
        <Card>
          <CardHeader>
            <CardTitle className="text-center text-red-600">Convite Inválido</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <p className="mb-4">{error || "O link de convite é inválido ou expirou."}</p>
              <p>Entre em contato com o clube para solicitar um novo convite.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show registration form
  return (
    <div className="container max-w-4xl py-10">
      <Card>
        <CardHeader>
          <CardTitle>Cadastro para Pré Cadastro</CardTitle>
          <CardDescription>
            Preencha o formulário abaixo para se cadastrar no processo de pré cadastro do clube {clubName}.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <Tabs defaultValue="personal" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="personal">Dados Pessoais</TabsTrigger>
                <TabsTrigger value="contact">Contato</TabsTrigger>
                <TabsTrigger value="documents">Documentos</TabsTrigger>
              </TabsList>

              {/* Personal Data Tab */}
              <TabsContent value="personal" className="space-y-4 pt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Nome Completo*</Label>
                    <Input
                      id="name"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="birthdate">Data de Nascimento*</Label>
                    <Input
                      id="birthdate"
                      type="date"
                      value={birthdate}
                      onChange={(e) => setBirthdate(e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="position">Posição*</Label>
                    <Select value={position} onValueChange={setPosition} required>
                      <SelectTrigger id="position">
                        <SelectValue placeholder="Selecione" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Goleiro">Goleiro</SelectItem>
                        <SelectItem value="Lateral Direito">Lateral Direito</SelectItem>
                        <SelectItem value="Zagueiro">Zagueiro</SelectItem>
                        <SelectItem value="Lateral Esquerdo">Lateral Esquerdo</SelectItem>
                        <SelectItem value="Volante">Volante</SelectItem>
                        <SelectItem value="Meias">Meias</SelectItem>
                        <SelectItem value="Extremo">Extremo</SelectItem>
                        <SelectItem value="Atacante">Atacante</SelectItem>
                        <SelectItem value="Centroavante">Centroavante</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="height">Altura (cm)*</Label>
                    <Input
                      id="height"
                      type="number"
                      step="1"
                      min="100"
                      max="250"
                      value={height}
                      onChange={(e) => setHeight(e.target.value)}
                      placeholder="Ex: 175"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="weight">Peso (kg)*</Label>
                    <Input
                      id="weight"
                      type="number"
                      step="0.1"
                      min="30"
                      max="150"
                      value={weight}
                      onChange={(e) => setWeight(e.target.value)}
                      placeholder="Ex: 70"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="nationality">Nacionalidade*</Label>
                    <Input
                      id="nationality"
                      value={nationality}
                      onChange={(e) => setNationality(e.target.value)}
                      placeholder="Nacionalidade"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="cpf">CPF*</Label>
                    <Input
                      id="cpf"
                      value={cpf}
                      onChange={(e) => setCpf(e.target.value.replace(/\D/g, ''))}
                      placeholder="Apenas números"
                      maxLength={11}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="rg">RG*</Label>
                    <Input
                      id="rg"
                      value={rg}
                      onChange={(e) => setRg(e.target.value)}
                      placeholder="Número do RG"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="fatherName">Nome do Pai*</Label>
                    <Input
                      id="fatherName"
                      value={fatherName}
                      onChange={(e) => setFatherName(e.target.value)}
                      placeholder="Nome completo do pai"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="motherName">Nome da Mãe*</Label>
                    <Input
                      id="motherName"
                      value={motherName}
                      onChange={(e) => setMotherName(e.target.value)}
                      placeholder="Nome completo da mãe"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="referredBy">Indicado por</Label>
                  <Input
                    id="referredBy"
                    value={referredBy}
                    onChange={(e) => setReferredBy(e.target.value)}
                    placeholder="Nome de quem indicou (se aplicável)"
                  />
                </div>
              </TabsContent>

              {/* Contact Tab */}
              <TabsContent value="contact" className="space-y-4 pt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="phone">Telefone*</Label>
                    <Input
                      id="phone"
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                      placeholder="(00) 00000-0000"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email*</Label>
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address">Endereço</Label>
                  <Input
                    id="address"
                    value={address}
                    onChange={(e) => setAddress(e.target.value)}
                    placeholder="Rua, número, complemento"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="zipCode">CEP</Label>
                    <Input
                      id="zipCode"
                      value={zipCode}
                      onChange={(e) => setZipCode(e.target.value)}
                      placeholder="00000-000"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="city">Cidade</Label>
                    <Input
                      id="city"
                      value={city}
                      onChange={(e) => setCity(e.target.value)}
                      placeholder="Cidade"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="state">Estado</Label>
                    <Input
                      id="state"
                      value={state}
                      onChange={(e) => setState(e.target.value)}
                      placeholder="Estado"
                    />
                  </div>
                </div>
              </TabsContent>

              {/* Documents Tab */}
              <TabsContent value="documents" className="space-y-4 pt-4">
                <div className="space-y-2">
                  <Label htmlFor="photo">Foto (obrigatório)</Label>
                  <FileUploader
                    id="photo"
                    accept="image/*"
                    maxSize={5}
                    onFileSelect={setPhotoFile}
                    currentFile={photoFile}
                  />
                  <p className="text-xs text-muted-foreground">
                    Envie uma foto de rosto recente, fundo claro, formato 3x4.
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="rgFile">RG</Label>
                  <FileUploader
                    id="rgFile"
                    accept=".pdf,image/*"
                    maxSize={5}
                    onFileSelect={setRgFile}
                    currentFile={rgFile}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="cpfFile">CPF</Label>
                  <FileUploader
                    id="cpfFile"
                    accept=".pdf,image/*"
                    maxSize={5}
                    onFileSelect={setCpfFile}
                    currentFile={cpfFile}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="birthCertificateFile">Certidão de Nascimento</Label>
                  <FileUploader
                    id="birthCertificateFile"
                    accept=".pdf,image/*"
                    maxSize={5}
                    onFileSelect={setBirthCertificateFile}
                    currentFile={birthCertificateFile}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="vaccinationCardFile">Carteira de Vacinação</Label>
                  <FileUploader
                    id="vaccinationCardFile"
                    accept=".pdf,image/*"
                    maxSize={5}
                    onFileSelect={setVaccinationCardFile}
                    currentFile={vaccinationCardFile}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="guardianDocumentFile">Documento do Responsável {isMinor && "(obrigatório para menores de 18 anos)"}</Label>
                  <FileUploader
                    id="guardianDocumentFile"
                    accept=".pdf,image/*"
                    maxSize={5}
                    onFileSelect={setGuardianDocumentFile}
                    currentFile={guardianDocumentFile}
                  />
                  {isMinor && (
                    <p className="text-xs text-muted-foreground">
                      Documento de identidade do responsável legal (obrigatório para menores de 18 anos).
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="schoolDeclarationFile">Declaração Escolar</Label>
                  <FileUploader
                    id="schoolDeclarationFile"
                    accept=".pdf,image/*"
                    maxSize={5}
                    onFileSelect={setSchoolDeclarationFile}
                    currentFile={schoolDeclarationFile}
                  />
                  <p className="text-xs text-muted-foreground">
                    Declaração de matrícula escolar atual.
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="medicalCertFile">Atestado Médico</Label>
                  <FileUploader
                    id="medicalCertFile"
                    accept=".pdf,image/*"
                    maxSize={5}
                    onFileSelect={setMedicalCertFile}
                    currentFile={medicalCertFile}
                  />
                  <p className="text-xs text-muted-foreground">
                    Atestado médico comprovando aptidão para prática esportiva.
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="evaluationFormFile">Ficha de Pré Cadastro</Label>
                  <FileUploader
                    id="evaluationFormFile"
                    accept=".pdf,image/*"
                    maxSize={5}
                    onFileSelect={setEvaluationFormFile}
                    currentFile={evaluationFormFile}
                  />
                  <FormTemplateDownloadLinks
                    clubId={clubId}
                    formType="pre_registration"
                    label="Ficha de Pré-cadastro"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="housingFormFile">Ficha de Moradia</Label>
                  <FileUploader
                    id="housingFormFile"
                    accept=".pdf,image/*"
                    maxSize={5}
                    onFileSelect={setHousingFormFile}
                    currentFile={housingFormFile}
                  />
                  <FormTemplateDownloadLinks
                    clubId={clubId}
                    formType="housing"
                    label="Ficha de Moradia"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="liabilityWaiverFile">Termo de Isenção de Responsabilidade</Label>
                  <FileUploader
                    id="liabilityWaiverFile"
                    accept=".pdf,image/*"
                    maxSize={5}
                    onFileSelect={setLiabilityWaiverFile}
                    currentFile={liabilityWaiverFile}
                  />
                  <FormTemplateDownloadLinks
                    clubId={clubId}
                    formType="liability_waiver"
                    label="Termo de Isenção de Responsabilidade"
                  />
                </div>
              </TabsContent>
            </Tabs>

            {error && (
              <div className="p-4 bg-red-50 text-red-800 rounded-md">
                {error}
              </div>
            )}
          </form>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" type="button" onClick={() => window.history.back()}>
            Voltar
          </Button>
          <Button onClick={handleSubmit} disabled={submitting}>
            {submitting ? "Enviando..." : "Enviar Cadastro"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
