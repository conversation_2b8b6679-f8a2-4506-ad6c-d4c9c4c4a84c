-- Adicionar campo de número de cadastro à tabela players
ALTER TABLE players
ADD COLUMN registration_number TEXT;

-- Criar índice para garantir unicidade do número de cadastro
CREATE UNIQUE INDEX idx_players_registration_number ON players(club_id, registration_number)
WHERE registration_number IS NOT NULL;

-- Adicionar comentário explicativo
COMMENT ON COLUMN players.registration_number IS 'Número de cadastro único do jogador, gerado automaticamente';

-- Criar função para gerar número de cadastro aleatório
CREATE OR REPLACE FUNCTION generate_registration_number()
RETURNS TEXT AS $$
DECLARE
    random_number TEXT;
BEGIN
    -- Gerar número aleatório de 6 dígitos
    random_number := LPAD(FLOOR(RANDOM() * 1000000)::TEXT, 6, '0');
    RETURN random_number;
END;
$$ LANGUAGE plpgsql;

-- Atualizar jogadores existentes com números de cadastro
DO $$
DECLARE
    player_record RECORD;
    new_reg_number TEXT;
    is_duplicate BOOLEAN;
BEGIN
    FOR player_record IN SELECT id, club_id FROM players WHERE registration_number IS NULL LOOP
        -- Loop até encontrar um número único
        LOOP
            new_reg_number := generate_registration_number();
            
            -- Verificar se o número já existe para este clube
            SELECT EXISTS (
                SELECT 1 FROM players 
                WHERE club_id = player_record.club_id 
                AND registration_number = new_reg_number
            ) INTO is_duplicate;
            
            -- Se não for duplicado, sair do loop
            IF NOT is_duplicate THEN
                EXIT;
            END IF;
        END LOOP;
        
        -- Atualizar o jogador com o novo número de cadastro
        UPDATE players 
        SET registration_number = new_reg_number 
        WHERE id = player_record.id;
    END LOOP;
END $$;
