import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import type { Exercise } from "@/api/api";

interface NovoExercicioDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreate: (exercise: { name: string; description?: string; category: string; difficulty: string }) => Promise<void>;
  loading?: boolean;
}

export function NovoExercicioDialog({ open, onOpenChange, onCreate, loading }: NovoExercicioDialogProps) {
  const [form, setForm] = useState({
    name: "",
    description: "",
    category: "",
    difficulty: ""
  });
  const [error, setError] = useState<string | null>(null);

  function handleChange(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) {
    setForm({ ...form, [e.target.name]: e.target.value });
  }

  function handleSelect(field: "category" | "difficulty", value: string) {
    setForm({ ...form, [field]: value });
  }

  async function handleSubmit() {
    setError(null);
    if (!form.name || !form.category || !form.difficulty) {
      setError("Preencha os campos obrigatórios.");
      return;
    }
    try {
      await onCreate(form);
      setForm({ name: "", description: "", category: "", difficulty: "" });
      onOpenChange(false);
    } catch (e: any) {
      setError(e.message || "Erro ao criar exercício");
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Novo Exercício</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-2">
          <Input name="name" placeholder="Nome*" value={form.name} onChange={handleChange} autoFocus disabled={loading} />
          <Select value={form.category} onValueChange={v => handleSelect("category", v)} disabled={loading}>
            <SelectTrigger>
              <SelectValue placeholder="Categoria* (força, tático, técnico)" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="força">Força</SelectItem>
              <SelectItem value="tático">Tático</SelectItem>
              <SelectItem value="técnico">Técnico</SelectItem>
            </SelectContent>
          </Select>
          <Select value={form.difficulty} onValueChange={v => handleSelect("difficulty", v)} disabled={loading}>
            <SelectTrigger>
              <SelectValue placeholder="Grupo muscular*" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="superiores">Membros Superiores</SelectItem>
              <SelectItem value="inferiores">Membros Inferiores</SelectItem>
              <SelectItem value="core">Core</SelectItem>
              <SelectItem value="geral">Geral</SelectItem>
            </SelectContent>
          </Select>
          <Input name="description" placeholder="Descrição" value={form.description} onChange={handleChange} disabled={loading} />
          {error && <div className="text-red-500 text-xs text-center mt-1">{error}</div>}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={loading}>Cancelar</Button>
          <Button onClick={handleSubmit} disabled={loading}>Salvar</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
