import { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";
import { uploadProfileImage } from "@/api/api";
import { getFirstName } from "@/api/external";
import { Upload, X } from "lucide-react";

interface AvatarUploadProps {
  userId: string;
  name: string;
  imageUrl?: string;
  onImageChange?: (url: string) => void;
  size?: "sm" | "md" | "lg";
}

export function AvatarUpload({
  userId,
  name,
  imageUrl,
  onImageChange,
  size = "md",
}: AvatarUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  // Obter as iniciais do nome do usuário
  const getInitials = (name: string) => {
    if (!name) return "U";

    const firstName = getFirstName(name);
    if (firstName.length <= 2) return firstName.toUpperCase();

    return firstName.charAt(0).toUpperCase();
  };

  // Obter a classe de tamanho
  const getSizeClass = () => {
    switch (size) {
      case "sm":
        return "h-10 w-10 text-xs";
      case "lg":
        return "h-24 w-24 text-xl";
      default:
        return "h-16 w-16 text-base";
    }
  };

  // Obter a classe de tamanho para os botões
  const getButtonSizeClass = () => {
    switch (size) {
      case "sm":
        return "h-6 w-6";
      case "lg":
        return "h-10 w-10";
      default:
        return "h-8 w-8";
    }
  };

  // Função para fazer upload da imagem
  const handleImageUpload = async (file: File) => {
    try {
      setUploading(true);

      // Criar URL de preview
      const preview = URL.createObjectURL(file);
      setPreviewUrl(preview);

      // Fazer upload da imagem
      const imageUrl = await uploadProfileImage(userId, file);

      // Salvar no localStorage para uso imediato
      localStorage.setItem("userProfileImage", imageUrl);

      // Notificar o componente pai
      if (onImageChange) {
        onImageChange(imageUrl);
      }

      toast({
        title: "Sucesso",
        description: "Imagem de perfil atualizada com sucesso",
      });
    } catch (err: any) {
      console.error("Erro ao fazer upload da imagem:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao fazer upload da imagem",
        variant: "destructive",
      });

      // Limpar preview em caso de erro
      setPreviewUrl(null);
    } finally {
      setUploading(false);
    }
  };

  // Função para lidar com o input de arquivo
  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleImageUpload(file);
    }
  };

  // Função para remover a imagem
  const handleRemoveImage = () => {
    setPreviewUrl(null);

    // Notificar o componente pai
    if (onImageChange) {
      onImageChange("");
    }
  };

  // URL da imagem atual (preview ou imageUrl)
  const currentImageUrl = previewUrl || imageUrl;

  return (
    <div className="relative">
      <Avatar className={getSizeClass()}>
        <AvatarImage src={currentImageUrl} alt={name} />
        <AvatarFallback className="bg-team-blue/10 text-team-blue">
          {getInitials(name)}
        </AvatarFallback>
      </Avatar>

      <div className="absolute -bottom-2 -right-2 flex space-x-1">
        <div className="relative">
          <input
            type="file"
            id="avatar-upload"
            className="absolute inset-0 opacity-0 cursor-pointer"
            onChange={handleFileInput}
            accept="image/*"
            disabled={uploading}
          />
          <Button
            variant="secondary"
            size="icon"
            className={`rounded-full ${getButtonSizeClass()}`}
            disabled={uploading}
          >
            {uploading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
            ) : (
              <Upload className="h-4 w-4" />
            )}
          </Button>
        </div>

        {currentImageUrl && (
          <Button
            variant="destructive"
            size="icon"
            className={`rounded-full ${getButtonSizeClass()}`}
            onClick={handleRemoveImage}
            disabled={uploading}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}
