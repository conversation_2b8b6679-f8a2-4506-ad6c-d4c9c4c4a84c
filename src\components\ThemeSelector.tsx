import { useState, useEffect } from "react";
import { useTheme } from "@/context/ThemeContext";
import { teamThemes } from "@/data/themes";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Check } from "lucide-react";
import { useCurrentClubId } from "@/context/ClubContext";
import { useClubInfoStore } from "@/store/useClubInfoStore";
import { toast } from "@/components/ui/use-toast";

export function ThemeSelector() {
  const { theme, setThemeId } = useTheme();
  const clubId = useCurrentClubId();
  const { updateClubInfo } = useClubInfoStore();
  const [selectedTheme, setSelectedTheme] = useState(theme.id);
  const [customPrimary, setCustomPrimary] = useState("");
  const [customSecondary, setCustomSecondary] = useState("");
  const [showCustom, setShowCustom] = useState(false);
  const [saving, setSaving] = useState(false);

  // Atualizar o tema selecionado quando o tema global mudar
  useEffect(() => {
    setSelectedTheme(theme.id);
  }, [theme.id]);

  // Função para aplicar o tema selecionado
  const handleApplyTheme = async () => {
    try {
      setSaving(true);
      await updateClubInfo(clubId, {
        primary_color: selectedTheme,
        secondary_color: teamThemes[selectedTheme]?.colors.secondary
      });
      setThemeId(selectedTheme);

      toast({
        title: "Tema atualizado",
        description: "As cores do clube foram atualizadas com sucesso.",
      });
    } catch (error) {
      toast({
        title: "Erro",
        description: "Não foi possível atualizar o tema.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Função para aplicar cores personalizadas
  const handleApplyCustomColors = async () => {
    if (!customPrimary || !customSecondary) {
      toast({
        title: "Erro",
        description: "Por favor, preencha ambas as cores.",
        variant: "destructive",
      });
      return;
    }

    try {
      setSaving(true);
      await updateClubInfo(clubId, {
        primary_color: "custom",
        secondary_color: customSecondary
      });

      // Aplicar cores customizadas diretamente
      const root = document.documentElement;
      root.style.setProperty("--color-primary", customPrimary);
      root.style.setProperty("--color-secondary", customSecondary);

      toast({
        title: "Cores personalizadas aplicadas",
        description: "As cores customizadas foram salvas com sucesso.",
      });
    } catch (error) {
      toast({
        title: "Erro",
        description: "Não foi possível salvar as cores personalizadas.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="space-y-4">
      <RadioGroup
        value={selectedTheme}
        onValueChange={setSelectedTheme}
        className="grid grid-cols-2 gap-2"
      >
        {Object.values(teamThemes).map((themeOption) => (
          <div key={themeOption.id} className="flex items-center space-x-2">
            <RadioGroupItem value={themeOption.id} id={`theme-${themeOption.id}`} />
            <Label
              htmlFor={`theme-${themeOption.id}`}
              className="flex items-center gap-2 cursor-pointer"
            >
              <div
                className="w-5 h-5 rounded-full border"
                style={{ backgroundColor: themeOption.colors.primary }}
              ></div>
              <span>{themeOption.name}</span>
              {theme.id === themeOption.id && (
                <Check className="h-4 w-4 text-green-500" />
              )}
            </Label>
          </div>
        ))}
      </RadioGroup>

      <Button
        onClick={handleApplyTheme}
        variant="outline"
        size="sm"
        className="w-full"
        disabled={saving}
      >
        {saving ? "Salvando..." : "Aplicar Tema"}
      </Button>

      <div className="pt-4 border-t">
        <div className="flex items-center justify-between mb-2">
          <h4 className="font-medium">Cores Personalizadas</h4>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowCustom(!showCustom)}
          >
            {showCustom ? "Ocultar" : "Mostrar"}
          </Button>
        </div>

        {showCustom && (
          <div className="space-y-3">
            <div className="space-y-2">
              <Label htmlFor="primary-color">Cor Primária</Label>
              <div className="flex gap-2">
                <Input
                  id="primary-color"
                  type="text"
                  placeholder="#1e40af"
                  value={customPrimary}
                  onChange={(e) => setCustomPrimary(e.target.value)}
                />
                <div
                  className="w-10 h-10 rounded border"
                  style={{ backgroundColor: customPrimary || theme.colors.primary }}
                ></div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="secondary-color">Cor Secundária</Label>
              <div className="flex gap-2">
                <Input
                  id="secondary-color"
                  type="text"
                  placeholder="#60a5fa"
                  value={customSecondary}
                  onChange={(e) => setCustomSecondary(e.target.value)}
                />
                <div
                  className="w-10 h-10 rounded border"
                  style={{ backgroundColor: customSecondary || theme.colors.secondary }}
                ></div>
              </div>
            </div>

            <Button
              onClick={handleApplyCustomColors}
              variant="outline"
              size="sm"
              className="w-full"
              disabled={saving}
            >
              {saving ? "Salvando..." : "Aplicar Cores Personalizadas"}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
