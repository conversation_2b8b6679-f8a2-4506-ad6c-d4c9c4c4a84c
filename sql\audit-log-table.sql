-- <PERSON><PERSON><PERSON> tabela de log de auditoria
CREATE TABLE IF NOT EXISTS audit_logs (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id),
  user_id UUID REFERENCES users(id),
  action TEXT NOT NULL,
  details J<PERSON>N<PERSON> DEFAULT '{}',
  success BOOLEAN NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON><PERSON> índices para melhorar a performance
CREATE INDEX IF NOT EXISTS idx_audit_logs_club_id ON audit_logs(club_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);

-- Ativar RLS na tabela
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Política para audit_logs: usu<PERSON>rios só podem ver logs do seu clube
CREATE POLICY audit_logs_club_isolation ON audit_logs
    USING (club_id IN (
        SELECT club_id FROM club_members WHERE user_id = auth.uid()
    ));

-- Política para audit_logs: apenas usuários com permissão podem ver logs
CREATE POLICY audit_logs_permission ON audit_logs
    USING (
        EXISTS (
            SELECT 1 FROM club_members 
            WHERE club_id = audit_logs.club_id 
            AND user_id = auth.uid()
            AND (
                role = 'admin' 
                OR role = 'president'
                OR permissions::jsonb ? 'audit_logs.view'
            )
        )
    );

-- Função para registrar eventos de auditoria
CREATE OR REPLACE FUNCTION log_audit_event(
    p_club_id INTEGER,
    p_user_id UUID,
    p_action TEXT,
    p_details JSONB,
    p_success BOOLEAN
) RETURNS INTEGER AS $$
DECLARE
    v_log_id INTEGER;
BEGIN
    INSERT INTO audit_logs (
        club_id,
        user_id,
        action,
        details,
        success,
        created_at
    ) VALUES (
        p_club_id,
        p_user_id,
        p_action,
        p_details,
        p_success,
        NOW()
    ) RETURNING id INTO v_log_id;
    
    RETURN v_log_id;
END;
$$ LANGUAGE plpgsql;
