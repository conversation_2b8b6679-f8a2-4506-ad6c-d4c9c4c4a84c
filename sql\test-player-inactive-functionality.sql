-- Script de teste para funcionalidade de jogador inativo
-- Execute este script para testar se a remoção de vinculações está funcionando corretamente

-- 1. Criar um jogador de teste (substitua os valores conforme necessário)
DO $$
DECLARE
    test_club_id INTEGER := 1; -- Substitua pelo ID do seu clube
    test_player_id UUID;
    test_category_id INTEGER;
    test_accommodation_id INTEGER;
BEGIN
    -- Inserir jogador de teste
    INSERT INTO players (
        id, club_id, name, position, age, number, status, birthdate
    ) VALUES (
        gen_random_uuid(), test_club_id, 'Jogador Teste Inativo', 'Atacante', 25, 999, 'disponivel', '1999-01-01'
    ) RETURNING id INTO test_player_id;
    
    RAISE NOTICE 'Jogador de teste criado com ID: %', test_player_id;
    
    -- Criar categoria de teste se não existir
    INSERT INTO categories (club_id, name, type, description)
    VALUES (test_club_id, 'Categoria Teste', 'custom', 'Categoria para teste de inativação')
    ON CONFLICT DO NOTHING
    RETURNING id INTO test_category_id;
    
    -- Se não conseguiu inserir, buscar categoria existente
    IF test_category_id IS NULL THEN
        SELECT id INTO test_category_id FROM categories WHERE club_id = test_club_id LIMIT 1;
    END IF;
    
    -- Criar alojamento de teste se não existir
    INSERT INTO accommodations (club_id, name, type, capacity)
    VALUES (test_club_id, 'Alojamento Teste', 'hotel', 10)
    ON CONFLICT DO NOTHING
    RETURNING id INTO test_accommodation_id;
    
    -- Se não conseguiu inserir, buscar alojamento existente
    IF test_accommodation_id IS NULL THEN
        SELECT id INTO test_accommodation_id FROM accommodations WHERE club_id = test_club_id LIMIT 1;
    END IF;
    
    -- Criar vinculações de teste
    
    -- 1. Categoria
    IF test_category_id IS NOT NULL THEN
        INSERT INTO player_categories (club_id, player_id, category_id)
        VALUES (test_club_id, test_player_id, test_category_id);
        RAISE NOTICE 'Jogador associado à categoria %', test_category_id;
    END IF;
    
    -- 2. Alojamento
    IF test_accommodation_id IS NOT NULL THEN
        INSERT INTO player_accommodations (
            club_id, player_id, accommodation_id, status, check_in_date
        ) VALUES (
            test_club_id, test_player_id, test_accommodation_id, 'active', CURRENT_DATE
        );
        RAISE NOTICE 'Jogador associado ao alojamento %', test_accommodation_id;
    END IF;
    
    -- 3. Salário
    INSERT INTO player_salaries (
        club_id, player_id, value, status, start_date
    ) VALUES (
        test_club_id, test_player_id, 5000.00, 'active', CURRENT_DATE
    );
    RAISE NOTICE 'Salário criado para o jogador';
    
    -- 4. Conta de usuário (simulada)
    INSERT INTO player_accounts (
        club_id, player_id, user_id
    ) VALUES (
        test_club_id, test_player_id, gen_random_uuid()
    );
    RAISE NOTICE 'Conta de usuário criada para o jogador';
    
    -- 5. Registro médico
    INSERT INTO medical_records (
        club_id, player_id, date, description, status
    ) VALUES (
        test_club_id, test_player_id::text, CURRENT_DATE, 'Exame de rotina', 'pending'
    );
    RAISE NOTICE 'Registro médico criado para o jogador';
    
    -- 6. Adiantamento de salário
    INSERT INTO salary_advances (
        club_id, person_id, person_type, amount, advance_date, month, year, status
    ) VALUES (
        test_club_id, test_player_id::text::integer, 'player', 1000.00, CURRENT_DATE, 
        EXTRACT(MONTH FROM CURRENT_DATE)::integer, EXTRACT(YEAR FROM CURRENT_DATE)::integer, 'active'
    );
    RAISE NOTICE 'Adiantamento de salário criado para o jogador';
    
    -- Verificar vinculações antes da inativação
    RAISE NOTICE '=== ANTES DA INATIVAÇÃO ===';
    RAISE NOTICE 'Categorias: %', (SELECT COUNT(*) FROM player_categories WHERE player_id = test_player_id);
    RAISE NOTICE 'Alojamentos ativos: %', (SELECT COUNT(*) FROM player_accommodations WHERE player_id = test_player_id AND status = 'active');
    RAISE NOTICE 'Salários ativos: %', (SELECT COUNT(*) FROM player_salaries WHERE player_id = test_player_id AND status = 'active');
    RAISE NOTICE 'Contas ativas: %', (SELECT COUNT(*) FROM player_accounts WHERE player_id = test_player_id AND (expires_at IS NULL OR expires_at > NOW()));
    RAISE NOTICE 'Registros médicos pendentes: %', (SELECT COUNT(*) FROM medical_records WHERE player_id = test_player_id::text AND status = 'pending');
    RAISE NOTICE 'Adiantamentos ativos: %', (SELECT COUNT(*) FROM salary_advances WHERE person_id::text = test_player_id::text AND status = 'active');
    
    -- ALTERAR STATUS PARA INATIVO (isso deve disparar o trigger)
    RAISE NOTICE '=== ALTERANDO STATUS PARA INATIVO ===';
    UPDATE players SET status = 'inativo' WHERE id = test_player_id;
    
    -- Verificar vinculações após a inativação
    RAISE NOTICE '=== APÓS A INATIVAÇÃO ===';
    RAISE NOTICE 'Categorias: %', (SELECT COUNT(*) FROM player_categories WHERE player_id = test_player_id);
    RAISE NOTICE 'Alojamentos ativos: %', (SELECT COUNT(*) FROM player_accommodations WHERE player_id = test_player_id AND status = 'active');
    RAISE NOTICE 'Alojamentos completed: %', (SELECT COUNT(*) FROM player_accommodations WHERE player_id = test_player_id AND status = 'completed');
    RAISE NOTICE 'Salários ativos: %', (SELECT COUNT(*) FROM player_salaries WHERE player_id = test_player_id AND status = 'active');
    RAISE NOTICE 'Salários inativos: %', (SELECT COUNT(*) FROM player_salaries WHERE player_id = test_player_id AND status = 'inactive');
    RAISE NOTICE 'Contas ativas: %', (SELECT COUNT(*) FROM player_accounts WHERE player_id = test_player_id AND (expires_at IS NULL OR expires_at > NOW()));
    RAISE NOTICE 'Contas expiradas: %', (SELECT COUNT(*) FROM player_accounts WHERE player_id = test_player_id AND expires_at <= NOW());
    RAISE NOTICE 'Registros médicos pendentes: %', (SELECT COUNT(*) FROM medical_records WHERE player_id = test_player_id::text AND status = 'pending');
    RAISE NOTICE 'Registros médicos inativos: %', (SELECT COUNT(*) FROM medical_records WHERE player_id = test_player_id::text AND status = 'inactive');
    RAISE NOTICE 'Adiantamentos ativos: %', (SELECT COUNT(*) FROM salary_advances WHERE person_id::text = test_player_id::text AND status = 'active');
    RAISE NOTICE 'Adiantamentos cancelados: %', (SELECT COUNT(*) FROM salary_advances WHERE person_id::text = test_player_id::text AND status = 'cancelled');
    
    -- Limpar dados de teste
    RAISE NOTICE '=== LIMPANDO DADOS DE TESTE ===';
    DELETE FROM salary_advances WHERE person_id::text = test_player_id::text;
    DELETE FROM medical_records WHERE player_id = test_player_id::text;
    DELETE FROM player_accounts WHERE player_id = test_player_id;
    DELETE FROM player_salaries WHERE player_id = test_player_id;
    DELETE FROM player_accommodations WHERE player_id = test_player_id;
    DELETE FROM player_categories WHERE player_id = test_player_id;
    DELETE FROM players WHERE id = test_player_id;
    
    RAISE NOTICE 'Dados de teste removidos com sucesso!';
    RAISE NOTICE 'Jogador de teste ID: % foi removido', test_player_id;
    
END $$;
