import React, { useState, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Upload, X, FileText, Check, AlertCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface DocumentUploadProps {
  value?: string;
  onChange: (value: string | null, file?: File) => void;
  className?: string;
  disabled?: boolean;
  maxSize?: number; // em MB
  status?: "pending" | "verified" | "rejected" | "missing";
  documentType: string;
  documentLabel: string;
  required?: boolean;
  onView?: () => void;
  actions?: React.ReactNode; // Botões de ação adicionais
}

export function DocumentUpload({
  value,
  onChange,
  className = "",
  disabled = false,
  maxSize = 5, // 5MB por padrão
  status = "missing",
  documentType,
  documentLabel,
  required = false,
  onView,
  actions,
}: DocumentUploadProps) {
  const [dragActive, setDragActive] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const validateFile = (file: File): boolean => {
    // Verificar tamanho
    const maxSizeBytes = maxSize * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      setError(`O documento deve ter no máximo ${maxSize}MB`);
      return false;
    }

    // Verificar tipo de arquivo
    const allowedTypes = [
      "application/pdf",
      "image/jpeg",
      "image/png",
      "image/jpg",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ];

    if (!allowedTypes.includes(file.type)) {
      setError("Formato de arquivo não suportado");
      return false;
    }

    setError(null);
    return true;
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];
      if (validateFile(file)) {
        handleFileChange(file);
      }
    }
  };

  const handleFileChange = (file: File) => {
    // Criar URL para preview
    const fileUrl = URL.createObjectURL(file);
    onChange(fileUrl, file);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      if (validateFile(file)) {
        handleFileChange(file);
      }
    }
  };

  const handleRemove = () => {
    onChange(null);
    if (inputRef.current) {
      inputRef.current.value = "";
    }
  };

  const getStatusBadge = () => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
            <AlertCircle className="h-3 w-3 mr-1" />
            Pendente
          </Badge>
        );
      case "verified":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
            <Check className="h-3 w-3 mr-1" />
            Verificado
          </Badge>
        );
      case "rejected":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">
            <X className="h-3 w-3 mr-1" />
            Rejeitado
          </Badge>
        );
      case "missing":
        return required ? (
          <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">
            <AlertCircle className="h-3 w-3 mr-1" />
            Obrigatório
          </Badge>
        ) : (
          <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200">
            Opcional
          </Badge>
        );
      default:
        return null;
    }
  };

  return (
    <div className={`flex flex-col ${className}`}>
      <div className="flex justify-between items-center mb-2">
        <span className="font-medium">{documentLabel}</span>
        {getStatusBadge()}
      </div>

      <div
        className={`relative border-2 ${
          dragActive ? "border-primary" : "border-dashed border-gray-300"
        } ${disabled ? "opacity-50" : ""} rounded-md overflow-hidden`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        {value ? (
          <div className={`flex items-center justify-between p-3 ${status === "rejected" ? "bg-red-50" : ""}`}>
            <div className="flex items-center">
              <FileText className={`h-5 w-5 mr-2 ${status === "rejected" ? "text-red-500" : "text-gray-500"}`} />
              <div>
                <span className="text-sm truncate max-w-[150px]">Documento enviado</span>
                {status === "rejected" && (
                  <div>
                    <span className="text-xs text-red-600 block">Documento rejeitado</span>
                    <span className="text-xs text-red-600 block font-medium">Clique para enviar novamente</span>
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center">
              {onView && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation(); // Impedir propagação do evento
                    onView();
                  }}
                  className="h-8 px-2 relative z-20" // Aumentar o z-index
                >
                  Ver
                </Button>
              )}
              {actions}

              {!disabled && status !== "verified" && !actions && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation(); // Impedir propagação do evento
                    handleRemove();
                  }}
                  className="h-8 px-2 text-red-500 hover:text-red-700 hover:bg-red-50 relative z-20"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center p-4 text-center">
            <FileText className="h-8 w-8 text-gray-400 mb-2" />
            <p className="text-sm text-gray-500">Enviar {documentLabel}</p>
            <p className="text-xs text-gray-400 mt-1">
              Arraste ou clique para selecionar
            </p>
          </div>
        )}

        <input
          ref={inputRef}
          type="file"
          accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
          onChange={handleInputChange}
          disabled={disabled || status === "verified"}
        />
      </div>

      {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
    </div>
  );
}
