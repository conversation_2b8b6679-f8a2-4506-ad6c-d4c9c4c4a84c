import { useState, useEffect, useCallback } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  RefreshCw,
  Calendar,
  CheckCircle,
  XCircle,
  Eye,
  Clock,
  FileText,
  FileCheck,
  AlertCircle,
  UserCheck,
  Edit,
  Trash2,
  Check,
  X
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import {
  getPlayersInEvaluation,
  schedulePlayerEvaluation,
  updatePlayerEvaluationStatus,
  updateDocumentsStatus,
  updateEvaluationStatus,
  deletePlayerEvaluationInvitation,
  PlayerEvaluationInvitation
} from "@/api/playerEvaluationInvitations";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import { calculateAge } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useUser } from "@/context/UserContext";
import { getPlayerDocuments, PlayerDocument, verifyDocument, DOCUMENT_LABELS } from "@/api/documents";
import { getSupabaseClientWithClubId } from "@/integrations/supabase/clientWithClubId";
import { parseISO } from "date-fns";

// Define player type
interface Player {
  id: string;
  name: string;
  birthdate?: string;
  position?: string;
  phone?: string;
  status: string;
  player_evaluation_invitations?: Array<{
    id: number;
    evaluation_date?: string;
    evaluation_location?: string;
    evaluation_requirements?: string;
    evaluation_notes?: string;
    documents_status?: "pending" | "approved" | "rejected";
    documents_verified_at?: string;
    documents_verified_by?: string;
    documents_rejection_reason?: string;
    evaluation_status?: "em avaliacao" | "aprovado" | "disponivel";
    created_by?: string;
    created_by_user?: {
      id: string;
      name: string;
    };
  }>;
}

// Schedule Evaluation Form Component
function ScheduleEvaluationForm({
  clubId,
  player,
  onSuccess,
  onCancel
}: {
  clubId: number;
  player: Player;
  onSuccess: () => void;
  onCancel: () => void;
}) {
  const { user } = useUser();
  const { toast } = useToast();

  const [date, setDate] = useState("");
  const [time, setTime] = useState("");
  const [location, setLocation] = useState("");
  const [requirements, setRequirements] = useState("Documento de identidade com foto, chuteira, meião e shorts.");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user?.id) {
      setError("Usuário não autenticado");
      return;
    }

    if (!date || !time || !location) {
      setError("Preencha todos os campos obrigatórios");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Combine date and time
      const dateTime = new Date(`${date}T${time}`);

      // Call the API to schedule the evaluation
      await schedulePlayerEvaluation(
        clubId,
        player.id,
        dateTime.toISOString(),
        location,
        requirements,
        user.id
      );

      onSuccess();
    } catch (err: unknown) {
      console.error("Erro ao agendar pré cadastro:", err);
      const errorMessage = err instanceof Error ? err.message : "Erro ao agendar pré cadastro";
      setError(errorMessage);
      toast({
        title: "Erro",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="playerName">Jogador</Label>
        <Input
          id="playerName"
          value={player.name}
          readOnly
          disabled
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="date">Data*</Label>
          <Input
            id="date"
            type="date"
            value={date}
            onChange={(e) => setDate(e.target.value)}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="time">Horário*</Label>
          <Input
            id="time"
            type="time"
            value={time}
            onChange={(e) => setTime(e.target.value)}
            required
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="location">Local*</Label>
        <Input
          id="location"
          value={location}
          onChange={(e) => setLocation(e.target.value)}
          placeholder="Endereço completo do local do pré cadastro"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="requirements">O que levar</Label>
        <Textarea
          id="requirements"
          value={requirements}
          onChange={(e) => setRequirements(e.target.value)}
          placeholder="Itens que o atleta deve levar para o pré cadastro"
          rows={3}
        />
      </div>

      {error && (
        <div className="text-sm font-medium text-destructive">{error}</div>
      )}

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancelar
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? "Agendando..." : "Agendar pré cadastro"}
        </Button>
      </div>
    </form>
  );
}

// Pre-Registration Document Viewer Component
function PreRegistrationDocumentViewer({
  clubId,
  player,
  onSuccess,
  onCancel
}: {
  clubId: number;
  player: Player;
  onSuccess: () => void;
  onCancel: () => void;
}) {
  const { user } = useUser();
  const { toast } = useToast();
  const [documents, setDocuments] = useState<PlayerDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedDocument, setSelectedDocument] = useState<PlayerDocument | null>(null);
  const [verifying, setVerifying] = useState(false);
  const [rejectionReason, setRejectionReason] = useState("");
  const [showRejectionForm, setShowRejectionForm] = useState(false);

  // Carregar documentos quando o componente for montado
  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        setLoading(true);
        const docs = await getPlayerDocuments(clubId, player.id);
        setDocuments(docs);
      } catch (err) {
        console.error("Erro ao buscar documentos:", err);
        toast({
          title: "Erro",
          description: "Erro ao carregar documentos",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDocuments();
  }, [clubId, player.id, toast]);

  // Função para verificar um documento
  const handleVerifyDocument = async (status: "verified" | "rejected") => {
    if (!selectedDocument) return;

    // Se for rejeição, mostrar formulário para informar o motivo
    if (status === "rejected" && !showRejectionForm) {
      setShowRejectionForm(true);
      return;
    }

    // Se for rejeição e o motivo não foi informado, exigir
    if (status === "rejected" && !rejectionReason.trim()) {
      toast({
        title: "Atenção",
        description: "Por favor, informe o motivo da rejeição para que o atleta possa corrigir o documento.",
        variant: "destructive",
      });
      return;
    }

    try {
      setVerifying(true);
      await verifyDocument(
        selectedDocument.id,
        user?.id || "",
        status,
        status === "rejected" ? rejectionReason : undefined
      );

      // Atualizar lista de documentos
      const updatedDocuments = documents.map((doc) =>
        doc.id === selectedDocument.id
          ? {
              ...doc,
              status,
              verified_at: new Date().toISOString(),
              verified_by: user?.id || "",
              rejection_reason: status === "rejected" ? rejectionReason : null
            }
          : doc
      );

      setDocuments(updatedDocuments);

      toast({
        title: "Sucesso",
        description: `Documento ${status === "verified" ? "verificado" : "rejeitado"} com sucesso`,
      });

      // Limpar o formulário e fechar
      setRejectionReason("");
      setShowRejectionForm(false);

      // Fechar visualização do documento
      setSelectedDocument(null);

      // Verificar se todos os documentos foram processados
      const allProcessed = updatedDocuments.every(doc => doc.status !== "pending");
      if (allProcessed) {
        onSuccess();
      }
    } catch (err: any) {
      console.error("Erro ao verificar documento:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao verificar documento",
        variant: "destructive",
      });
    } finally {
      setVerifying(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Documentos do Atleta - {player.name}</h3>
        <Button variant="outline" onClick={onCancel}>
          Fechar
        </Button>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      ) : documents.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          Nenhum documento encontrado
        </div>
      ) : selectedDocument ? (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">
              {DOCUMENT_LABELS[selectedDocument.document_type] || selectedDocument.document_type}
            </h3>
            <div className="flex space-x-2">
              {selectedDocument.status === "pending" && !showRejectionForm && (
                <>
                  <Button
                    variant="outline"
                    className="bg-green-50 text-green-700 border-green-200 hover:bg-green-100 hover:text-green-800"
                    onClick={() => handleVerifyDocument("verified")}
                    disabled={verifying}
                  >
                    <Check className="h-4 w-4 mr-1" />
                    Aprovar
                  </Button>
                  <Button
                    variant="outline"
                    className="bg-red-50 text-red-700 border-red-200 hover:bg-red-100 hover:text-red-800"
                    onClick={() => handleVerifyDocument("rejected")}
                    disabled={verifying}
                  >
                    <X className="h-4 w-4 mr-1" />
                    Rejeitar
                  </Button>
                </>
              )}

              {selectedDocument.status === "pending" && showRejectionForm && (
                <>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowRejectionForm(false);
                      setRejectionReason("");
                    }}
                    disabled={verifying}
                  >
                    Cancelar
                  </Button>
                  <Button
                    variant="outline"
                    className="bg-red-50 text-red-700 border-red-200 hover:bg-red-100 hover:text-red-800"
                    onClick={() => handleVerifyDocument("rejected")}
                    disabled={verifying}
                  >
                    <X className="h-4 w-4 mr-1" />
                    Confirmar Rejeição
                  </Button>
                </>
              )}
              <Button
                variant="outline"
                onClick={() => setSelectedDocument(null)}
              >
                Voltar
              </Button>
            </div>
          </div>

          {showRejectionForm && (
            <div className="mb-4 space-y-2">
              <Label htmlFor="rejection-reason">Motivo da Rejeição</Label>
              <Input
                id="rejection-reason"
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                placeholder="Informe o motivo da rejeição para que o atleta possa corrigir o documento"
                className="w-full"
              />
              <p className="text-xs text-gray-500">
                Esta mensagem será exibida para o atleta para que ele possa corrigir o documento.
              </p>
            </div>
          )}

          <div className="border rounded-md p-2 overflow-hidden" style={{ maxHeight: "60vh" }}>
            {selectedDocument.file_url.endsWith(".pdf") ? (
              <iframe
                src={`https://docs.google.com/viewer?url=${encodeURIComponent(selectedDocument.file_url)}&embedded=true`}
                className="w-full h-[60vh] border-0"
                title="Documento"
                allowFullScreen
              />
            ) : (
              <div className="flex justify-center" style={{ maxHeight: "58vh", overflow: "hidden" }}>
                <img
                  src={selectedDocument.file_url}
                  alt="Documento"
                  className="max-h-[58vh] object-contain"
                  onClick={(e) => e.preventDefault()}
                  style={{ pointerEvents: "none", display: "block" }}
                />
              </div>
            )}
          </div>

          <div className="text-sm space-y-2">
            <div className="flex items-center">
              <span className="font-medium mr-2">Status:</span>
              {selectedDocument.status === "pending" && (
                <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-md text-xs flex items-center">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  Pendente
                </span>
              )}
              {selectedDocument.status === "verified" && (
                <span className="bg-green-100 text-green-800 px-2 py-1 rounded-md text-xs flex items-center">
                  <Check className="h-3 w-3 mr-1" />
                  Aprovado
                </span>
              )}
              {selectedDocument.status === "rejected" && (
                <span className="bg-red-100 text-red-800 px-2 py-1 rounded-md text-xs flex items-center">
                  <X className="h-3 w-3 mr-1" />
                  Rejeitado
                </span>
              )}
            </div>

            <p className="text-gray-500">Enviado em: {new Date(selectedDocument.uploaded_at).toLocaleString()}</p>

            {selectedDocument.verified_at && (
              <p className="text-gray-500">Verificado em: {new Date(selectedDocument.verified_at).toLocaleString()}</p>
            )}

            {selectedDocument.status === "rejected" && (
              <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="font-medium text-red-700 text-sm">Motivo da Rejeição:</p>
                {selectedDocument.rejection_reason ? (
                  <p className="text-red-600 mt-1">{selectedDocument.rejection_reason}</p>
                ) : (
                  <p className="text-red-600 mt-1 italic">Nenhum motivo especificado</p>
                )}
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {documents.map((document) => (
            <div
              key={document.id}
              className="border rounded-md p-4 cursor-pointer hover:bg-gray-50"
              onClick={() => setSelectedDocument(document)}
            >
              <div className="flex justify-between items-start">
                <h3 className="font-medium">
                  {DOCUMENT_LABELS[document.document_type] || document.document_type}
                </h3>
                <div className="flex items-center">
                  {document.status === "pending" && (
                    <div className="flex items-center bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      Pendente
                    </div>
                  )}
                  {document.status === "verified" && (
                    <div className="flex items-center bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
                      <Check className="h-3 w-3 mr-1" />
                      Aprovado
                    </div>
                  )}
                  {document.status === "rejected" && (
                    <div className="flex items-center bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs">
                      <X className="h-3 w-3 mr-1" />
                      Rejeitado
                    </div>
                  )}
                </div>
              </div>
              <p className="text-sm text-gray-500 mt-2">
                Enviado em: {new Date(document.uploaded_at).toLocaleDateString()}
              </p>
              {document.status === "rejected" && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-xs font-medium text-red-700">Documento Rejeitado</p>
                  {document.rejection_reason ? (
                    <p className="text-xs text-red-600 mt-1">
                      <span className="font-medium">Motivo:</span> {document.rejection_reason}
                    </p>
                  ) : (
                    <p className="text-xs text-red-600 mt-1">Nenhum motivo especificado</p>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

// Player Edit Form Component
function PlayerEditForm({
  clubId,
  player,
  onSuccess,
  onCancel
}: {
  clubId: number;
  player: Player;
  onSuccess: () => void;
  onCancel: () => void;
}) {
  const { user } = useUser();
  const { toast } = useToast();

  const [name, setName] = useState(player.name || "");
  const [birthdate, setBirthdate] = useState(player.birthdate || "");
  const [position, setPosition] = useState(player.position || "");
  const [phone, setPhone] = useState(player.phone || "");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user?.id) {
      setError("Usuário não autenticado");
      return;
    }

    if (!name || !position) {
      setError("Nome e posição são obrigatórios");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Criar cliente Supabase com club_id nos headers
      const supabaseWithClubId = getSupabaseClientWithClubId(clubId);

      // Atualizar dados do jogador
      const { data, error: updateError } = await supabaseWithClubId
        .from("players")
        .update({
          name,
          birthdate,
          position,
          phone
        })
        .eq("id", player.id)
        .eq("club_id", clubId)
        .select()
        .single();

      if (updateError) {
        throw new Error(`Erro ao atualizar jogador: ${updateError.message}`);
      }

      toast({
        title: "Sucesso",
        description: "Jogador atualizado com sucesso",
      });

      onSuccess();
    } catch (err: unknown) {
      console.error("Erro ao atualizar jogador:", err);
      const errorMessage = err instanceof Error ? err.message : "Erro ao atualizar jogador";
      setError(errorMessage);
      toast({
        title: "Erro",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">Nome*</Label>
        <Input
          id="name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="birthdate">Data de Nascimento</Label>
        <Input
          id="birthdate"
          type="date"
          value={birthdate}
          onChange={(e) => setBirthdate(e.target.value)}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="position">Posição*</Label>
        <Select value={position} onValueChange={setPosition}>
          <SelectTrigger id="position">
            <SelectValue placeholder="Selecione a posição" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Goleiro">Goleiro</SelectItem>
            <SelectItem value="Lateral Direito">Lateral Direito</SelectItem>
            <SelectItem value="Zagueiro">Zagueiro</SelectItem>
            <SelectItem value="Lateral Esquerdo">Lateral Esquerdo</SelectItem>
            <SelectItem value="Volante">Volante</SelectItem>
            <SelectItem value="Meias">Meias</SelectItem>
            <SelectItem value="Extremo">Extremo</SelectItem>
            <SelectItem value="Atacante">Atacante</SelectItem>
            <SelectItem value="Centroavante">Centroavante</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="phone">Telefone</Label>
        <Input
          id="phone"
          value={phone}
          onChange={(e) => setPhone(e.target.value)}
          placeholder="(00) 00000-0000"
        />
      </div>

      {error && (
        <div className="text-sm font-medium text-destructive">{error}</div>
      )}

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancelar
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? "Salvando..." : "Salvar Alterações"}
        </Button>
      </div>
    </form>
  );
}

// Evaluation Status Update Form Component
function EvaluationStatusUpdateForm({
  clubId,
  player,
  onSuccess,
  onCancel
}: {
  clubId: number;
  player: Player;
  onSuccess: () => void;
  onCancel: () => void;
}) {
  const { user } = useUser();
  const { toast } = useToast();

  const invitation = player.player_evaluation_invitations?.[0];
  const currentStatus = invitation?.evaluation_status || "em avaliacao";

  const [status, setStatus] = useState<"em avaliacao" | "aprovado" | "disponivel">(
    currentStatus as "em avaliacao" | "aprovado" | "disponivel"
  );
  const [notes, setNotes] = useState(invitation?.evaluation_notes || "");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user?.id) {
      setError("Usuário não autenticado");
      return;
    }

    if (!invitation) {
      setError("Convite não encontrado");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // First update the evaluation notes if changed
      if (notes !== invitation.evaluation_notes) {
        await updatePlayerEvaluationStatus(
          clubId,
          player.id,
          player.status,
          notes,
          user.id
        );
      }

      // Then update the evaluation status
      if (status !== currentStatus) {
        await updateEvaluationStatus(
          clubId,
          invitation.id,
          status,
          user.id
        );
      }

      onSuccess();
    } catch (err: unknown) {
      console.error("Erro ao atualizar status:", err);
      const errorMessage = err instanceof Error ? err.message : "Erro ao atualizar status";
      setError(errorMessage);
      toast({
        title: "Erro",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <p className="font-medium">Jogador: {player.name}</p>
        <p className="text-sm text-muted-foreground">Posição: {player.position}</p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="status">Status da Pré cadastro</Label>
        <Select value={status} onValueChange={(value: "em avaliacao" | "aprovado" | "disponivel") => setStatus(value)}>
          <SelectTrigger id="status">
            <SelectValue placeholder="Selecione o status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="em avaliacao">Em Pré Cadastro</SelectItem>
            <SelectItem value="aprovado">Aprovado</SelectItem>
            <SelectItem value="disponivel">Disponível (Integrar ao Elenco)</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          {status === "em avaliacao" ? "O atleta está em processo de pré cadastro." :
           status === "aprovado" ? "O atleta foi aprovado no pré cadastro, mas ainda não está integrado ao elenco." :
           "O atleta será integrado ao elenco principal e aparecerá na página de Elenco."}
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="notes">Observações</Label>
        <Textarea
          id="notes"
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
          placeholder="Observações sobre o pré cadastro do atleta"
          rows={4}
        />
      </div>

      {error && (
        <div className="text-sm font-medium text-destructive">{error}</div>
      )}

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancelar
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? "Salvando..." : "Salvar Status"}
        </Button>
      </div>
    </form>
  );
}

export function PlayersInEvaluationTable() {
  const clubId = useCurrentClubId();
  const { toast } = useToast();
  const { user } = useUser();

  const [players, setPlayers] = useState<Player[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deletingPlayerId, setDeletingPlayerId] = useState<string | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // Dialog states
  const [scheduleDialogOpen, setScheduleDialogOpen] = useState(false);
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [documentsDialogOpen, setDocumentsDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);

  // Fetch players in evaluation
  const fetchPlayers = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const data = await getPlayersInEvaluation(clubId);
      setPlayers(data);
    } catch (err: unknown) {
      console.error("Erro ao buscar jogadores em pré cadastro:", err);
      const errorMessage = err instanceof Error ? err.message : "Erro ao buscar jogadores em pré cadastro";
      setError(errorMessage);
      toast({
        title: "Erro",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [clubId, toast]);

  // Load players on mount
  useEffect(() => {
    fetchPlayers();
  }, [clubId, fetchPlayers]);

  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "-";

    try {
      // Use parseISO to avoid timezone issues with date strings in YYYY-MM-DD format
      const date = parseISO(dateString);
      return date.toLocaleString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.error("Error formatting date:", error);
      return dateString;
    }
  };

  // Handle schedule button click
  const handleScheduleClick = (player: Player) => {
    setSelectedPlayer(player);
    setScheduleDialogOpen(true);
  };

  // Handle status update button click
  const handleStatusUpdateClick = (player: Player) => {
    setSelectedPlayer(player);
    setStatusDialogOpen(true);
  };

  // Handle document approval button click
  const handleDocumentApprovalClick = (player: Player) => {
    setSelectedPlayer(player);
    setDocumentsDialogOpen(true);
  };

  // Handle schedule success
  const handleScheduleSuccess = () => {
    setScheduleDialogOpen(false);
    fetchPlayers();
    toast({
      title: "Sucesso",
      description: "Pré cadastro agendada com sucesso",
    });
  };

  // Handle status update success
  const handleStatusUpdateSuccess = () => {
    setStatusDialogOpen(false);
    fetchPlayers();
    toast({
      title: "Sucesso",
      description: "Status atualizado com sucesso",
    });
  };

  // Handle document approval success
  const handleDocumentApprovalSuccess = () => {
    setDocumentsDialogOpen(false);
    fetchPlayers();
    toast({
      title: "Sucesso",
      description: "Status dos documentos atualizado com sucesso",
    });
  };

  // Handle edit success
  const handleEditSuccess = () => {
    setEditDialogOpen(false);
    fetchPlayers();
    toast({
      title: "Sucesso",
      description: "Jogador atualizado com sucesso",
    });
  };

  // Handle edit button click
  const handleEditClick = (player: Player) => {
    setSelectedPlayer(player);
    setEditDialogOpen(true);
  };

  // Handle delete button click
  const handleDeleteClick = (player: Player) => {
    setSelectedPlayer(player);
    setDeletingPlayerId(player.id);
    setDeleteConfirmOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (!selectedPlayer || !user?.id) return;

    const invitation = selectedPlayer.player_evaluation_invitations?.[0];
    if (!invitation) return;

    try {
      setDeleteLoading(true);

      await deletePlayerEvaluationInvitation(
        clubId,
        invitation.id,
        user.id
      );

      setDeleteConfirmOpen(false);
      fetchPlayers();
      toast({
        title: "Sucesso",
        description: "Jogador removido com sucesso",
      });
    } catch (err: unknown) {
      console.error("Erro ao excluir jogador:", err);
      const errorMessage = err instanceof Error ? err.message : "Erro ao excluir jogador";
      toast({
        title: "Erro",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setDeleteLoading(false);
      setDeletingPlayerId(null);
    }
  };

  // Get evaluation status
  const getEvaluationStatus = (player: Player) => {
    const invitation = player.player_evaluation_invitations?.[0];

    if (!invitation) {
      return <Badge className="bg-gray-500">Sem Convite</Badge>;
    }

    // Check evaluation status first
    if (invitation.evaluation_status) {
      if (invitation.evaluation_status === "disponivel") {
        return <Badge className="bg-green-500">Disponível</Badge>;
      } else if (invitation.evaluation_status === "aprovado") {
        return <Badge className="bg-emerald-500">Aprovado</Badge>;
      }
    }

    // Check document status
    if (invitation.documents_status) {
      if (invitation.documents_status === "rejected") {
        return <Badge className="bg-orange-500">Documentos Rejeitados</Badge>;
      } else if (invitation.documents_status === "approved") {
        // If documents are approved but no evaluation date
        if (!invitation.evaluation_date) {
          return <Badge className="bg-red-500">Aguardando Agendamento</Badge>;
        }
      } else {
        return <Badge className="bg-yellow-500">Documentos Pendentes</Badge>;
      }
    }

    // Check evaluation date
    if (invitation.evaluation_date) {
      const evaluationDate = new Date(invitation.evaluation_date);
      const now = new Date();

      if (evaluationDate > now) {
        return <Badge className="bg-blue-500">Agendado</Badge>;
      } else {
        return <Badge className="bg-purple-500">Pré Cadastro Realizado</Badge>;
      }
    }

    return <Badge className="bg-yellow-500">Documentos Pendentes</Badge>;
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Jogadores em pré cadastro</h2>
        <Button
          variant="outline"
          size="sm"
          onClick={fetchPlayers}
          disabled={loading}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Atualizar
        </Button>
      </div>

      {error && (
        <div className="p-4 bg-red-50 text-red-800 rounded-md">
          {error}
        </div>
      )}

      {loading ? (
        <div className="flex justify-center p-8">
          <RefreshCw className="h-6 w-6 animate-spin" />
        </div>
      ) : players.length === 0 ? (
        <div className="text-center p-8 text-muted-foreground">
          Nenhum jogador em pré cadastro encontrado.
        </div>
      ) : (
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Nome</TableHead>
                <TableHead>Data de Nascimento</TableHead>
                <TableHead>Idade</TableHead>
                <TableHead>Posição</TableHead>
                <TableHead>Telefone</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Data do Pré Cadastro</TableHead>
                <TableHead>Convidado por</TableHead>
                <TableHead className="text-right">Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {players.map((player) => (
                <TableRow key={player.id}>
                  <TableCell className="font-medium">{player.name}</TableCell>
                  <TableCell>
                    {player.birthdate ? new Date(player.birthdate).toLocaleDateString('pt-BR') : "-"}
                  </TableCell>
                  <TableCell>
                    {player.birthdate ? calculateAge(player.birthdate) : "-"}
                  </TableCell>
                  <TableCell>{player.position}</TableCell>
                  <TableCell>{player.phone || "-"}</TableCell>
                  <TableCell>{getEvaluationStatus(player)}</TableCell>
                  <TableCell>
                    {formatDate(player.player_evaluation_invitations?.[0]?.evaluation_date)}
                  </TableCell>
                  <TableCell>
                    {player.player_evaluation_invitations?.[0]?.created_by_user?.name || "-"}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => window.location.href = `/jogador/${player.id}`}
                        title="Ver perfil"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>

                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDocumentApprovalClick(player)}
                        title="Verificar documentos"
                      >
                        <FileCheck className="h-4 w-4" />
                      </Button>

                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleScheduleClick(player)}
                        title="Agendar pré cadastro"
                      >
                        <Calendar className="h-4 w-4" />
                      </Button>

                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleStatusUpdateClick(player)}
                        title="Atualizar status"
                      >
                        <UserCheck className="h-4 w-4" />
                      </Button>

                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEditClick(player)}
                        title="Editar jogador"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>

                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDeleteClick(player)}
                        title="Excluir jogador"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Schedule Dialog */}
      <Dialog open={scheduleDialogOpen} onOpenChange={setScheduleDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Agendar Pré Cadastro</DialogTitle>
          </DialogHeader>

          {selectedPlayer && (
            <ScheduleEvaluationForm
              clubId={clubId}
              player={selectedPlayer}
              onSuccess={handleScheduleSuccess}
              onCancel={() => setScheduleDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Status Update Dialog */}
      <Dialog open={statusDialogOpen} onOpenChange={setStatusDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Atualizar Status</DialogTitle>
          </DialogHeader>

          {selectedPlayer && (
            <EvaluationStatusUpdateForm
              clubId={clubId}
              player={selectedPlayer}
              onSuccess={handleStatusUpdateSuccess}
              onCancel={() => setStatusDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Document Approval Dialog */}
      <Dialog open={documentsDialogOpen} onOpenChange={setDocumentsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Verificar Documentos</DialogTitle>
          </DialogHeader>

          {selectedPlayer && (
            <PreRegistrationDocumentViewer
              clubId={clubId}
              player={selectedPlayer}
              onSuccess={handleDocumentApprovalSuccess}
              onCancel={() => setDocumentsDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={deleteConfirmOpen}
        onOpenChange={setDeleteConfirmOpen}
        title="Excluir Jogador"
        description={`Tem certeza que deseja excluir o jogador ${selectedPlayer?.name}? Esta ação não pode ser desfeita.`}
        confirmText="Excluir"
        cancelText="Cancelar"
        onConfirm={handleDeleteConfirm}
        loading={deleteLoading}
      />

      {/* Edit Player Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar Jogador</DialogTitle>
          </DialogHeader>

          {selectedPlayer && (
            <PlayerEditForm
              clubId={clubId}
              player={selectedPlayer}
              onSuccess={handleEditSuccess}
              onCancel={() => setEditDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
