import { create } from "zustand";
import {
  MedicalAppointment,
  getMedicalAppointments,
  createMedicalAppointment,
  updateMedicalAppointment,
  deleteMedicalAppointment,
  updateAppointmentStatus,
  AppointmentStatus
} from "@/api/api";

interface MedicalAppointmentsState {
  appointments: MedicalAppointment[];
  loading: boolean;
  error: string | null;
  fetchAppointments: (
    clubId: number,
    filters?: {
      professionalId?: number;
      playerId?: string;
      startDate?: string;
      endDate?: string;
      status?: AppointmentStatus;
    }
  ) => Promise<void>;
  addAppointment: (
    clubId: number,
    userId: string,
    appointment: Omit<MedicalAppointment, "id" | "club_id" | "created_at" | "updated_at" | "notification_sent">
  ) => Promise<void>;
  updateAppointment: (
    clubId: number,
    userId: string,
    id: number,
    appointment: Partial<Omit<MedicalAppointment, "id" | "club_id" | "created_at" | "updated_at">>
  ) => Promise<void>;
  deleteAppointment: (clubId: number, userId: string, id: number) => Promise<void>;
  updateStatus: (clubId: number, userId: string, id: number, status: AppointmentStatus) => Promise<void>;
}

export const useMedicalAppointmentsStore = create<MedicalAppointmentsState>((set) => ({
  appointments: [],
  loading: false,
  error: null,

  fetchAppointments: async (clubId, filters) => {
    set({ loading: true, error: null });
    try {
      const appointments = await getMedicalAppointments(clubId, filters);
      set({ appointments, loading: false });
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : "Erro ao buscar agendamentos",
        loading: false,
      });
    }
  },

  addAppointment: async (clubId, userId, appointment) => {
    set({ loading: true, error: null });
    try {
      const newAppointment = await createMedicalAppointment(clubId, userId, appointment);
      set((state) => ({
        appointments: [...state.appointments, newAppointment],
        loading: false,
      }));
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : "Erro ao adicionar agendamento",
        loading: false,
      });
    }
  },

  updateAppointment: async (clubId, userId, id, appointment) => {
    set({ loading: true, error: null });
    try {
      const updated = await updateMedicalAppointment(clubId, userId, id, appointment);
      set((state) => ({
        appointments: state.appointments.map((a) => (a.id === id ? updated : a)),
        loading: false,
      }));
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : "Erro ao atualizar agendamento",
        loading: false,
      });
    }
  },

  deleteAppointment: async (clubId, userId, id) => {
    set({ loading: true, error: null });
    try {
      await deleteMedicalAppointment(clubId, userId, id);
      set((state) => ({
        appointments: state.appointments.filter((a) => a.id !== id),
        loading: false,
      }));
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : "Erro ao excluir agendamento",
        loading: false,
      });
    }
  },

  updateStatus: async (clubId, userId, id, status) => {
    set({ loading: true, error: null });
    try {
      const updated = await updateAppointmentStatus(clubId, userId, id, status);
      set((state) => ({
        appointments: state.appointments.map((a) => (a.id === id ? updated : a)),
        loading: false,
      }));
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : "Erro ao atualizar status do agendamento",
        loading: false,
      });
    }
  },
}));
