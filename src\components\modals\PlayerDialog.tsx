import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/components/ui/use-toast";
import { useState, useEffect } from "react";
import type { Player, Accommodation, HotelRoom } from "@/api/api";
import { usePlayersStore } from "@/store/usePlayersStore";
import { useCategoriesStore } from "@/store/useCategoriesStore";
import { differenceInYears, parseISO } from "date-fns";
import { getAddressByCEP, formatCEP, formatCPF, validateCPF } from "@/api/external";
import { Checkbox } from "@/components/ui/checkbox";

interface PlayerDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  player?: Player;
  editMode?: boolean;
  clubId: number;
}

const POSITIONS = [
  "Goleiro",
  "Zagueiro",
  "Lateral",
  "Volante",
  "Meias",
  "Extremo",
  "Centroavante",
  "Atacante",
  "Outro"
];

const STATES = [
  "AC", "AL", "AP", "AM", "BA", "CE", "DF", "ES", "GO", "MA", "MT", "MS", "MG",
  "PA", "PB", "PR", "PE", "PI", "RJ", "RN", "RS", "RO", "RR", "SC", "SP", "SE", "TO"
];

export function PlayerDialog({ open, onOpenChange, player, editMode, clubId }: PlayerDialogProps) {
  // Dados básicos
  const [name, setName] = useState("");
  const [birthdate, setBirthdate] = useState("");
  const [position, setPosition] = useState("");
  const [height, setHeight] = useState("");
  const [weight, setWeight] = useState("");
  const [nationality, setNationality] = useState("");
  const [number, setNumber] = useState("");
  const [registrationNumber, setRegistrationNumber] = useState("");
  const [status, setStatus] = useState("disponivel");
  const [nickname, setNickname] = useState("");

  // Dados de categoria
  const [entryDate, setEntryDate] = useState("");
  const [exitDate, setExitDate] = useState("");
  const [championshipRegistration, setChampionshipRegistration] = useState("");
  const [professionalStatus, setProfessionalStatus] = useState("amador");
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>("");
  const [contractEndDate, setContractEndDate] = useState("");
  const [observation, setObservation] = useState("");

  // Dados de empréstimo
  const [loanEndDate, setLoanEndDate] = useState("");
  const [loanClubName, setLoanClubName] = useState("");

  // Dados de documentos
  const [rgNumber, setRgNumber] = useState("");
  const [cpfNumber, setCpfNumber] = useState("");
  const [fatherName, setFatherName] = useState("");
  const [motherName, setMotherName] = useState("");
  const [referredBy, setReferredBy] = useState("");

  // Dados de contato
  const [phone, setPhone] = useState("");
  const [address, setAddress] = useState("");
  const [zipCode, setZipCode] = useState("");
  const [city, setCity] = useState("");
  const [state, setState] = useState("");
  const [email, setEmail] = useState("");

  // Tab ativa
  const [activeTab, setActiveTab] = useState("basic");

  // Categorias
  const { categories, fetchCategories } = useCategoriesStore();

  // Dados de alojamento
  const [needsAccommodation, setNeedsAccommodation] = useState(false);
  const [accommodations, setAccommodations] = useState<Accommodation[]>([]);
  const [selectedAccommodationId, setSelectedAccommodationId] = useState<string>("");
  const [hotelRooms, setHotelRooms] = useState<HotelRoom[]>([]);
  const [selectedRoomId, setSelectedRoomId] = useState<string>("");
  const [loadingAccommodations, setLoadingAccommodations] = useState(false);

  // Atualiza os campos quando o jogador muda ou o modal é aberto
  useEffect(() => {
    if (player) {
      // Dados básicos
      setName(player.name || "");
      setBirthdate(player.birthdate || "");
      setPosition(player.position || "");
      setHeight(player.height?.toString() || "");
      setWeight(player.weight?.toString() || "");
      setNationality(player.nationality || "");
      setNumber(player.number?.toString() || "");
      setRegistrationNumber(player.registration_number || "");
      setStatus(player.status || "disponivel");
      setNickname(player.nickname || "");

      // Dados de categoria
      setEntryDate(player.entry_date || "");
      setExitDate(player.exit_date || "");
      setChampionshipRegistration(player.championship_registration || "");
      setProfessionalStatus(player.professional_status || "amador");
      setContractEndDate(player.contract_end_date || "");
      setObservation(player.observation || "");

      // Dados de empréstimo
      setLoanEndDate(player.loan_end_date || "");
      setLoanClubName(player.loan_club_name || "");

      // Dados de documentos
      setRgNumber(player.rg_number || "");
      setCpfNumber(player.cpf_number || "");
      setFatherName(player.father_name || "");
      setMotherName(player.mother_name || "");
      setReferredBy(player.referred_by || "");

      // Dados de contato
      setPhone(player.phone || "");
      setAddress(player.address || "");
      setZipCode(player.zip_code || "");
      setCity(player.city || "");
      setState(player.state || "");
      setEmail(player.email || "");

      // Dados de alojamento
      setNeedsAccommodation(player.is_accommodated || false);
      if (player.accommodation_id) {
        setSelectedAccommodationId(player.accommodation_id.toString());
      }

      // Buscar categoria do jogador
      const fetchPlayerCategory = async () => {
        try {
          const { getPlayerCategories } = await import('@/api/categories');
          const playerCategories = await getPlayerCategories(clubId, player.id);
          console.log("Categorias do jogador:", playerCategories);
          if (playerCategories && playerCategories.length > 0) {
            // A função getPlayerCategories retorna um array de categorias, então pegamos o ID da primeira categoria
            setSelectedCategoryId(playerCategories[0].id.toString());
          }
        } catch (err) {
          console.error("Erro ao buscar categoria do jogador:", err);
        }
      };

      fetchPlayerCategory();
    } else {
      // Limpa os campos quando estiver adicionando um novo jogador
      // Dados básicos
      setName("");
      setBirthdate("");
      setPosition("");
      setHeight("");
      setWeight("");
      setNationality("");
      setNumber("");
      setStatus("disponivel");
      setNickname("");

      // Dados de categoria
      setEntryDate(new Date().toISOString().split("T")[0]);
      setExitDate("");
      setChampionshipRegistration("");
      setProfessionalStatus("amador");
      setSelectedCategoryId("");
      setContractEndDate("");
      setObservation("");

      // Dados de empréstimo
      setLoanEndDate("");
      setLoanClubName("");

      // Dados de documentos
      setRgNumber("");
      setCpfNumber("");
      setFatherName("");
      setMotherName("");
      setReferredBy("");

      // Dados de contato
      setPhone("");
      setAddress("");
      setZipCode("");
      setCity("");
      setState("");
      setEmail("");

      // Dados de alojamento
      setNeedsAccommodation(false);
      setSelectedAccommodationId("");
      setSelectedRoomId("");
    }
  }, [player, open, clubId]);

  // Carregar categorias
  useEffect(() => {
    fetchCategories(clubId);
  }, [clubId, fetchCategories]);

  // Carregar alojamentos
  useEffect(() => {
    const fetchAccommodations = async () => {
      try {
        setLoadingAccommodations(true);
        const { getAccommodations } = await import('@/api/api');
        const accommodationsData = await getAccommodations(clubId);
        setAccommodations(accommodationsData);
      } catch (err) {
        console.error("Erro ao carregar alojamentos:", err);
        toast({
          title: "Erro",
          description: "Não foi possível carregar os alojamentos",
          variant: "destructive"
        });
      } finally {
        setLoadingAccommodations(false);
      }
    };

    if (open) {
      fetchAccommodations();
    }
  }, [clubId, open]);

  // Carregar quartos quando um alojamento do tipo hotel for selecionado
  useEffect(() => {
    const fetchRooms = async () => {
      if (!selectedAccommodationId) {
        setHotelRooms([]);
        return;
      }

      const selectedAccommodation = accommodations.find(
        acc => acc.id.toString() === selectedAccommodationId
      );

      if (selectedAccommodation?.type !== 'hotel') {
        setHotelRooms([]);
        return;
      }

      try {
        setLoadingAccommodations(true);
        const { getHotelRooms } = await import('@/api/api');
        const roomsData = await getHotelRooms(clubId, parseInt(selectedAccommodationId));
        setHotelRooms(roomsData);
      } catch (err) {
        console.error("Erro ao carregar quartos:", err);
        toast({
          title: "Erro",
          description: "Não foi possível carregar os quartos do hotel",
          variant: "destructive"
        });
      } finally {
        setLoadingAccommodations(false);
      }
    };

    fetchRooms();
  }, [clubId, selectedAccommodationId, accommodations]);

  const [error, setError] = useState("");
  const [loadingCEP, setLoadingCEP] = useState(false);
  const { addPlayer: addPlayerToStore, updatePlayer: updatePlayerInStore, loading: loadingPlayer } = usePlayersStore();

  // Função para buscar endereço pelo CEP
  const handleCEPSearch = async () => {
    if (!zipCode || zipCode.length < 8) {
      return;
    }

    try {
      setLoadingCEP(true);
      const addressData = await getAddressByCEP(zipCode);

      // Preencher campos de endereço
      setAddress(addressData.logradouro);
      setCity(addressData.localidade);
      setState(addressData.uf);

      // Formatar CEP
      setZipCode(formatCEP(addressData.cep));
    } catch (err: any) {
      toast({
        title: "Erro",
        description: err.message || "Erro ao buscar endereço pelo CEP",
        variant: "destructive",
      });
    } finally {
      setLoadingCEP(false);
    }
  };

  const handleSave = async () => {
    // Validar campos obrigatórios
    if (!name.trim()) {
      setError("Nome é obrigatório.");
      setActiveTab("basic");
      return;
    }
    if (!position.trim()) {
      setError("Posição é obrigatória.");
      setActiveTab("basic");
      return;
    }
    if (!birthdate) {
      setError("Data de nascimento é obrigatória.");
      setActiveTab("basic");
      return;
    }
    if (!nickname) {
      setError("Apelido é obrigatório.");
      setActiveTab("basic");
      return;
    }
    if (!entryDate) {
      setError("Data de entrada é obrigatória.");
      setActiveTab("category");
      return;
    }
    if (!professionalStatus) {
      setError("Status profissional/amador é obrigatório.");
      setActiveTab("category");
      return;
    }
    if (!selectedCategoryId) {
      setError("Categoria é obrigatória.");
      setActiveTab("category");
      return;
    }
    if (!cpfNumber) {
      setError("CPF é obrigatório.");
      setActiveTab("documents");
      return;
    }

    // Validar CPF
    const cleanCPF = cpfNumber.replace(/\D/g, "");
    if (cleanCPF.length !== 11) {
      setError("CPF deve conter 11 dígitos.");
      setActiveTab("documents");
      return;
    }
    if (!phone) {
      setError("Telefone é obrigatório.");
      setActiveTab("contact");
      return;
    }
    if (!height) {
      setError("Altura é obrigatória.");
      setActiveTab("basic");
      return;
    }
    if (!weight) {
      setError("Peso é obrigatório.");
      setActiveTab("basic");
      return;
    }

    // Validar campos de empréstimo quando o status é "emprestado"
    if (status === "emprestado") {
      if (!loanClubName.trim()) {
        setError("Nome do clube de destino é obrigatório para jogadores emprestados.");
        setActiveTab("category");
        return;
      }
      if (!loanEndDate) {
        setError("Data de fim do empréstimo é obrigatória para jogadores emprestados.");
        setActiveTab("category");
        return;
      }
    }

    setError("");

    // Não precisamos mais calcular a idade aqui, pois já está sendo calculada no playerData

    const playerData = {
      // Dados básicos
      name,
      birthdate,
      position: position || "Outro", // Garantir que position nunca seja vazio
      height: height ? parseFloat(height) : undefined,
      weight: weight ? parseFloat(weight) : undefined,
      nationality,
      number: number ? parseInt(number) : 0, // Garantir que number seja um número válido
      registration_number: registrationNumber, // Número de cadastro único
      status: status || "disponivel", // Garantir que status nunca seja vazio
      nickname,

      // Dados de categoria
      entry_date: entryDate,
      exit_date: exitDate || undefined,
      championship_registration: championshipRegistration || undefined,
      professional_status: professionalStatus,
      contract_end_date: contractEndDate || undefined,
      observation: observation || undefined,

      // Dados de empréstimo
      loan_end_date: status === "emprestado" ? loanEndDate : null,
      loan_club_name: status === "emprestado" ? loanClubName : null,

      // Dados de documentos
      rg_number: rgNumber || undefined,
      cpf_number: cpfNumber ? cpfNumber.replace(/\D/g, "") : undefined, // Remover formatação do CPF
      father_name: fatherName || undefined,
      mother_name: motherName || undefined,
      referred_by: referredBy || undefined,

      // Dados de contato
      phone: phone || undefined,
      address: address || undefined,
      zip_code: zipCode || undefined,
      city: city || undefined,
      state: state || undefined,
      email: email || undefined,

      // Dados de alojamento
      is_accommodated: needsAccommodation,
      accommodation_id: needsAccommodation && selectedAccommodationId ? parseInt(selectedAccommodationId) : undefined,

      // Outros dados
      club_id: clubId,
      age: birthdate ? differenceInYears(new Date(), parseISO(birthdate)) : undefined,
      stats: player?.stats || {
        games: 0,
        goals: 0,
        assists: 0,
        yellowCards: 0,
        redCards: 0,
        minutes: 0,
      },
    };

    try {
      // Log para depuração
      console.log("Dados do jogador a serem enviados:", playerData);

      // Usar o createPlayer e updatePlayer diretamente da API em vez do store
      let savedPlayer: Player | null = null;

      if (editMode && player?.id) {
        // Atualizar jogador existente
        console.log("Atualizando jogador existente:", {
          clubId,
          playerId: player.id,
          playerName: playerData.name
        });

        const { updatePlayer } = await import('@/api/api');
        savedPlayer = await updatePlayer(clubId, player.id, playerData);
        console.log("Jogador atualizado com sucesso:", {
          id: savedPlayer.id,
          name: savedPlayer.name
        });

        // Também atualizar o store
        await updatePlayerInStore(clubId, player.id, playerData);
      } else {
        // Criar novo jogador
        console.log("Criando novo jogador:", {
          clubId,
          playerName: playerData.name
        });

        const { createPlayer } = await import('@/api/api');
        savedPlayer = await createPlayer(clubId, playerData);
        console.log("Jogador criado com sucesso:", {
          id: savedPlayer.id,
          name: savedPlayer.name,
          registrationNumber: savedPlayer.registration_number
        });

        // Atualizar o store manualmente com o jogador já criado
        // Isso evita uma segunda chamada para createPlayer que estava ocorrendo em addPlayerToStore
        const { usePlayersStore } = await import('@/store/usePlayersStore');
        usePlayersStore.setState((state) => ({
          players: [...state.players, savedPlayer]
        }));
      }

      // Se uma categoria foi selecionada, associar o jogador a ela
      if (selectedCategoryId && savedPlayer) {
        try {
          // Usar a função diretamente em vez de fazer uma chamada fetch para a API
          const { assignPlayerToCategory } = await import('@/api/api');
          await assignPlayerToCategory(clubId, savedPlayer.id, parseInt(selectedCategoryId));
          console.log("Jogador associado à categoria com sucesso:", {
            clubId,
            playerId: savedPlayer.id,
            categoryId: parseInt(selectedCategoryId)
          });
        } catch (err) {
          console.error("Erro ao associar jogador à categoria:", err);
          toast({
            title: "Aviso",
            description: "Jogador salvo, mas houve um erro ao associá-lo à categoria selecionada.",
            variant: "destructive"
          });
        }
      }

      // Se o jogador precisa de alojamento e um quarto de hotel foi selecionado, associar o jogador ao quarto
      if (needsAccommodation && selectedAccommodationId && savedPlayer) {
        try {
          const selectedAccommodation = accommodations.find(
            acc => acc.id.toString() === selectedAccommodationId
          );

          if (selectedAccommodation?.type === 'hotel' && selectedRoomId) {
            // Associar jogador ao quarto de hotel
            const { assignPlayerToAccommodation } = await import('@/api/api');
            await assignPlayerToAccommodation(
              clubId,
              savedPlayer.id,
              parseInt(selectedAccommodationId),
              {
                hotel_room_id: parseInt(selectedRoomId),
                check_in_date: new Date().toISOString().split('T')[0],
                status: 'active'
              }
            );
            console.log("Jogador associado ao quarto de hotel com sucesso:", {
              clubId,
              playerId: savedPlayer.id,
              accommodationId: parseInt(selectedAccommodationId),
              roomId: parseInt(selectedRoomId)
            });
          } else if (selectedAccommodation) {
            // Associar jogador ao alojamento (não hotel)
            const { assignPlayerToAccommodation } = await import('@/api/api');
            await assignPlayerToAccommodation(
              clubId,
              savedPlayer.id,
              parseInt(selectedAccommodationId),
              {
                check_in_date: new Date().toISOString().split('T')[0],
                status: 'active'
              }
            );
            console.log("Jogador associado ao alojamento com sucesso:", {
              clubId,
              playerId: savedPlayer.id,
              accommodationId: parseInt(selectedAccommodationId)
            });
          }
        } catch (err) {
          console.error("Erro ao associar jogador ao alojamento:", err);
          toast({
            title: "Aviso",
            description: "Jogador salvo, mas houve um erro ao associá-lo ao alojamento selecionado.",
            variant: "destructive"
          });
        }
      }

      onOpenChange(false);
    } catch (err: unknown) {
      const error = err as Error;
      setError(error.message || "Erro ao salvar jogador");
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[95vh] flex flex-col">
        <DialogHeader className="pb-4 border-b flex-shrink-0">
          <DialogTitle className="text-xl font-semibold flex items-center gap-2">
            {editMode ? (
              <>
                <span className="w-2 h-2 bg-primary rounded-full"></span>
                Editar Jogador
              </>
            ) : (
              <>
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                Adicionar Jogador
              </>
            )}
          </DialogTitle>
          {error && (
            <div className="mt-2 p-3 bg-destructive/10 border border-destructive/20 rounded-md">
              <p className="text-sm text-destructive font-medium">{error}</p>
            </div>
          )}
        </DialogHeader>

        <div className="flex-1 overflow-y-auto min-h-0">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="grid grid-cols-5 mb-4 bg-muted/50 flex-shrink-0">
              <TabsTrigger value="basic" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
                Dados Básicos
              </TabsTrigger>
              <TabsTrigger value="category" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
                Contrato
              </TabsTrigger>
              <TabsTrigger value="documents" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
                Documentos
              </TabsTrigger>
              <TabsTrigger value="contact" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
                Contato
              </TabsTrigger>
              <TabsTrigger value="accommodation" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
                Alojamento
              </TabsTrigger>
            </TabsList>

            <div className="flex-1 overflow-y-auto">
              {/* Tab de Dados Básicos */}
              <TabsContent value="basic" className="space-y-4 p-1">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Nome*</Label>
                <Input
                  id="name"
                  placeholder="Nome completo"
                  value={name}
                  onChange={e => setName(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="nickname">Apelido*</Label>
                <Input
                  id="nickname"
                  placeholder="Apelido"
                  value={nickname}
                  onChange={e => setNickname(e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="position">Posição*</Label>
                <Select value={position} onValueChange={setPosition}>
                  <SelectTrigger id="position">
                    <SelectValue placeholder="Selecione a posição" />
                  </SelectTrigger>
                  <SelectContent>
                    {POSITIONS.map(pos => (
                      <SelectItem key={pos} value={pos}>{pos}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="birthdate">Data de Nascimento*</Label>
                <Input
                  id="birthdate"
                  type="date"
                  value={birthdate}
                  onChange={e => setBirthdate(e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="height">Altura (cm)*</Label>
                <Input
                  id="height"
                  placeholder="Altura em cm"
                  value={height}
                  onChange={e => setHeight(e.target.value)}
                  type="number"
                  min={120}
                  max={230}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="weight">Peso (kg)*</Label>
                <Input
                  id="weight"
                  placeholder="Peso em kg"
                  value={weight}
                  onChange={e => setWeight(e.target.value)}
                  type="number"
                  min={40}
                  max={150}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="nationality">Nacionalidade</Label>
                <Input
                  id="nationality"
                  placeholder="Nacionalidade"
                  value={nationality}
                  onChange={e => setNationality(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="number">Número da Camisa</Label>
                <Input
                  id="number"
                  placeholder="Número da camisa"
                  value={number}
                  onChange={e => setNumber(e.target.value)}
                  type="number"
                  min={1}
                  max={99}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="registrationNumber">Número de Cadastro</Label>
              <Input
                id="registrationNumber"
                placeholder={editMode ? "Número de cadastro" : "Gerado automaticamente"}
                value={registrationNumber}
                onChange={e => setRegistrationNumber(e.target.value)}
                disabled={!editMode} // Somente leitura na criação
                readOnly={!editMode}
              />
              {!editMode && (
                <p className="text-xs text-muted-foreground">
                  O número de cadastro será gerado automaticamente.
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select value={status} onValueChange={setStatus}>
                <SelectTrigger id="status">
                  <SelectValue placeholder="Status do jogador" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="disponivel">Disponível</SelectItem>
                  <SelectItem value="lesionado">Lesionado</SelectItem>
                  <SelectItem value="suspenso">Suspenso</SelectItem>
                  <SelectItem value="em recuperacao">Em recuperação</SelectItem>
                  <SelectItem value="inativo">Inativo</SelectItem>
                  <SelectItem value="emprestado">Emprestado</SelectItem>
                  <SelectItem value="em avaliacao">Em Avaliação</SelectItem>
                </SelectContent>
              </Select>
            </div>
              </TabsContent>

              {/* Tab de Categoria */}
              <TabsContent value="category" className="space-y-4 p-1">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="entryDate">Data de Entrada*</Label>
                <Input
                  id="entryDate"
                  type="date"
                  value={entryDate}
                  onChange={e => setEntryDate(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="exitDate">Data de Saída</Label>
                <Input
                  id="exitDate"
                  type="date"
                  value={exitDate}
                  onChange={e => setExitDate(e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="championshipRegistration">Inscrição Campeonato</Label>
                <Input
                  id="championshipRegistration"
                  placeholder="Número de inscrição"
                  value={championshipRegistration}
                  onChange={e => setChampionshipRegistration(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="professionalStatus">Profissional/Amador*</Label>
                <Select value={professionalStatus} onValueChange={setProfessionalStatus}>
                  <SelectTrigger id="professionalStatus">
                    <SelectValue placeholder="Selecione o status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="profissional">Profissional</SelectItem>
                    <SelectItem value="amador">Amador</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="contractEndDate">Fim do Contrato</Label>
              <Input
                id="contractEndDate"
                type="date"
                value={contractEndDate}
                onChange={e => setContractEndDate(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Categoria*</Label>
              <Select value={selectedCategoryId} onValueChange={setSelectedCategoryId} required>
                <SelectTrigger id="category">
                  <SelectValue placeholder="Selecione uma categoria" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="observation">Observação (apenas administradores podem editar)</Label>
              <textarea
                id="observation"
                className="w-full min-h-[100px] p-2 border rounded-md"
                placeholder="Observações sobre o jogador"
                value={observation}
                onChange={e => setObservation(e.target.value)}
                readOnly={!editMode} // Apenas administradores podem editar
              />
              {!editMode && (
                <p className="text-xs text-muted-foreground">
                  Este campo só pode ser editado por administradores.
                </p>
              )}
            </div>

            {/* Campos de empréstimo - aparecem apenas quando o status é "emprestado" */}
            {status === "emprestado" && (
              <div className="mt-4 p-4 border border-primary/20 bg-primary/5 rounded-md space-y-4">
                <h3 className="font-medium text-primary">Informações de Empréstimo</h3>

                <div className="space-y-2">
                  <Label htmlFor="loanClubName">Clube de Destino*</Label>
                  <Input
                    id="loanClubName"
                    placeholder="Nome do clube para o qual o jogador foi emprestado"
                    value={loanClubName}
                    onChange={e => setLoanClubName(e.target.value)}
                    required={status === "emprestado"}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="loanEndDate">Data de Fim do Empréstimo*</Label>
                  <Input
                    id="loanEndDate"
                    type="date"
                    value={loanEndDate}
                    onChange={e => setLoanEndDate(e.target.value)}
                    required={status === "emprestado"}
                  />
                  <p className="text-xs text-primary">
                    Ao chegar nesta data, o jogador voltará automaticamente para o status "Disponível"
                  </p>
                </div>
              </div>
            )}
              </TabsContent>

              {/* Tab de Documentos */}
              <TabsContent value="documents" className="space-y-4 p-1">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="rgNumber">Número RG</Label>
                <Input
                  id="rgNumber"
                  placeholder="RG"
                  value={rgNumber}
                  onChange={e => setRgNumber(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="cpfNumber">Número CPF*</Label>
                <Input
                  id="cpfNumber"
                  placeholder="CPF"
                  value={cpfNumber}
                  onChange={e => {
                    // Permitir entrada de caracteres formatados (pontos e traço)
                    let value = e.target.value;

                    // Se o usuário estiver digitando números, formatar automaticamente
                    if (value.match(/^\d+$/)) {
                      // Limitar a 11 dígitos
                      value = value.slice(0, 11);

                      // Formatar CPF automaticamente enquanto digita
                      if (value.length > 9) {
                        value = value.replace(/^(\d{3})(\d{3})(\d{3})(\d{0,2}).*/, "$1.$2.$3-$4");
                      } else if (value.length > 6) {
                        value = value.replace(/^(\d{3})(\d{3})(\d{0,3}).*/, "$1.$2.$3");
                      } else if (value.length > 3) {
                        value = value.replace(/^(\d{3})(\d{0,3}).*/, "$1.$2");
                      }

                      setCpfNumber(value);
                    }
                    // Se o usuário estiver colando um CPF já formatado ou editando manualmente
                    else {
                      // Remover todos os caracteres não numéricos
                      const numericValue = value.replace(/\D/g, "");

                      // Limitar a 11 dígitos
                      if (numericValue.length <= 11) {
                        // Se for um CPF completo, formatar
                        if (numericValue.length === 11) {
                          setCpfNumber(formatCPF(numericValue));
                        } else {
                          // Manter a formatação atual se estiver editando
                          setCpfNumber(value);
                        }
                      }
                    }
                  }}
                  maxLength={14} // 11 dígitos + 3 caracteres de formatação (. e -)
                  onBlur={() => {
                    // Validar CPF ao perder o foco
                    if (cpfNumber) {
                      const cleanCPF = cpfNumber.replace(/\D/g, "");
                      if (cleanCPF.length !== 11) {
                        toast({
                          title: "CPF Incompleto",
                          description: "O CPF deve conter 11 dígitos",
                          variant: "destructive",
                        });
                      } else {
                        const isValid = validateCPF(cpfNumber);
                        if (!isValid) {
                          toast({
                            title: "CPF Inválido",
                            description: "O CPF informado não é válido",
                            variant: "destructive",
                          });
                        }
                      }
                    }
                  }}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="fatherName">Nome do Pai</Label>
                <Input
                  id="fatherName"
                  placeholder="Nome do pai"
                  value={fatherName}
                  onChange={e => setFatherName(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="motherName">Nome da Mãe</Label>
                <Input
                  id="motherName"
                  placeholder="Nome da mãe"
                  value={motherName}
                  onChange={e => setMotherName(e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="referredBy">Indicado por</Label>
              <Input
                id="referredBy"
                placeholder="Indicado por"
                value={referredBy}
                onChange={e => setReferredBy(e.target.value)}
              />
            </div>
              </TabsContent>

              {/* Tab de Contato */}
              <TabsContent value="contact" className="space-y-4 p-1">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="phone">Telefone*</Label>
                <Input
                  id="phone"
                  placeholder="Telefone"
                  value={phone}
                  onChange={e => setPhone(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Email"
                  value={email}
                  onChange={e => setEmail(e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="address">Endereço</Label>
              <Input
                id="address"
                placeholder="Endereço completo"
                value={address}
                onChange={e => setAddress(e.target.value)}
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="zipCode">CEP</Label>
                <div className="flex space-x-2">
                  <Input
                    id="zipCode"
                    placeholder="CEP"
                    value={zipCode}
                    onChange={e => setZipCode(e.target.value)}
                    onBlur={handleCEPSearch}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCEPSearch}
                    disabled={loadingCEP || !zipCode || zipCode.length < 8}
                  >
                    {loadingCEP ? "..." : "Buscar"}
                  </Button>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="city">Cidade</Label>
                <Input
                  id="city"
                  placeholder="Cidade"
                  value={city}
                  onChange={e => setCity(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="state">Estado</Label>
                <Select value={state} onValueChange={setState}>
                  <SelectTrigger id="state">
                    <SelectValue placeholder="UF" />
                  </SelectTrigger>
                  <SelectContent>
                    {STATES.map(st => (
                      <SelectItem key={st} value={st}>{st}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
              </TabsContent>

              {/* Tab de Alojamento */}
              <TabsContent value="accommodation" className="space-y-4 p-1">
            <div className="flex items-center space-x-2 mb-4">
              <Checkbox
                id="needsAccommodation"
                checked={needsAccommodation}
                onCheckedChange={(checked) => {
                  setNeedsAccommodation(checked as boolean);
                  if (!checked) {
                    setSelectedAccommodationId("");
                    setSelectedRoomId("");
                  }
                }}
              />
              <Label htmlFor="needsAccommodation" className="cursor-pointer">
                Jogador precisa de alojamento
              </Label>
            </div>

            {needsAccommodation && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="accommodation">Alojamento*</Label>
                  <Select
                    value={selectedAccommodationId}
                    onValueChange={(value) => {
                      setSelectedAccommodationId(value);
                      setSelectedRoomId("");
                    }}
                    disabled={loadingAccommodations}
                    required
                  >
                    <SelectTrigger id="accommodation">
                      <SelectValue placeholder="Selecione um alojamento" />
                    </SelectTrigger>
                    <SelectContent>
                      {accommodations.map(accommodation => (
                        <SelectItem key={accommodation.id} value={accommodation.id.toString()}>
                          {accommodation.name} ({accommodation.type})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {selectedAccommodationId && accommodations.find(
                  acc => acc.id.toString() === selectedAccommodationId
                )?.type === 'hotel' && (
                  <div className="space-y-2">
                    <Label htmlFor="room">Quarto*</Label>
                    <Select
                      value={selectedRoomId}
                      onValueChange={setSelectedRoomId}
                      disabled={loadingAccommodations || hotelRooms.length === 0}
                      required
                    >
                      <SelectTrigger id="room">
                        <SelectValue placeholder="Selecione um quarto" />
                      </SelectTrigger>
                      <SelectContent>
                        {hotelRooms.map(room => (
                          <SelectItem key={room.id} value={room.id.toString()}>
                            Quarto {room.room_number} (Capacidade: {room.capacity})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {hotelRooms.length === 0 && selectedAccommodationId && (
                      <p className="text-sm text-amber-600">
                        Não há quartos disponíveis para este hotel. Por favor, adicione quartos na página de Alojamentos.
                      </p>
                    )}
                  </div>
                )}

                <div className="mt-4 p-4 bg-primary/5 border border-primary/20 rounded-md">
                  <p className="text-sm text-primary">
                    <strong>Nota:</strong> Ao associar um jogador a um alojamento, ele será automaticamente registrado com data de entrada de hoje.
                    Você pode gerenciar os detalhes do alojamento na página de Alojamentos.
                  </p>
                </div>
              </>
            )}
              </TabsContent>
            </div>
          </Tabs>
        </div>

        <DialogFooter className="pt-4 border-t bg-muted/20 flex-shrink-0">
          <div className="flex gap-3 w-full">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="flex-1"
            >
              Cancelar
            </Button>
            <Button
              onClick={handleSave}
              disabled={loadingPlayer}
              className="flex-1 bg-primary hover:bg-primary/90"
            >
              {loadingPlayer ? (
                <>
                  <span className="animate-spin mr-2">⏳</span>
                  Salvando...
                </>
              ) : (
                editMode ? "Atualizar Jogador" : "Criar Jogador"
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
