import { create } from "zustand";
import { MedicalRecord } from "../api/api";
import { getMedicalRecords, createMedicalRecord, updateMedicalRecord, deleteMedicalRecord } from "../api/api";

interface MedicalRecordsState {
  medicalRecords: MedicalRecord[];
  loading: boolean;
  error: string | null;
  fetchMedicalRecords: (clubId: number) => Promise<void>;
  addMedicalRecord: (clubId: number, record: Omit<MedicalRecord, "id">) => Promise<void>;
  updateMedicalRecord: (clubId: number, id: number, record: Partial<MedicalRecord>) => Promise<void>;
  deleteMedicalRecord: (clubId: number, id: number) => Promise<void>;
}

export const useMedicalRecordsStore = create<MedicalRecordsState>((set) => ({
  medicalRecords: [],
  loading: false,
  error: null,

  fetchMedicalRecords: async (clubId: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const medicalRecords = await getMedicalRecords(clubId);
      set({ medicalRecords, loading: false });
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao buscar prontuários", loading: false });
    }
  },

  addMedicalRecord: async (clubId: number, record: Omit<MedicalRecord, "id">): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const newRecord = await createMedicalRecord(clubId, record);
      set((state) => ({ medicalRecords: [...state.medicalRecords, newRecord], loading: false }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao adicionar prontuário", loading: false });
    }
  },

  updateMedicalRecord: async (clubId: number, id: number, record: Partial<MedicalRecord>): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const updated = await updateMedicalRecord(clubId, id, record);
      if (updated) {
        set((state) => ({ medicalRecords: state.medicalRecords.map(r => r.id === id ? updated : r), loading: false }));
      } else {
        set({ error: "Prontuário não encontrado", loading: false });
      }
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao atualizar prontuário", loading: false });
    }
  },

  deleteMedicalRecord: async (clubId: number, id: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const ok = await deleteMedicalRecord(clubId, id);
      if (ok) {
        set((state) => ({ medicalRecords: state.medicalRecords.filter(r => r.id !== id), loading: false }));
      } else {
        set({ error: "Prontuário não encontrado", loading: false });
      }
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao deletar prontuário", loading: false });
    }
  },
}));
