import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FinancialAccount } from "@/api/api";
import { toast } from "@/hooks/use-toast";
import { jsPDF } from "jspdf";
import autoTable from 'jspdf-autotable';
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { useCurrentClubId } from "@/context/ClubContext";
import { getClubInfo } from "@/api";

interface AccountsReportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  accounts: FinancialAccount[];
  currentMonth: number;
  currentYear: number;
}

// Interface for jsPDF with autoTable
interface jsPDFWithAutoTable extends jsPDF {
  autoTable: typeof autoTable;
  internal: {
    getNumberOfPages: () => number;
    pageSize: {
      width: number;
      height: number;
    };
  };
}

export function AccountsReportDialog({
  open,
  onOpenChange,
  accounts,
  currentMonth,
  currentYear
}: AccountsReportDialogProps) {
  const [month, setMonth] = useState(currentMonth.toString());
  const [year, setYear] = useState(currentYear.toString());
  const [type, setType] = useState("todos");
  const [status, setStatus] = useState("todos");
  const [category, setCategory] = useState("todos");
  const [costCenter, setCostCenter] = useState("todos");
  const [loading, setLoading] = useState(false);
  const clubId = useCurrentClubId();

  const months = [
    "Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho",
    "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"
  ];

  // Get unique cost centers from accounts
  const uniqueCostCenters = Array.from(
    new Set(accounts.filter(a => a.cost_center).map(a => a.cost_center))
  );

  // Get unique categories from accounts
  const uniqueCategories = Array.from(
    new Set(accounts.map(a => a.category))
  );

  const handleGenerate = async () => {
    setLoading(true);
    try {
      // Get club info for the report header
      const clubInfo = await getClubInfo(clubId);

      // Filter accounts based on selected criteria
      let filteredAccounts = [...accounts];

      // Filter by month and year
      if (month !== "todos" && year !== "todos") {
        const startDate = new Date(parseInt(year), parseInt(month) - 1, 1);
        const endDate = new Date(parseInt(year), parseInt(month), 0); // Last day of the month

        filteredAccounts = filteredAccounts.filter(account => {
          const dueDate = new Date(account.due_date);
          return dueDate >= startDate && dueDate <= endDate;
        });
      } else if (month !== "todos") {
        filteredAccounts = filteredAccounts.filter(account => {
          const dueDate = new Date(account.due_date);
          return dueDate.getMonth() + 1 === parseInt(month);
        });
      } else if (year !== "todos") {
        filteredAccounts = filteredAccounts.filter(account => {
          const dueDate = new Date(account.due_date);
          return dueDate.getFullYear() === parseInt(year);
        });
      }

      // Filter by type
      if (type !== "todos") {
        filteredAccounts = filteredAccounts.filter(account => account.type === type);
      }

      // Filter by status
      if (status !== "todos") {
        filteredAccounts = filteredAccounts.filter(account => account.status === status);
      }

      // Filter by category
      if (category !== "todos") {
        filteredAccounts = filteredAccounts.filter(account => account.category === category);
      }

      // Filter by cost center
      if (costCenter !== "todos") {
        filteredAccounts = filteredAccounts.filter(account => account.cost_center === costCenter);
      }

      // Sort accounts by due date
      filteredAccounts.sort((a, b) => {
        return new Date(a.due_date).getTime() - new Date(b.due_date).getTime();
      });

      // Create PDF
      const doc = new jsPDF("portrait", "mm", "a4") as jsPDFWithAutoTable;

      // Add header
      const pageWidth = doc.internal.pageSize.width;
      doc.setFontSize(18);
      doc.text("Relatório de Contas a Pagar e Receber", pageWidth / 2, 15, { align: "center" });

      // Add club info
      doc.setFontSize(12);
      doc.text(clubInfo.name, pageWidth / 2, 25, { align: "center" });
      if (clubInfo.address) {
        doc.setFontSize(10);
        doc.text(clubInfo.address, pageWidth / 2, 30, { align: "center" });
      }

      // Add report info
      doc.setFontSize(10);
      const reportDate = format(new Date(), "dd/MM/yyyy", { locale: ptBR });
      doc.text(`Data do relatório: ${reportDate}`, pageWidth - 15, 40, { align: "right" });

      // Add filter info
      let filterText = "Filtros: ";
      if (month !== "todos") filterText += `Mês: ${months[parseInt(month) - 1]}, `;
      if (year !== "todos") filterText += `Ano: ${year}, `;
      if (type !== "todos") filterText += `Tipo: ${type === "a_pagar" ? "A Pagar" : "A Receber"}, `;
      if (status !== "todos") filterText += `Status: ${status}, `;
      if (category !== "todos") filterText += `Categoria: ${category}, `;
      if (costCenter !== "todos") filterText += `Centro de Custo: ${costCenter}, `;

      // Remove trailing comma and space
      filterText = filterText.endsWith(", ") ? filterText.slice(0, -2) : filterText;

      doc.text(filterText, 15, 40);

      // Add table
      const tableData = filteredAccounts.map(account => [
        account.type === "a_pagar" ? "A Pagar" : "A Receber",
        account.description,
        account.supplier_client,
        format(new Date(account.due_date), "dd/MM/yyyy", { locale: ptBR }),
        `R$ ${account.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`,
        account.status === "pendente" ? "Pendente" : account.status === "pago" ? "Pago" : "Recebido"
      ]);

      autoTable(doc, {
        head: [["Tipo", "Descrição", "Fornecedor/Cliente", "Vencimento", "Valor", "Status"]],
        body: tableData,
        startY: 45,
        styles: {
          fontSize: 8,
          cellPadding: 2,
        },
        headStyles: {
          fillColor: [41, 128, 185],
          textColor: 255,
          fontStyle: "bold",
        },
        alternateRowStyles: {
          fillColor: [240, 240, 240],
        },
        margin: { top: 45 },
      });

      // Add summary
      const totalAPagar = filteredAccounts
        .filter(a => a.type === "a_pagar")
        .reduce((sum, a) => sum + a.amount, 0);

      const totalAReceber = filteredAccounts
        .filter(a => a.type === "a_receber")
        .reduce((sum, a) => sum + a.amount, 0);

      const totalPendente = filteredAccounts
        .filter(a => a.status === "pendente")
        .reduce((sum, a) => sum + a.amount, 0);

      const totalPago = filteredAccounts
        .filter(a => a.status === "pago" || a.status === "recebido")
        .reduce((sum, a) => sum + a.amount, 0);

      const summaryY = (doc as any).lastAutoTable.finalY + 10;

      doc.setFontSize(10);
      doc.text("Resumo:", 15, summaryY);
      doc.text(`Total a Pagar: R$ ${totalAPagar.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, 15, summaryY + 5);
      doc.text(`Total a Receber: R$ ${totalAReceber.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, 15, summaryY + 10);
      doc.text(`Total Pendente: R$ ${totalPendente.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, 15, summaryY + 15);
      doc.text(`Total Pago/Recebido: R$ ${totalPago.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, 15, summaryY + 20);

      // Add page numbers
      const pageCount = doc.internal.getNumberOfPages();
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setFontSize(8);
        doc.text(`Página ${i} de ${pageCount}`, pageWidth / 2, doc.internal.pageSize.height - 10, { align: "center" });
      }

      // Save the PDF
      const reportTitle = `Contas_${month !== "todos" ? months[parseInt(month) - 1] : ""}_${year !== "todos" ? year : ""}.pdf`;
      doc.save(reportTitle);

      toast({
        title: "Relatório gerado",
        description: "O relatório foi gerado com sucesso.",
      });
    } catch (error) {
      console.error("Erro ao gerar relatório:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao gerar o relatório.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Gerar Relatório de Contas</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="month">Mês</Label>
              <Select value={month} onValueChange={setMonth}>
                <SelectTrigger id="month">
                  <SelectValue placeholder="Selecione o mês" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos">Todos</SelectItem>
                  {months.map((monthName, index) => (
                    <SelectItem key={index + 1} value={(index + 1).toString()}>
                      {monthName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="year">Ano</Label>
              <Select value={year} onValueChange={setYear}>
                <SelectTrigger id="year">
                  <SelectValue placeholder="Selecione o ano" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos">Todos</SelectItem>
                  {[currentYear - 1, currentYear, currentYear + 1].map((y) => (
                    <SelectItem key={y} value={y.toString()}>
                      {y}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="type">Tipo</Label>
              <Select value={type} onValueChange={setType}>
                <SelectTrigger id="type">
                  <SelectValue placeholder="Selecione o tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos">Todos</SelectItem>
                  <SelectItem value="a_pagar">A Pagar</SelectItem>
                  <SelectItem value="a_receber">A Receber</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="status">Status</Label>
              <Select value={status} onValueChange={setStatus}>
                <SelectTrigger id="status">
                  <SelectValue placeholder="Selecione o status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos">Todos</SelectItem>
                  <SelectItem value="pendente">Pendente</SelectItem>
                  <SelectItem value="pago">Pago</SelectItem>
                  <SelectItem value="recebido">Recebido</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="category">Categoria</Label>
              <Select value={category} onValueChange={setCategory}>
                <SelectTrigger id="category">
                  <SelectValue placeholder="Selecione a categoria" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos">Todas</SelectItem>
                  {uniqueCategories.map((cat) => (
                    <SelectItem key={cat} value={cat}>
                      {cat}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="costCenter">Centro de Custo</Label>
              <Select value={costCenter} onValueChange={setCostCenter}>
                <SelectTrigger id="costCenter">
                  <SelectValue placeholder="Selecione o centro de custo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos">Todos</SelectItem>
                  {uniqueCostCenters.map((cc) => (
                    <SelectItem key={cc} value={cc}>
                      {cc}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleGenerate} disabled={loading}>
            {loading ? "Gerando..." : "Gerar Relatório"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
