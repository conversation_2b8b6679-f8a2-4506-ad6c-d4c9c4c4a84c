
import React, { useState, useEffect } from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, Filter } from "lucide-react";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface Column {
  key: string;
  header: string;
  render?: (row: any) => React.ReactNode;
  searchable?: boolean;
  filterable?: boolean;
}

interface FilterOption {
  key: string;
  label: string;
  options: { label: string; value: string }[];
}

interface DataTableProps {
  data: any[];
  columns: Column[];
  pageSize?: number;
  filterOptions?: FilterOption[];
  actions?: (row: any) => React.ReactNode;
  onRowClick?: (row: any) => void;
  className?: string;
  isLoading?: boolean;
}

export const DataTable: React.FC<DataTableProps> = ({
  data,
  columns,
  pageSize = 10,
  filterOptions = [],
  actions,
  onRowClick,
  className,
  isLoading = false,
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeFilters, setActiveFilters] = useState<Record<string, string>>({});

  // Filter data based on search term and filters
  const filteredData = React.useMemo(() => {
    return data
      .filter((row) => {
        // Apply search filter
        if (searchTerm) {
          const searchableColumns = columns.filter(col => col.searchable !== false);
          return searchableColumns.some(column => {
            const value = row[column.key];
            if (typeof value === 'string') {
              return value.toLowerCase().includes(searchTerm.toLowerCase());
            }
            return false;
          });
        }
        return true;
      })
      .filter((row) => {
        // Apply dropdown filters
        for (const [key, value] of Object.entries(activeFilters)) {
          if (value && row[key] !== undefined) {
            if (typeof row[key] === 'string' && row[key].toLowerCase() !== value.toLowerCase()) {
              return false;
            }
          }
        }
        return true;
      });
  }, [data, searchTerm, activeFilters, columns]);

  // Calculate pagination
  const totalPages = Math.ceil(filteredData.length / pageSize);
  const paginatedData = React.useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return filteredData.slice(startIndex, startIndex + pageSize);
  }, [filteredData, currentPage, pageSize]);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, activeFilters]);

  const handleFilterChange = (key: string, value: string) => {
    setActiveFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const renderPagination = () => {
    if (totalPages <= 1) return null;

    return (
      <Pagination className="mt-4">
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious 
              onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
              className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
            />
          </PaginationItem>
          
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            let pageNum: number;
            
            if (totalPages <= 5) {
              pageNum = i + 1;
            } else if (currentPage <= 3) {
              pageNum = i + 1;
            } else if (currentPage >= totalPages - 2) {
              pageNum = totalPages - 4 + i;
            } else {
              pageNum = currentPage - 2 + i;
            }
            
            return (
              <PaginationItem key={i}>
                <PaginationLink 
                  isActive={pageNum === currentPage}
                  onClick={() => setCurrentPage(pageNum)}
                >
                  {pageNum}
                </PaginationLink>
              </PaginationItem>
            );
          })}
          
          <PaginationItem>
            <PaginationNext 
              onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
              className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    );
  };

  return (
    <div className={className}>
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 mb-4">
        <div className="relative w-full sm:w-auto">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Buscar..."
            className="pl-8 w-full sm:w-[250px]"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        {filterOptions.length > 0 && (
          <div className="flex gap-2 w-full sm:w-auto overflow-x-auto pb-2 sm:pb-0">
            {filterOptions.map((filterGroup) => (
              <DropdownMenu key={filterGroup.key}>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-9">
                    <Filter className="h-4 w-4 mr-2" />
                    {filterGroup.label}
                    {activeFilters[filterGroup.key] && (
                      <span className="ml-1 bg-primary text-primary-foreground rounded-full w-2 h-2 inline-block"/>
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {filterGroup.options.map(option => (
                    <DropdownMenuItem 
                      key={option.value}
                      onClick={() => handleFilterChange(filterGroup.key, 
                        activeFilters[filterGroup.key] === option.value ? "" : option.value
                      )}
                      className={
                        activeFilters[filterGroup.key] === option.value 
                          ? "bg-muted font-medium" 
                          : ""
                      }
                    >
                      {option.label}
                    </DropdownMenuItem>
                  ))}
                  {activeFilters[filterGroup.key] && (
                    <DropdownMenuItem 
                      onClick={() => handleFilterChange(filterGroup.key, "")}
                      className="text-primary"
                    >
                      Limpar filtro
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            ))}
          </div>
        )}
      </div>

      <div className="border rounded-md overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              {columns.map((column) => (
                <TableHead key={column.key}>{column.header}</TableHead>
              ))}
              {actions && <TableHead></TableHead>}
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={columns.length + (actions ? 1 : 0)} className="text-center py-8">
                  <div className="flex justify-center">
                    <div className="h-6 w-6 border-2 border-t-primary rounded-full animate-spin"></div>
                  </div>
                </TableCell>
              </TableRow>
            ) : paginatedData.length === 0 ? (
              <TableRow>
                <TableCell colSpan={columns.length + (actions ? 1 : 0)} className="text-center py-8 text-muted-foreground">
                  Nenhum registro encontrado
                </TableCell>
              </TableRow>
            ) : (
              paginatedData.map((row, index) => (
                <TableRow 
                  key={index} 
                  onClick={onRowClick ? () => onRowClick(row) : undefined}
                  className={onRowClick ? "cursor-pointer hover:bg-muted" : ""}
                >
                  {columns.map((column) => (
                    <TableCell key={`${index}-${column.key}`}>
                      {column.render ? column.render(row) : row[column.key]}
                    </TableCell>
                  ))}
                  {actions && (
                    <TableCell onClick={e => e.stopPropagation()}>
                      {actions(row)}
                    </TableCell>
                  )}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {renderPagination()}
      
      <div className="text-sm text-muted-foreground mt-2">
        {isLoading ? "Carregando..." : `Mostrando ${paginatedData.length} de ${filteredData.length} registros`}
      </div>
    </div>
  );
};
