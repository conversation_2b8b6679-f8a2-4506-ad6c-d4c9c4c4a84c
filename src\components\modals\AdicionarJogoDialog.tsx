import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useState, useEffect } from "react";
import { createUpcomingMatch, getOpponents, getCompetitions, Opponent, Competition, getCategories, Category } from "@/api/api";
import { toast } from "@/hooks/use-toast";
import { useSeasonStore } from "@/store/useSeasonStore";
import { Loader2 } from "lucide-react";
import { useUser } from "@/context/UserContext";

interface AdicionarJogoDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
}

export function AdicionarJogoDialog({ open, onOpenChange, clubId }: AdicionarJogoDialogProps) {
  const [opponentId, setOpponentId] = useState("");
  const [competitionId, setCompetitionId] = useState("");
  const [date, setDate] = useState("");
  const [time, setTime] = useState("");
  const [location, setLocation] = useState("");
  const [type, setType] = useState<"casa" | "fora" | "">("");
  const [idaVolta, setIdaVolta] = useState(false);
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [opponents, setOpponents] = useState<Opponent[]>([]);
  const [competitions, setCompetitions] = useState<Competition[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [categoryId, setCategoryId] = useState<number | null>(null);
  const [createCallup, setCreateCallup] = useState(false);
  const [loadingData, setLoadingData] = useState(false);
  const { activeSeason } = useSeasonStore();
  const { user } = useUser();

  // Carregar adversários, competições e categorias
  useEffect(() => {
    if (open && clubId) {
      const fetchData = async () => {
        setLoadingData(true);
        try {
          const [opponentsData, competitionsData, categoriesData] = await Promise.all([
            getOpponents(clubId),
            getCompetitions(clubId, activeSeason?.id),
            getCategories(clubId)
          ]);
          setOpponents(opponentsData);
          setCompetitions(competitionsData);
          setCategories(categoriesData);
        } catch (error) {
          console.error("Erro ao carregar dados:", error);
          toast({
            title: "Erro ao carregar dados",
            description: "Não foi possível carregar os dados necessários.",
            variant: "destructive"
          });
        } finally {
          setLoadingData(false);
        }
      };
      fetchData();
    }
  }, [open, clubId, activeSeason?.id]);

  const handleSave = async () => {
    if (!activeSeason) {
      setError("Selecione uma temporada antes de adicionar o jogo.");
      return;
    }
    if (!opponentId) {
      setError("O adversário é obrigatório.");
      return;
    }
    if (!competitionId) {
      setError("A competição é obrigatória.");
      return;
    }
    if (!date) {
      setError("A data é obrigatória.");
      return;
    }
    if (!time) {
      setError("O horário é obrigatório.");
      return;
    }
    if (!location.trim()) {
      setError("O local é obrigatório.");
      return;
    }
    if (!type) {
      setError("O tipo de jogo é obrigatório.");
      return;
    }

    // Encontrar o adversário e a competição selecionados
    const selectedOpponent = opponents.find(o => o.id === opponentId);
    const selectedCompetition = competitions.find(c => c.id === competitionId);

    if (!selectedOpponent || !selectedCompetition) {
      setError("Adversário ou competição inválidos.");
      return;
    }

    setError("");
    setIsLoading(true);
    try {
      // Verificar se a categoria foi selecionada quando a opção de criar convocação está ativa
      if (createCallup && !categoryId) {
        setError("Selecione uma categoria para criar a convocação.");
        setIsLoading(false);
        return;
      }

      console.log("Criando partida com categoria_id:", categoryId);

      const result = await createUpcomingMatch(
        clubId,
        {
          club_id: clubId,
          opponent: selectedOpponent.name,
          opponent_id: opponentId,
          competition: selectedCompetition.name,
          competition_id: competitionId,
          date,
          time,
          location,
          type: type as "casa" | "fora",
          season_id: activeSeason.id,
          ida_volta: idaVolta,
          escalacao: undefined,
          formation: "",
          category_id: categoryId,
          create_callup: createCallup
        },
        user?.id
      );

      let successMessage = "Jogo adicionado com sucesso!";
      if (createCallup && categoryId) {
        successMessage += " Uma convocação foi criada automaticamente.";
      }

      toast({ title: successMessage, variant: "default" });
      setOpponentId("");
      setCompetitionId("");
      setDate("");
      setTime("");
      setLocation("");
      setType("");
      setIdaVolta(false);
      setCategoryId(null);
      setCreateCallup(false);
      onOpenChange(false);
    } catch (e) {
      console.error("Erro ao salvar jogo:", e);
      setError("Erro ao salvar jogo.");
      toast({ title: "Erro ao salvar jogo.", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle>Adicionar Jogo</DialogTitle>
        </DialogHeader>

        {loadingData ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
            <span className="ml-2">Carregando dados...</span>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div>
                <Label htmlFor="opponent" className="block text-sm font-medium mb-1">Adversário*</Label>
                <select
                  id="opponent"
                  value={opponentId}
                  onChange={e => setOpponentId(e.target.value)}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="">Selecione um adversário</option>
                  {opponents.map(opponent => (
                    <option key={opponent.id} value={opponent.id}>{opponent.name}</option>
                  ))}
                </select>
                <div className="flex justify-end mt-1">
                  <Button
                    type="button"
                    variant="link"
                    className="text-xs p-0 h-auto"
                    onClick={() => window.open('/adversarios', '_blank')}
                  >
                    Gerenciar adversários
                  </Button>
                </div>
              </div>

              <div>
                <Label htmlFor="competition" className="block text-sm font-medium mb-1">Competição*</Label>
                <select
                  id="competition"
                  value={competitionId}
                  onChange={e => setCompetitionId(e.target.value)}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="">Selecione uma competição</option>
                  {competitions.map(competition => (
                    <option key={competition.id} value={competition.id}>{competition.name}</option>
                  ))}
                </select>
                <div className="flex justify-end mt-1">
                  <Button
                    type="button"
                    variant="link"
                    className="text-xs p-0 h-auto"
                    onClick={() => window.open('/competicoes', '_blank')}
                  >
                    Gerenciar competições
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="date" className="block text-sm font-medium mb-1">Data*</Label>
                  <Input id="date" type="date" value={date} onChange={e => setDate(e.target.value)} />
                </div>
                <div>
                  <Label htmlFor="time" className="block text-sm font-medium mb-1">Horário*</Label>
                  <Input id="time" type="time" value={time} onChange={e => setTime(e.target.value)} />
                </div>
              </div>

              <div>
                <Label htmlFor="location" className="block text-sm font-medium mb-1">Local*</Label>
                <Input id="location" placeholder="Estádio, cidade" value={location} onChange={e => setLocation(e.target.value)} />
              </div>

              <div>
                <Label htmlFor="type" className="block text-sm font-medium mb-1">Tipo de jogo*</Label>
                <div className="flex gap-4">
                  <label className="flex items-center gap-2 p-3 border rounded-md flex-1 cursor-pointer hover:bg-blue-50">
                    <input
                      type="radio"
                      name="game-type"
                      value="casa"
                      checked={type === "casa"}
                      onChange={() => setType("casa")}
                      className="h-4 w-4"
                    />
                    <span>Casa</span>
                  </label>

                  <label className="flex items-center gap-2 p-3 border rounded-md flex-1 cursor-pointer hover:bg-blue-50">
                    <input
                      type="radio"
                      name="game-type"
                      value="fora"
                      checked={type === "fora"}
                      onChange={() => setType("fora")}
                      className="h-4 w-4"
                    />
                    <span>Fora</span>
                  </label>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="idaVolta"
                  checked={idaVolta}
                  onChange={e => setIdaVolta(e.target.checked)}
                  className="h-4 w-4"
                />
                <Label htmlFor="idaVolta">Partida de ida/volta?</Label>
              </div>

              <div>
                <Label htmlFor="category" className="block text-sm font-medium mb-1">Categoria</Label>
                <select
                  id="category"
                  value={categoryId?.toString() || ""}
                  onChange={e => setCategoryId(e.target.value ? parseInt(e.target.value) : null)}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="">Selecione uma categoria (opcional)</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>{category.name}</option>
                  ))}
                </select>
                <div className="text-xs text-gray-500 mt-1">
                  Selecionar uma categoria filtrará os jogadores disponíveis para esta partida.
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="createCallup"
                  checked={createCallup}
                  onChange={e => setCreateCallup(e.target.checked)}
                  className="h-4 w-4"
                  disabled={!categoryId}
                />
                <Label htmlFor="createCallup">
                  Criar convocação automaticamente?
                  {!categoryId && <span className="text-gray-500 text-xs ml-2">(Selecione uma categoria primeiro)</span>}
                </Label>
              </div>
            </div>

            {error && <div className="text-red-500 text-sm mt-1">{error}</div>}
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancelar</Button>
          <Button onClick={handleSave} disabled={isLoading || loadingData}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Salvar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
