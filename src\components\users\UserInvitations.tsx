import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { getClubInvitations, createUserInvitation, cancelUserInvitation, UserInvitation, getDepartments, Department } from "@/api/api";
import { DepartmentSelector } from "@/components/DepartmentSelector";
import { CollaboratorSelector } from "@/components/CollaboratorSelector";
import { CustomPermissionsSelector } from "@/components/users/CustomPermissionsSelector";
import { Plus, Trash2, Mail, Clock, Settings, User, Link, Link2 } from "lucide-react";
import { ROLES, ROLE_PERMISSIONS } from "@/constants/permissions";

export function UserInvitations() {
  const clubId = useCurrentClubId();
  const [invitations, setInvitations] = useState<UserInvitation[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [departments, setDepartments] = useState<Department[]>([]);

  // Estados para o formulário de convite
  const [email, setEmail] = useState("");
  const [role, setRole] = useState("staff"); // Usando o papel padrão de staff do nosso sistema de permissões
  const [departmentId, setDepartmentId] = useState("");
  const [formError, setFormError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState("basic");
  const [customPermissions, setCustomPermissions] = useState<Record<string, boolean>>({});
  const [useCustomPermissions, setUseCustomPermissions] = useState(false);
  const [generatePassword, setGeneratePassword] = useState(true);
  const [password, setPassword] = useState("");
  const [collaboratorId, setCollaboratorId] = useState("none");
  const [linkToCollaborator, setLinkToCollaborator] = useState(false);

  // Carregar convites
  useEffect(() => {
    const fetchInvitations = async () => {
      try {
        setLoading(true);
        const data = await getClubInvitations(clubId);
        setInvitations(data);
      } catch (err: any) {
        console.error("Erro ao carregar convites:", err);
        toast({
          title: "Erro",
          description: "Não foi possível carregar os convites",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchInvitations();
  }, [clubId]);

  // Carregar departamentos
  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        const data = await getDepartments(clubId);
        setDepartments(data);
      } catch (err: any) {
        console.error("Erro ao carregar departamentos:", err);
      }
    };

    fetchDepartments();
  }, [clubId]);

  // Função para criar um convite
  const handleCreateInvitation = async () => {
    // Validar campos
    if (!email.trim()) {
      setFormError("Email é obrigatório");
      return;
    }

    if (!generatePassword && !password.trim()) {
      setFormError("Senha é obrigatória quando não for gerada automaticamente");
      return;
    }

    if (linkToCollaborator && (!collaboratorId || collaboratorId === "none")) {
      setFormError("Selecione um colaborador para vincular");
      setActiveTab("collaborator");
      return;
    }

    try {
      setSubmitting(true);
      setFormError(null);

      // Determinar quais permissões usar
      const permissionsToUse = useCustomPermissions
        ? customPermissions
        : ROLE_PERMISSIONS[role as keyof typeof ROLE_PERMISSIONS] || {};

      const newInvitation = await createUserInvitation(
        clubId,
        email,
        role,
        departmentId ? parseInt(departmentId) : undefined,
        permissionsToUse,
        generatePassword ? undefined : password,
        linkToCollaborator && collaboratorId && collaboratorId !== "none" ? parseInt(collaboratorId) : undefined
      );

      // Adicionar o novo convite à lista
      setInvitations([newInvitation, ...invitations]);

      toast({
        title: "Sucesso",
        description: "Convite enviado com sucesso",
      });

      // Limpar formulário e fechar modal
      setEmail("");
      setRole("staff");
      setDepartmentId("");
      setCustomPermissions({});
      setUseCustomPermissions(false);
      setGeneratePassword(true);
      setPassword("");
      setCollaboratorId("none");
      setLinkToCollaborator(false);
      setActiveTab("basic");
      setIsCreateDialogOpen(false);
    } catch (err: any) {
      console.error("Erro ao criar convite:", err);
      setFormError(err.message || "Erro ao criar convite");
      toast({
        title: "Erro",
        description: err.message || "Erro ao criar convite",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Função para cancelar um convite
  const handleCancelInvitation = async (invitation: UserInvitation) => {
    if (!confirm(`Tem certeza que deseja cancelar o convite para ${invitation.email}?`)) {
      return;
    }

    try {
      await cancelUserInvitation(invitation.id);

      // Remover o convite da lista
      setInvitations(invitations.filter((inv) => inv.id !== invitation.id));

      toast({
        title: "Sucesso",
        description: "Convite cancelado com sucesso",
      });
    } catch (err: any) {
      console.error("Erro ao cancelar convite:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao cancelar convite",
        variant: "destructive",
      });
    }
  };

  // Função para obter o status do convite
  const getInvitationStatus = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
            Pendente
          </Badge>
        );
      case "accepted":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
            Aceito
          </Badge>
        );
      case "expired":
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200">
            Expirado
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200">
            {status}
          </Badge>
        );
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Convites de Usuários</CardTitle>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-1" />
          Novo Convite
        </Button>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        ) : invitations.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            Nenhum convite encontrado
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Email</TableHead>
                <TableHead>Função</TableHead>
                <TableHead className="hidden md:table-cell">Departamento</TableHead>
                <TableHead className="hidden md:table-cell">Colaborador</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="hidden md:table-cell">Data</TableHead>
                <TableHead className="w-[100px] text-right">Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {invitations.map((invitation) => (
                <TableRow key={invitation.id}>
                  <TableCell className="font-medium">{invitation.email}</TableCell>
                  <TableCell>{invitation.role}</TableCell>
                  <TableCell className="hidden md:table-cell">
                    {invitation.department_name || "Nenhum"}
                  </TableCell>
                  <TableCell className="hidden md:table-cell">
                    {invitation.collaborator_name ? (
                      <div className="flex items-center gap-1">
                        <Link2 className="h-4 w-4 text-blue-500" />
                        {invitation.collaborator_name}
                      </div>
                    ) : (
                      "Nenhum"
                    )}
                  </TableCell>
                  <TableCell>{getInvitationStatus(invitation.status)}</TableCell>
                  <TableCell className="hidden md:table-cell">
                    {new Date(invitation.created_at).toLocaleDateString()}
                  </TableCell>
                  <TableCell className="text-right">
                    {invitation.status === "pending" && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleCancelInvitation(invitation)}
                        className="text-red-500 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}

        {/* Modal para criar convite */}
        <Dialog open={isCreateDialogOpen} onOpenChange={(open) => {
          setIsCreateDialogOpen(open);
          if (!open) {
            // Resetar o formulário ao fechar
            setEmail("");
            setRole("staff");
            setDepartmentId("");
            setCustomPermissions({});
            setUseCustomPermissions(false);
            setGeneratePassword(true);
            setPassword("");
            setCollaboratorId("none");
            setLinkToCollaborator(false);
            setActiveTab("basic");
            setFormError(null);
          }
        }}>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Novo Convite de Usuário</DialogTitle>
            </DialogHeader>

            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid grid-cols-4 mb-4">
                <TabsTrigger value="basic">Informações Básicas</TabsTrigger>
                <TabsTrigger value="permissions">Permissões</TabsTrigger>
                <TabsTrigger value="password">Senha</TabsTrigger>
                <TabsTrigger value="collaborator">Colaborador</TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email*</Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Email do usuário"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="role">Função*</Label>
                  <Select
                    value={role}
                    onValueChange={(value) => {
                      setRole(value);
                      // Se não estiver usando permissões customizadas, atualizar as permissões
                      if (!useCustomPermissions) {
                        setCustomPermissions(ROLE_PERMISSIONS[value as keyof typeof ROLE_PERMISSIONS] || {});
                      }
                    }}
                  >
                    <SelectTrigger id="role">
                      <SelectValue placeholder="Selecione a função" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(ROLES).map(([key, value]) => (
                        <SelectItem key={key} value={key}>
                          {value.label} - {value.description}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <DepartmentSelector
                    value={departmentId}
                    onChange={setDepartmentId}
                    label="Departamento (opcional)"
                    placeholder="Selecione um departamento"
                  />
                </div>
              </TabsContent>

              <TabsContent value="permissions" className="space-y-4">
                <div className="flex items-center space-x-2 mb-4">
                  <Checkbox
                    id="useCustomPermissions"
                    checked={useCustomPermissions}
                    onCheckedChange={(checked) => {
                      setUseCustomPermissions(checked as boolean);
                      if (!checked) {
                        // Se desmarcar, voltar para as permissões padrão do papel
                        setCustomPermissions(ROLE_PERMISSIONS[role as keyof typeof ROLE_PERMISSIONS] || {});
                      }
                    }}
                  />
                  <Label htmlFor="useCustomPermissions">
                    Personalizar permissões (sobrescreve as permissões padrão da função)
                  </Label>
                </div>

                {useCustomPermissions ? (
                  <CustomPermissionsSelector
                    value={customPermissions}
                    onChange={setCustomPermissions}
                    showRoleSelector={true}
                  />
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    Ative a opção acima para personalizar as permissões.
                    <br />
                    Atualmente usando as permissões padrão da função {ROLES[role as keyof typeof ROLES]?.label || role}.
                  </div>
                )}
              </TabsContent>

              <TabsContent value="password" className="space-y-4">
                <div className="flex items-center space-x-2 mb-4">
                  <Checkbox
                    id="generatePassword"
                    checked={generatePassword}
                    onCheckedChange={(checked) => {
                      setGeneratePassword(checked as boolean);
                      if (checked) {
                        setPassword("");
                      }
                    }}
                  />
                  <Label htmlFor="generatePassword">
                    Gerar senha aleatória automaticamente
                  </Label>
                </div>

                {!generatePassword && (
                  <div className="space-y-2">
                    <Label htmlFor="password">Senha*</Label>
                    <Input
                      id="password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder="Digite a senha para o usuário"
                      required
                    />
                    <p className="text-xs text-muted-foreground">
                      A senha deve ter pelo menos 6 caracteres.
                    </p>
                  </div>
                )}

                {generatePassword && (
                  <div className="text-center py-8 text-muted-foreground">
                    Uma senha aleatória será gerada e enviada por email para o usuário.
                  </div>
                )}
              </TabsContent>

              <TabsContent value="collaborator" className="space-y-4">
                <div className="flex items-center space-x-2 mb-4">
                  <Checkbox
                    id="linkToCollaborator"
                    checked={linkToCollaborator}
                    onCheckedChange={(checked) => {
                      setLinkToCollaborator(checked as boolean);
                      if (!checked) {
                        setCollaboratorId("none");
                      }
                    }}
                  />
                  <Label htmlFor="linkToCollaborator" className="flex items-center gap-1">
                    <Link2 className="h-4 w-4" />
                    Vincular a um colaborador existente
                  </Label>
                </div>

                {linkToCollaborator && (
                  <CollaboratorSelector
                    value={collaboratorId}
                    onChange={setCollaboratorId}
                    label="Colaborador*"
                    placeholder="Selecione um colaborador"
                    required={true}
                    excludeWithUsers={true}
                  />
                )}

                {!linkToCollaborator && (
                  <div className="text-center py-8 text-muted-foreground">
                    O usuário não será vinculado a nenhum colaborador existente.
                    <br />
                    <span className="text-sm text-amber-500">
                      Vincular a um colaborador permite que o usuário acesse o sistema como o colaborador selecionado.
                    </span>
                  </div>
                )}
              </TabsContent>
            </Tabs>

            {formError && <p className="text-red-500 text-sm mt-4">{formError}</p>}

            <DialogFooter className="mt-4">
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                Cancelar
              </Button>
              <Button onClick={handleCreateInvitation} disabled={submitting}>
                {submitting ? "Enviando..." : "Enviar Convite"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}
