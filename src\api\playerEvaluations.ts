import { supabase } from "@/integrations/supabase/client";
import { withPermission, withAuditLog } from "./middleware";
import { PLAYER_PERMISSIONS } from "@/constants/permissions";
import { createMedicalNotification } from "./notifications";

// Types
export type PlayerEvaluation = {
  id: number;
  club_id: number;
  player_id: string;
  content: string;
  created_by: string;
  created_by_name?: string;
  created_by_role?: string;
  created_at: string;
  updated_at: string;
  updated_by: string | null;
  updated_by_name?: string;
  updated_by_role?: string;
  status: "pending" | "approved" | "released" | "monitored" | "rejected";
  is_locked: boolean;
  signature_url?: string;
  last_viewed_at?: string;

  // Approval workflow fields
  approved_by_manager?: string;
  approved_by_president?: string;
  manager_approved_at?: string;
  president_approved_at?: string;
  manager_notes?: string;
  president_notes?: string;
  requires_manager_approval?: boolean;
  requires_president_approval?: boolean;
  manager_name?: string;
  president_name?: string;

  // Additional fields for list view
  player_name?: string;
  player_image?: string;
  position?: string;
  evaluator_name?: string;
  evaluation_date?: string;
  notes?: string;

  // Fields for approval
  approved_by?: string;
  approved_by_name?: string;
  approval_date?: string;
  approval_signature_url?: string;
  evaluator_signature_url?: string;

  // Technical evaluation fields
  technical_evaluation?: Record<string, number>;

  // Height and weight
  height?: number;
  weight?: number;

  // Additional fields
  age?: number;
  dominant_foot?: string;
  pdf_url?: string;
};

/**
 * Enriches a player evaluation with user names
 * @param evaluation Player evaluation
 * @returns Player evaluation with user names
 */
async function enrichEvaluationWithUserNames(evaluation: PlayerEvaluation | null): Promise<PlayerEvaluation | null> {
  if (!evaluation) return null;

  // Get created_by user
  if (evaluation.created_by) {
    try {
      const { data: createdByUser } = await supabase
        .from("users")
        .select("name, role")
        .eq("id", evaluation.created_by)
        .single();

      if (createdByUser) {
        evaluation.created_by_name = createdByUser.name;
        evaluation.created_by_role = createdByUser.role;
      }
    } catch (error) {
      console.error("Error fetching created_by user:", error);
    }
  }

  // Get updated_by user
  if (evaluation.updated_by && evaluation.updated_by !== evaluation.created_by) {
    try {
      const { data: updatedByUser } = await supabase
        .from("users")
        .select("name, role")
        .eq("id", evaluation.updated_by)
        .single();

      if (updatedByUser) {
        evaluation.updated_by_name = updatedByUser.name;
        evaluation.updated_by_role = updatedByUser.role;
      }
    } catch (error) {
      console.error("Error fetching updated_by user:", error);
    }
  }

  // Get manager approver user
  if (evaluation.approved_by_manager) {
    try {
      const { data: managerUser } = await supabase
        .from("users")
        .select("name")
        .eq("id", evaluation.approved_by_manager)
        .single();

      if (managerUser) {
        evaluation.manager_name = managerUser.name;
      }
    } catch (error) {
      console.error("Error fetching manager user:", error);
    }
  }

  // Get president approver user
  if (evaluation.approved_by_president) {
    try {
      const { data: presidentUser } = await supabase
        .from("users")
        .select("name")
        .eq("id", evaluation.approved_by_president)
        .single();

      if (presidentUser) {
        evaluation.president_name = presidentUser.name;
      }
    } catch (error) {
      console.error("Error fetching president user:", error);
    }
  }

  return evaluation;
}

/**
 * Get player evaluation by player ID
 * @param clubId Club ID
 * @param playerId Player ID
 * @param userId User ID for permission check
 * @returns Player evaluation or null if not found
 */
export async function getPlayerEvaluation(
  clubId: number,
  playerId: string,
  userId: string
): Promise<PlayerEvaluation | null> {
  // Check if user is the player (can only view)
  const isOwnProfile = await isPlayerOwnedByUser(clubId, playerId, userId);

  // If it's the player's own profile, they can only view released evaluations
  if (isOwnProfile) {
    const { data, error } = await supabase
      .from("player_evaluations")
      .select("*")
      .eq("club_id", clubId)
      .eq("player_id", playerId)
      .eq("status", "released") // Only show released evaluations to players
      .single();

    if (error) {
      if (error.code === "PGRST116") {
        // No released evaluation found for player
        return null;
      }
      throw new Error(`Error fetching player evaluation: ${error.message}`);
    }

    // Update last_viewed_at timestamp when a player views their evaluation
    // Only update if there's an evaluation to view
    if (data) {
      // Update the last_viewed_at timestamp
      const { error: updateError } = await supabase
        .from("player_evaluations")
        .update({
          last_viewed_at: new Date().toISOString(),
        })
        .eq("club_id", clubId)
        .eq("player_id", playerId);

      if (updateError) {
        console.error("Error updating last_viewed_at:", updateError);
        // Continue even if there's an error updating the timestamp
      }
    }

    // Fetch user names for created_by and updated_by
    const evaluation = data as PlayerEvaluation;
    return await enrichEvaluationWithUserNames(evaluation);
  }

  // For staff members, check permissions
  return withPermission(
    clubId,
    userId,
    PLAYER_PERMISSIONS.VIEW,
    async () => {
      const { data, error } = await supabase
        .from("player_evaluations")
        .select("*")
        .eq("club_id", clubId)
        .eq("player_id", playerId)
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          // No evaluation found
          return null;
        }
        throw new Error(`Error fetching player evaluation: ${error.message}`);
      }

      // Fetch user names for created_by and updated_by
      const evaluation = data as PlayerEvaluation;
      return await enrichEvaluationWithUserNames(evaluation);
    }
  );
}

/**
 * Add digital signature text to evaluation content
 * @param content Evaluation content
 * @param userName User name
 * @param userRole User role
 * @returns Content with digital signature
 */
export async function addDigitalSignature(
  content: string,
  userName: string,
  userRole: string
): Promise<string> {
  const date = new Date().toLocaleDateString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
  const time = new Date().toLocaleTimeString('pt-BR', {
    hour: '2-digit',
    minute: '2-digit'
  });

  const roleTranslation: Record<string, string> = {
    'president': 'Presidente',
    'admin': 'Administrador',
    'manager': 'Gerente',
    'coach': 'Treinador',
    'medical': 'Médico',
    'staff': 'Funcionário',
    'player': 'Jogador'
  };

  const translatedRole = roleTranslation[userRole] || userRole;

  const signature = `
    <div style="margin-top: 40px; border-top: 2px solid #e5e7eb; padding-top: 20px; background-color: #f9fafb; padding: 20px; border-radius: 8px;">
      <div style="text-align: center;">
        <p style="margin: 0; font-weight: bold; font-size: 16px; color: #374151;">✍️ Assinatura Digital</p>
        <div style="margin-top: 15px; padding: 10px; background-color: white; border-radius: 6px; border: 1px solid #d1d5db;">
          <p style="margin: 0; font-weight: bold; font-size: 14px; color: #1f2937;">${userName}</p>
          <p style="margin: 5px 0 0 0; font-style: italic; color: #6b7280; font-size: 13px;">${translatedRole}</p>
          <p style="margin: 10px 0 0 0; font-size: 12px; color: #9ca3af;">
            📅 ${date} às ${time}
          </p>
        </div>
        <p style="margin: 10px 0 0 0; font-size: 11px; color: #9ca3af; font-style: italic;">
          Documento assinado digitalmente conforme MP 2.200-2/2001
        </p>
      </div>
    </div>
  `;

  return content + signature;
}

/**
 * Update player evaluation with signature URL
 * @param clubId Club ID
 * @param playerId Player ID
 * @param signatureUrl Signature URL
 * @param userId User ID for permission check
 * @returns Updated player evaluation
 */
export async function updatePlayerEvaluationSignature(
  clubId: number,
  playerId: string,
  signatureUrl: string,
  userId: string
): Promise<PlayerEvaluation> {
  return withPermission(
    clubId,
    userId,
    PLAYER_PERMISSIONS.EDIT,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "player_evaluation.add_signature",
        { player_id: playerId },
        async () => {
          const { data, error } = await supabase
            .from("player_evaluations")
            .update({
              signature_url: signatureUrl,
              updated_at: new Date().toISOString(),
              updated_by: userId,
            })
            .eq("club_id", clubId)
            .eq("player_id", playerId)
            .select()
            .single();

          if (error) {
            throw new Error(`Error adding signature to player evaluation: ${error.message}`);
          }

          return await enrichEvaluationWithUserNames(data as PlayerEvaluation);
        }
      );
    }
  );
}

/**
 * Create or update player evaluation
 * @param clubId Club ID
 * @param playerId Player ID
 * @param content Evaluation content
 * @param userId User ID for permission check
 * @param addSignature Whether to add digital signature
 * @returns Created or updated player evaluation
 */
export async function savePlayerEvaluation(
  clubId: number,
  playerId: string,
  content: string,
  userId: string,
  addSignature: boolean = true // Changed default to true to always add signature
): Promise<PlayerEvaluation> {
  return withPermission(
    clubId,
    userId,
    PLAYER_PERMISSIONS.EDIT,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "player_evaluation.save",
        { player_id: playerId },
        async () => {
          // Check if evaluation already exists
          const { data: existingEvaluation } = await supabase
            .from("player_evaluations")
            .select("*")
            .eq("club_id", clubId)
            .eq("player_id", playerId)
            .single();

          // Get user information for signature
          let userName = "Usuário";
          let userRole = "";

          if (addSignature) {
            try {
              const { data: userData } = await supabase
                .from("users")
                .select("name, role")
                .eq("id", userId)
                .single();

              if (userData) {
                userName = userData.name || "Usuário";
                userRole = userData.role || "";
              }
            } catch (error) {
              console.error("Error fetching user data for signature:", error);
            }
          }

          // Add digital signature if requested
          let finalContent = content;
          if (addSignature) {
            finalContent = await addDigitalSignature(content, userName, userRole);
          }

          if (existingEvaluation) {
            // If evaluation is locked, only admin/president can update it
            if (existingEvaluation.is_locked) {
              // Check if user is admin or president
              const { data: userRole } = await supabase
                .from("users")
                .select("role")
                .eq("id", userId)
                .single();

              if (userRole?.role !== "admin" && userRole?.role !== "president") {
                throw new Error("This evaluation is locked and can only be edited by administrators or president");
              }
            }

            // Update existing evaluation
            const { data, error } = await supabase
              .from("player_evaluations")
              .update({
                content: finalContent,
                updated_at: new Date().toISOString(),
                updated_by: userId,
              })
              .eq("club_id", clubId)
              .eq("player_id", playerId)
              .select()
              .single();

            if (error) {
              throw new Error(`Error updating player evaluation: ${error.message}`);
            }

            return await enrichEvaluationWithUserNames(data as PlayerEvaluation);
          } else {
            // Create new evaluation
            const { data, error } = await supabase
              .from("player_evaluations")
              .insert({
                club_id: clubId,
                player_id: playerId,
                content: finalContent,
                created_by: userId,
                updated_by: userId,
              })
              .select()
              .single();

            if (error) {
              throw new Error(`Error creating player evaluation: ${error.message}`);
            }

            return await enrichEvaluationWithUserNames(data as PlayerEvaluation);
          }
        }
      );
    }
  );
}

/**
 * Lock player evaluation to prevent further edits
 * @param clubId Club ID
 * @param playerId Player ID
 * @param userId User ID for permission check
 * @returns Updated player evaluation
 */
export async function lockPlayerEvaluation(
  clubId: number,
  playerId: string,
  userId: string
): Promise<PlayerEvaluation> {
  return withPermission(
    clubId,
    userId,
    PLAYER_PERMISSIONS.EDIT,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "player_evaluation.lock",
        { player_id: playerId },
        async () => {
          const { data, error } = await supabase
            .from("player_evaluations")
            .update({
              is_locked: true,
              updated_at: new Date().toISOString(),
              updated_by: userId,
            })
            .eq("club_id", clubId)
            .eq("player_id", playerId)
            .select()
            .single();

          if (error) {
            throw new Error(`Error locking player evaluation: ${error.message}`);
          }

          return data as PlayerEvaluation;
        }
      );
    }
  );
}

/**
 * Update player evaluation status
 * @param clubId Club ID
 * @param playerId Player ID
 * @param status New status
 * @param userId User ID for permission check
 * @returns Updated player evaluation
 */
export async function updatePlayerEvaluationStatus(
  clubId: number,
  playerId: string,
  status: "approved" | "released" | "monitored",
  userId: string
): Promise<PlayerEvaluation> {
  return withPermission(
    clubId,
    userId,
    PLAYER_PERMISSIONS.EDIT,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "player_evaluation.status_update",
        { player_id: playerId, status },
        async () => {
          const { data, error } = await supabase
            .from("player_evaluations")
            .update({
              status,
              updated_at: new Date().toISOString(),
              updated_by: userId,
            })
            .eq("club_id", clubId)
            .eq("player_id", playerId)
            .select(`
              *,
              players:player_id (
                name
              )
            `)
            .single();

          if (error) {
            throw new Error(`Error updating player evaluation status: ${error.message}`);
          }

          // Se o status foi alterado para "released", enviar notificações
          if (status === "released") {
            const playerName = (data.players as any)?.name || "Atleta desconhecido";
            await notifyEvaluationReleased(clubId, playerName, data.id);
          }

          return data as PlayerEvaluation;
        }
      );
    }
  );
}

/**
 * Delete player evaluation
 * @param clubId Club ID
 * @param playerId Player ID
 * @param userId User ID for permission check
 * @returns Success status
 */
export async function deletePlayerEvaluation(
  clubId: number,
  playerId: string,
  userId: string
): Promise<boolean> {
  return withPermission(
    clubId,
    userId,
    PLAYER_PERMISSIONS.EDIT,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "player_evaluation.delete",
        { player_id: playerId },
        async () => {
          // Check if user is admin or president
          const { data: userRole } = await supabase
            .from("users")
            .select("role")
            .eq("id", userId)
            .single();

          if (userRole?.role !== "admin" && userRole?.role !== "president") {
            throw new Error("Only administrators or president can delete evaluations");
          }

          const { error } = await supabase
            .from("player_evaluations")
            .delete()
            .eq("club_id", clubId)
            .eq("player_id", playerId);

          if (error) {
            throw new Error(`Error deleting player evaluation: ${error.message}`);
          }

          return true;
        }
      );
    }
  );
}

/**
 * Check if player is owned by user
 * @param clubId Club ID
 * @param playerId Player ID
 * @param userId User ID
 * @returns True if player is owned by user
 */
async function isPlayerOwnedByUser(
  clubId: number,
  playerId: string,
  userId: string
): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from("players")
      .select("user_id")
      .eq("club_id", clubId)
      .eq("id", playerId)
      .single();

    if (error || !data) {
      return false;
    }

    return data.user_id === userId;
  } catch (error) {
    console.error("Error checking player ownership:", error);
    return false;
  }
}

/**
 * Get pending evaluations for approval by managers and presidents
 * @param clubId Club ID
 * @param userId User ID for permission check
 * @returns List of pending evaluations
 */
export async function getPendingEvaluationsForApproval(
  clubId: number,
  userId: string
): Promise<PlayerEvaluation[]> {
  return withPermission(
    clubId,
    userId,
    PLAYER_PERMISSIONS.EDIT,
    async () => {
      const { data, error } = await supabase
        .from("player_evaluations")
        .select(`
          *,
          players:player_id (
            id,
            name,
            position,
            image
          )
        `)
        .eq("club_id", clubId)
        .eq("status", "pending")
        .order("created_at", { ascending: false });

      if (error) {
        throw new Error(`Error fetching pending evaluations: ${error.message}`);
      }

      // Enrich each evaluation with user names
      const enrichedEvaluations = await Promise.all(
        data.map(async (evaluation: any) => {
          const enriched = {
            ...evaluation,
            player_name: evaluation.players?.name,
            player_image: evaluation.players?.image,
            position: evaluation.players?.position,
          };

          // Get created_by user name
          if (evaluation.created_by) {
            try {
              const { data: createdByUser } = await supabase
                .from("users")
                .select("name")
                .eq("id", evaluation.created_by)
                .single();

              if (createdByUser) {
                enriched.created_by_name = createdByUser.name;
              }
            } catch (error) {
              console.error("Error fetching created_by user:", error);
            }
          }

          // Get manager user name
          if (evaluation.approved_by_manager) {
            try {
              const { data: managerUser } = await supabase
                .from("users")
                .select("name")
                .eq("id", evaluation.approved_by_manager)
                .single();

              if (managerUser) {
                enriched.manager_name = managerUser.name;
              }
            } catch (error) {
              console.error("Error fetching manager user:", error);
            }
          }

          // Get president user name
          if (evaluation.approved_by_president) {
            try {
              const { data: presidentUser } = await supabase
                .from("users")
                .select("name")
                .eq("id", evaluation.approved_by_president)
                .single();

              if (presidentUser) {
                enriched.president_name = presidentUser.name;
              }
            } catch (error) {
              console.error("Error fetching president user:", error);
            }
          }

          return enriched;
        })
      );

      return enrichedEvaluations;
    }
  );
}

/**
 * Approve evaluation as manager
 * @param clubId Club ID
 * @param evaluationId Evaluation ID
 * @param userId Manager user ID
 * @param notes Optional manager notes
 * @returns Updated evaluation
 */
export async function approveEvaluationAsManager(
  clubId: number,
  evaluationId: number,
  userId: string,
  notes?: string
): Promise<PlayerEvaluation> {
  return withPermission(
    clubId,
    userId,
    PLAYER_PERMISSIONS.EDIT,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "player_evaluation.manager_approval",
        { evaluation_id: evaluationId },
        async () => {
          const { data, error } = await supabase
            .from("player_evaluations")
            .update({
              approved_by_manager: userId,
              manager_approved_at: new Date().toISOString(),
              manager_notes: notes,
              updated_at: new Date().toISOString(),
              updated_by: userId,
            })
            .eq("id", evaluationId)
            .eq("club_id", clubId)
            .select()
            .single();

          if (error) {
            throw new Error(`Error approving evaluation as manager: ${error.message}`);
          }

          return await enrichEvaluationWithUserNames(data);
        }
      );
    }
  );
}

/**
 * Approve evaluation as president
 * @param clubId Club ID
 * @param evaluationId Evaluation ID
 * @param userId President user ID
 * @param notes Optional president notes
 * @returns Updated evaluation
 */
export async function approveEvaluationAsPresident(
  clubId: number,
  evaluationId: number,
  userId: string,
  notes?: string
): Promise<PlayerEvaluation> {
  return withPermission(
    clubId,
    userId,
    PLAYER_PERMISSIONS.EDIT,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "player_evaluation.president_approval",
        { evaluation_id: evaluationId },
        async () => {
          // First get the current evaluation to check if manager has approved
          const { data: currentEvaluation, error: fetchError } = await supabase
            .from("player_evaluations")
            .select("*")
            .eq("id", evaluationId)
            .eq("club_id", clubId)
            .single();

          if (fetchError) {
            throw new Error(`Error fetching evaluation: ${fetchError.message}`);
          }

          const updateData: any = {
            approved_by_president: userId,
            president_approved_at: new Date().toISOString(),
            president_notes: notes,
            updated_at: new Date().toISOString(),
            updated_by: userId,
          };

          // PRESIDENTE TEM AUTORIDADE SUPERIOR: Aprovação do presidente libera automaticamente a avaliação
          // independente da aprovação do gerente
          updateData.status = "released";

          const { data, error } = await supabase
            .from("player_evaluations")
            .update(updateData)
            .eq("id", evaluationId)
            .eq("club_id", clubId)
            .select(`
              *,
              players:player_id (
                name
              )
            `)
            .single();

          if (error) {
            throw new Error(`Error approving evaluation as president: ${error.message}`);
          }

          // Se a avaliação foi liberada, enviar notificações
          if (updateData.status === "released") {
            const playerName = (data.players as any)?.name || "Atleta desconhecido";
            await notifyEvaluationReleased(clubId, playerName, evaluationId);
          }

          return await enrichEvaluationWithUserNames(data);
        }
      );
    }
  );
}

/**
 * Reject evaluation with reason
 * @param clubId Club ID
 * @param evaluationId Evaluation ID
 * @param userId User ID rejecting
 * @param reason Rejection reason
 * @param rejectorRole Role of the person rejecting (manager or president)
 * @returns Updated evaluation
 */
export async function rejectEvaluation(
  clubId: number,
  evaluationId: number,
  userId: string,
  reason: string,
  rejectorRole: "manager" | "president"
): Promise<PlayerEvaluation> {
  return withPermission(
    clubId,
    userId,
    PLAYER_PERMISSIONS.EDIT,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "player_evaluation.rejection",
        { evaluation_id: evaluationId, rejector_role: rejectorRole },
        async () => {
          const updateData: any = {
            status: "rejected",
            updated_at: new Date().toISOString(),
            updated_by: userId,
          };

          if (rejectorRole === "manager") {
            updateData.manager_notes = reason;
            updateData.approved_by_manager = userId;
            updateData.manager_approved_at = new Date().toISOString();
          } else {
            updateData.president_notes = reason;
            updateData.approved_by_president = userId;
            updateData.president_approved_at = new Date().toISOString();
          }

          const { data, error } = await supabase
            .from("player_evaluations")
            .update(updateData)
            .eq("id", evaluationId)
            .eq("club_id", clubId)
            .select()
            .single();

          if (error) {
            throw new Error(`Error rejecting evaluation: ${error.message}`);
          }

          return await enrichEvaluationWithUserNames(data);
        }
      );
    }
  );
}

/**
 * Get all player evaluations for a club
 * @param clubId Club ID
 * @param playerId Optional player ID to filter by
 * @returns List of player evaluations
 */
export async function getPlayerEvaluations(
  clubId: number,
  playerId?: number
): Promise<any[]> {
  try {
    // Base query
    let query = supabase
      .from("player_evaluations")
      .select(`
        id,
        club_id,
        player_id,
        status,
        created_at,
        updated_at,
        created_by,
        updated_by,
        is_locked,
        last_viewed_at,
        approved_by_manager,
        approved_by_president,
        manager_approved_at,
        president_approved_at,
        manager_notes,
        president_notes,
        requires_manager_approval,
        requires_president_approval,
        players:player_id (
          id,
          name as player_name,
          position,
          image as player_image
        )
      `)
      .eq("club_id", clubId);

    // Add player filter if provided
    if (playerId) {
      query = query.eq("player_id", playerId);
    }

    // Execute query
    const { data, error } = await query.order("created_at", { ascending: false });

    if (error) {
      throw new Error(`Error fetching player evaluations: ${error.message}`);
    }

    // Format the data to flatten the structure
    const formattedData = data.map((item: any) => {
      return {
        id: item.id,
        club_id: item.club_id,
        player_id: item.player_id,
        player_name: item.players?.player_name || "Atleta não encontrado",
        position: item.players?.position || "",
        player_image: item.players?.player_image || "",
        status: item.status,
        created_at: item.created_at,
        updated_at: item.updated_at,
        created_by: item.created_by,
        updated_by: item.updated_by,
        is_locked: item.is_locked,
        last_viewed_at: item.last_viewed_at,
        notes: item.notes || "",
        evaluation_date: item.created_at,
      };
    });

    // Enrich with evaluator names
    for (const evaluation of formattedData) {
      try {
        if (evaluation.created_by) {
          const { data: userData } = await supabase
            .from("users")
            .select("name")
            .eq("id", evaluation.created_by)
            .single();

          if (userData) {
            evaluation.evaluator_name = userData.name;
          }
        }
      } catch (error) {
        console.error("Error fetching evaluator name:", error);
      }
    }

    return formattedData;
  } catch (error) {
    console.error("Error in getPlayerEvaluations:", error);
    throw error;
  }
}

/**
 * Get users by specific roles in a club
 * @param clubId Club ID
 * @param roles Array of roles to filter by
 * @returns List of users with specified roles
 */
async function getUsersByRoles(
  clubId: number,
  roles: string[]
): Promise<{ id: string; name: string; role: string }[]> {
  try {
    const { data, error } = await supabase
      .from("club_members")
      .select(`
        user_id,
        role,
        users:user_id (
          id,
          name
        )
      `)
      .eq("club_id", clubId)
      .in("role", roles);

    if (error) {
      throw new Error(`Error fetching users by roles: ${error.message}`);
    }

    return (data || [])
      .filter(member => member.users && member.user_id)
      .map(member => ({
        id: member.user_id,
        name: (member.users as any)?.name || "Usuário desconhecido",
        role: member.role
      }));
  } catch (error) {
    console.error("Error in getUsersByRoles:", error);
    return [];
  }
}

/**
 * Send notifications when an evaluation is released (status "liberado")
 * @param clubId Club ID
 * @param playerName Player name
 * @param evaluationId Evaluation ID
 */
async function notifyEvaluationReleased(
  clubId: number,
  playerName: string,
  evaluationId: number
): Promise<void> {
  try {
    // Get users with roles that should be notified: president, admin, manager
    const usersToNotify = await getUsersByRoles(clubId, ["president", "admin", "manager"]);

    if (usersToNotify.length === 0) {
      console.warn("No users found to notify about released evaluation");
      return;
    }

    // Create notifications for each user
    const notificationPromises = usersToNotify.map(user =>
      createMedicalNotification({
        club_id: clubId,
        user_id: user.id,
        title: "Atleta Dispensado",
        message: `O atleta ${playerName} foi dispensado (avaliação liberada).`,
        type: "evaluation_released",
        reference_id: evaluationId.toString(),
        reference_type: "player_evaluation"
      })
    );

    await Promise.all(notificationPromises);
    console.log(`Notifications sent to ${usersToNotify.length} users about released evaluation for ${playerName}`);
  } catch (error) {
    console.error("Error sending evaluation released notifications:", error);
    // Don't throw error to avoid breaking the main flow
  }
}
