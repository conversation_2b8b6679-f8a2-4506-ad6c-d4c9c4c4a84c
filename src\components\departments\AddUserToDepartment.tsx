import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { addUserToDepartment, Department } from "@/api/api";

interface AddUserToDepartmentProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  department?: Department;
  onSuccess?: () => void;
}

export function AddUserToDepartment({
  open,
  onOpenChange,
  department,
  onSuccess,
}: AddUserToDepartmentProps) {
  const clubId = useCurrentClubId();
  const [email, setEmail] = useState("");
  const [role, setRole] = useState("member");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Limpar formulário quando o modal é aberto/fechado
  useEffect(() => {
    if (open) {
      setEmail("");
      setRole("member");
      setError(null);
    }
  }, [open]);

  // Função para adicionar usuário ao departamento
  const handleAddUser = async () => {
    // Validar campos
    if (!email.trim()) {
      setError("Email é obrigatório");
      return;
    }

    if (!department) {
      setError("Departamento não selecionado");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Primeiro, verificar se o usuário existe
      // Se não existir, criar um convite
      // Se existir, adicionar ao departamento
      
      // Por enquanto, vamos apenas simular a adição
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: "Sucesso",
        description: "Usuário adicionado ao departamento com sucesso",
      });

      onOpenChange(false);

      if (onSuccess) {
        onSuccess();
      }
    } catch (err: any) {
      console.error("Erro ao adicionar usuário ao departamento:", err);
      setError(err.message || "Erro ao adicionar usuário ao departamento");
      toast({
        title: "Erro",
        description: err.message || "Erro ao adicionar usuário ao departamento",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            Adicionar Usuário ao Departamento: {department?.name}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email do Usuário*</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Email do usuário"
              required
            />
            <p className="text-sm text-muted-foreground">
              Se o usuário não existir, um convite será enviado para este email.
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="role">Função no Departamento*</Label>
            <Select value={role} onValueChange={setRole}>
              <SelectTrigger id="role">
                <SelectValue placeholder="Selecione a função" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="manager">Gerente</SelectItem>
                <SelectItem value="member">Membro</SelectItem>
                <SelectItem value="viewer">Visualizador</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {error && <p className="text-red-500 text-sm">{error}</p>}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleAddUser} disabled={loading}>
            {loading ? "Adicionando..." : "Adicionar"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
