import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useFinancialStore } from "@/store/useFinancialStore";
import { Label } from "@/components/ui/label";

interface NovaTransacaoDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
}

export function NovaTransacaoDialog({ open, onOpenChange, clubId }: NovaTransacaoDialogProps) {
  const [description, setDescription] = useState("");
  const [amount, setAmount] = useState("");
  const [category, setCategory] = useState("salários");
  const [type, setType] = useState("despesa");
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [paymentStatus, setPaymentStatus] = useState("pending");
  const { addTransaction, loading } = useFinancialStore();

  const handleSave = async () => {
    if (!description || !amount || !date) {
      return;
    }

    // Converte o valor para número
    const numericAmount = parseFloat(amount);

    if (isNaN(numericAmount)) {
      return;
    }

    try {
      console.log("Adicionando transação:", {
        description,
        amount: numericAmount,
        category,
        type,
        date,
        club_id: clubId,
        payment_status: type === 'receita' ? 'paid' : paymentStatus
      });

      await addTransaction(clubId, {
        description,
        amount: numericAmount,
        category,
        type,
        date,
        club_id: clubId,
        payment_status: type === 'receita' ? 'paid' : paymentStatus
      });

      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao adicionar transação:", error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Nova Transação Financeira</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <Label htmlFor="description">Descrição</Label>
            <Input id="description" value={description} onChange={e => setDescription(e.target.value)} placeholder="Descrição da transação" />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="amount">Valor</Label>
              <Input
                id="amount"
                value={amount}
                onChange={e => setAmount(e.target.value)}
                placeholder="0,00"
                type="number"
                step="0.01"
              />
            </div>

            <div>
              <Label htmlFor="date">Data</Label>
              <Input id="date" type="date" value={date} onChange={e => setDate(e.target.value)} />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="category">Categoria</Label>
              <Select value={category} onValueChange={setCategory}>
                <SelectTrigger id="category">
                  <SelectValue placeholder="Categoria" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="salários">Salários</SelectItem>
                  <SelectItem value="transferências">Transferências</SelectItem>
                  <SelectItem value="ingressos">Ingressos</SelectItem>
                  <SelectItem value="patrocínios">Patrocínios</SelectItem>
                  <SelectItem value="operacional">Operacional</SelectItem>
                  <SelectItem value="outros">Outros</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="type">Tipo</Label>
              <Select value={type} onValueChange={setType}>
                <SelectTrigger id="type">
                  <SelectValue placeholder="Tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="receita">Receita</SelectItem>
                  <SelectItem value="despesa">Despesa</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {type === 'despesa' && (
            <div>
              <Label htmlFor="payment_status">Status de Pagamento</Label>
              <Select value={paymentStatus} onValueChange={setPaymentStatus}>
                <SelectTrigger id="payment_status">
                  <SelectValue placeholder="Status de Pagamento" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pendente</SelectItem>
                  <SelectItem value="paid">Pago</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleSave} disabled={!description || !amount || !date || loading}>
            Salvar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
