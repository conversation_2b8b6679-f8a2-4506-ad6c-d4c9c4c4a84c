-- Create administrative_documents table
CREATE TABLE IF NOT EXISTS administrative_documents (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  document_number INTEGER NOT NULL,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  created_by UUID REFERENCES auth.users(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  digital_signature BOOLEAN DEFAULT FALSE,
  signed_by UUID REFERENCES auth.users(id),
  signed_at TIMESTAMP WITH TIME ZONE,
  document_type TEXT NOT NULL -- 'oficio' or 'memorando'
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_administrative_documents_club_id ON administrative_documents(club_id);

-- Create administrative_tasks table (for <PERSON><PERSON><PERSON>)
CREATE TABLE IF NOT EXISTS administrative_tasks (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  responsible UUID REFERENCES auth.users(id),
  due_date TIMESTAMP WITH TIME ZONE,
  status TEXT NOT NULL DEFAULT 'a_fazer', -- 'a_fazer', 'em_andamento', 'concluido'
  created_by UUID REFERENCES auth.users(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_administrative_tasks_club_id ON administrative_tasks(club_id);
CREATE INDEX IF NOT EXISTS idx_administrative_tasks_status ON administrative_tasks(status);

-- Create administrative_reminders table
CREATE TABLE IF NOT EXISTS administrative_reminders (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  reminder_date TIMESTAMP WITH TIME ZONE NOT NULL,
  completed BOOLEAN DEFAULT FALSE,
  created_by UUID REFERENCES auth.users(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  reminder_type TEXT NOT NULL -- 'activity', 'document', 'email', 'meeting'
);

-- Create club_form_templates table for customizable forms
CREATE TABLE IF NOT EXISTS club_form_templates (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  content TEXT NOT NULL, -- HTML content with rich text
  form_type TEXT NOT NULL, -- 'pre_registration', 'housing', 'liability_waiver', 'custom'
  is_active BOOLEAN DEFAULT TRUE,
  created_by UUID REFERENCES auth.users(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(club_id, form_type, name)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_administrative_reminders_club_id ON administrative_reminders(club_id);
CREATE INDEX IF NOT EXISTS idx_administrative_reminders_date ON administrative_reminders(reminder_date);

-- Create indexes for club_form_templates
CREATE INDEX IF NOT EXISTS idx_club_form_templates_club_id ON club_form_templates(club_id);
CREATE INDEX IF NOT EXISTS idx_club_form_templates_type ON club_form_templates(form_type);
CREATE INDEX IF NOT EXISTS idx_club_form_templates_active ON club_form_templates(is_active);

-- Create sequence for document numbering
CREATE SEQUENCE IF NOT EXISTS administrative_document_number_seq;

-- Create function to automatically generate document numbers
CREATE OR REPLACE FUNCTION generate_document_number()
RETURNS TRIGGER AS $$
BEGIN
  -- Get the next value from the sequence
  NEW.document_number := nextval('administrative_document_number_seq');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically generate document numbers
CREATE TRIGGER set_document_number
BEFORE INSERT ON administrative_documents
FOR EACH ROW
EXECUTE FUNCTION generate_document_number();

-- Add permissions for administrative module
INSERT INTO permissions (name, description, category)
VALUES
  ('administrative.view', 'Ver módulo administrativo', 'administrative'),
  ('administrative.documents.create', 'Criar documentos administrativos', 'administrative'),
  ('administrative.documents.edit', 'Editar documentos administrativos', 'administrative'),
  ('administrative.documents.delete', 'Excluir documentos administrativos', 'administrative'),
  ('administrative.documents.sign', 'Assinar documentos administrativos', 'administrative'),
  ('administrative.tasks.create', 'Criar tarefas administrativas', 'administrative'),
  ('administrative.tasks.edit', 'Editar tarefas administrativas', 'administrative'),
  ('administrative.tasks.delete', 'Excluir tarefas administrativas', 'administrative'),
  ('administrative.reminders.create', 'Criar lembretes administrativos', 'administrative'),
  ('administrative.reminders.edit', 'Editar lembretes administrativos', 'administrative'),
  ('administrative.reminders.delete', 'Excluir lembretes administrativos', 'administrative')
ON CONFLICT (name) DO NOTHING;
