import { useState, useEffect } from "react";
import { Bell } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { useUser } from "@/context/UserContext";
import { useCurrentClubId } from "@/context/ClubContext";
import { getMedicalNotifications, markMedicalNotificationAsRead, markAllMedicalNotificationsAsRead, countUnreadMedicalNotifications, Notification } from "@/api/api";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";

export function NotificationsBell() {
  const { user } = useUser();
  const clubId = useCurrentClubId();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);

  // Buscar notificações quando o componente for montado
  useEffect(() => {
    if (user?.id && clubId) {
      fetchNotifications();
      fetchUnreadCount();

      // Atualizar a cada 5 minutos
      const interval = setInterval(() => {
        fetchUnreadCount();
      }, 5 * 60 * 1000);

      return () => clearInterval(interval);
    }
  }, [user?.id, clubId]);

  // Buscar notificações quando o popover for aberto
  useEffect(() => {
    if (open && user?.id && clubId) {
      fetchNotifications();
    }
  }, [open]);

  // Buscar notificações
  const fetchNotifications = async () => {
    if (!user?.id || !clubId) return;

    try {
      setLoading(true);
      const data = await getMedicalNotifications(user.id, clubId, 20);
      setNotifications(data);
    } catch (error) {
      console.error("Erro ao buscar notificações:", error);
    } finally {
      setLoading(false);
    }
  };

  // Buscar contagem de notificações não lidas
  const fetchUnreadCount = async () => {
    if (!user?.id || !clubId) return;

    try {
      const count = await countUnreadMedicalNotifications(user.id, clubId);
      setUnreadCount(count);
    } catch (error) {
      console.error("Erro ao contar notificações não lidas:", error);
    }
  };

  // Marcar notificação como lida
  const handleMarkAsRead = async (notificationId: number) => {
    if (!user?.id) return;

    try {
      await markMedicalNotificationAsRead(notificationId, user.id);

      // Atualizar a lista de notificações
      setNotifications(notifications.map(notification =>
        notification.id === notificationId
          ? { ...notification, read: true }
          : notification
      ));

      // Atualizar a contagem de não lidas
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error("Erro ao marcar notificação como lida:", error);
    }
  };

  // Marcar todas as notificações como lidas
  const handleMarkAllAsRead = async () => {
    if (!user?.id || !clubId) return;

    try {
      await markAllMedicalNotificationsAsRead(user.id, clubId);

      // Atualizar a lista de notificações
      setNotifications(notifications.map(notification => ({ ...notification, read: true })));

      // Atualizar a contagem de não lidas
      setUnreadCount(0);
    } catch (error) {
      console.error("Erro ao marcar todas as notificações como lidas:", error);
    }
  };

  // Formatar data relativa
  const formatRelativeDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), {
        addSuffix: true,
        locale: ptBR
      });
    } catch (error) {
      return dateString;
    }
  };

  // Obter ícone para o tipo de notificação
  const getNotificationIcon = (type?: string) => {
    switch (type) {
      case "rehab_session":
        return "🏥";
      case "medical_record":
        return "📋";
      case "system":
        return "⚙️";
      default:
        return "📢";
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
            >
              {unreadCount > 9 ? "9+" : unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <div className="flex items-center justify-between p-4">
          <h4 className="font-medium">Notificações</h4>
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              className="text-xs h-8"
              onClick={handleMarkAllAsRead}
            >
              Marcar todas como lidas
            </Button>
          )}
        </div>
        <Separator />
        <ScrollArea className="h-[300px]">
          {loading ? (
            <div className="flex items-center justify-center h-20">
              <p className="text-sm text-muted-foreground">Carregando...</p>
            </div>
          ) : notifications.length === 0 ? (
            <div className="flex items-center justify-center h-20">
              <p className="text-sm text-muted-foreground">Nenhuma notificação</p>
            </div>
          ) : (
            <div>
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-4 hover:bg-accent/50 cursor-pointer ${notification.read ? "" : "bg-accent/20"}`}
                  onClick={() => !notification.read && handleMarkAsRead(notification.id)}
                >
                  <div className="flex items-start gap-3">
                    <div className="text-xl">{getNotificationIcon(notification.type)}</div>
                    <div className="space-y-1 flex-1">
                      <p className="text-sm font-medium leading-none">{notification.title}</p>
                      <p className="text-sm text-muted-foreground">
                        {notification.message || notification.description}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {formatRelativeDate(notification.created_at)}
                      </p>
                    </div>
                    {!notification.read && (
                      <div className="h-2 w-2 rounded-full bg-primary mt-1" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
}
