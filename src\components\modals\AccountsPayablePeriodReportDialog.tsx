import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { toast } from "@/hooks/use-toast";
import { jsPDF } from "jspdf";
import autoTable from 'jspdf-autotable';
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { useCurrentClubId } from "@/context/ClubContext";
import { getClubInfo } from "@/api";
import { getPendingPayablesByPeriod, ConsolidatedPayable } from "@/api/financialReports";
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

interface AccountsPayablePeriodReportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// Interface for jsPDF with autoTable
interface jsPDFWithAutoTable extends jsPDF {
  autoTable: typeof autoTable;
  internal: {
    getNumberOfPages: () => number;
    pageSize: {
      width: number;
      height: number;
    };
  };
}

export function AccountsPayablePeriodReportDialog({
  open,
  onOpenChange
}: AccountsPayablePeriodReportDialogProps) {
  const [startDate, setStartDate] = useState<Date>(new Date());
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [loading, setLoading] = useState(false);
  const clubId = useCurrentClubId();

  const handleGenerate = async () => {
    if (!clubId) {
      toast({
        title: "Erro",
        description: "ID do clube não encontrado.",
        variant: "destructive",
      });
      return;
    }

    if (!startDate || !endDate) {
      toast({
        title: "Erro",
        description: "Por favor, selecione as datas de início e fim.",
        variant: "destructive",
      });
      return;
    }

    if (startDate > endDate) {
      toast({
        title: "Erro",
        description: "A data de início deve ser anterior à data de fim.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);

    try {
      // Get club info
      const clubInfo = await getClubInfo(clubId);

      // Get payables for the period
      const startDateStr = format(startDate, "yyyy-MM-dd");
      const endDateStr = format(endDate, "yyyy-MM-dd");
      const payables = await getPendingPayablesByPeriod(clubId, startDateStr, endDateStr);

      // Group by department
      const payablesByDepartment: Record<string, ConsolidatedPayable[]> = {};
      payables.forEach(payable => {
        const dept = payable.department || 'Outros';
        if (!payablesByDepartment[dept]) {
          payablesByDepartment[dept] = [];
        }
        payablesByDepartment[dept].push(payable);
      });

      // Create PDF
      const doc = new jsPDF("portrait", "mm", "a4") as jsPDFWithAutoTable;

      // Add header
      const pageWidth = doc.internal.pageSize.width;
      doc.setFontSize(18);
      doc.text("Relatório de Contas a Pagar por Período", pageWidth / 2, 15, { align: "center" });

      // Add club info
      doc.setFontSize(12);
      doc.text(clubInfo.name, pageWidth / 2, 25, { align: "center" });
      if (clubInfo.address) {
        doc.setFontSize(10);
        doc.text(clubInfo.address, pageWidth / 2, 30, { align: "center" });
      }

      // Add period and generation date
      doc.setFontSize(10);
      doc.text(`Período: ${format(startDate, "dd/MM/yyyy", { locale: ptBR })} a ${format(endDate, "dd/MM/yyyy", { locale: ptBR })}`, 15, 40);
      doc.text(`Gerado em: ${format(new Date(), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}`, 15, 45);

      let currentY = 55;

      // Add summary
      const totalAmount = payables.reduce((sum, p) => sum + p.amount, 0);
      const totalCount = payables.length;

      doc.setFontSize(12);
      doc.text("Resumo do Período:", 15, currentY);
      currentY += 8;

      doc.setFontSize(10);
      doc.text(`Total de Contas: ${totalCount}`, 15, currentY);
      currentY += 5;
      doc.text(`Valor Total: R$ ${totalAmount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, 15, currentY);
      currentY += 15;

      if (payables.length === 0) {
        doc.setFontSize(12);
        doc.text("Nenhuma conta a pagar encontrada para o período selecionado.", 15, currentY);
      } else {
        // Generate tables by department
        const sortedDepartments = Object.keys(payablesByDepartment).sort();

        for (const dept of sortedDepartments) {
          const deptPayables = payablesByDepartment[dept];
          const deptTotal = deptPayables.reduce((sum, p) => sum + p.amount, 0);

          // Department header
          doc.setFontSize(14);
          doc.setFillColor(41, 128, 185);
          doc.rect(15, currentY, pageWidth - 30, 8, 'F');
          doc.setTextColor(255, 255, 255);
          doc.text(`${dept} (${deptPayables.length} itens - R$ ${deptTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 })})`, 17, currentY + 5);
          doc.setTextColor(0, 0, 0);
          currentY += 12;

          // Prepare table data
          const tableData = deptPayables.map(payable => [
            payable.name,
            payable.description,
            format(new Date(payable.due_date || payable.transaction_date), "dd/MM/yyyy", { locale: ptBR }),
            payable.pix_key || 'Não informado',
            payable.role || '-',
            `R$ ${payable.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`
          ]);

          // Add table
          autoTable(doc, {
            head: [["Nome", "Descrição", "Vencimento", "Chave PIX", "Função", "Valor"]],
            body: tableData,
            startY: currentY,
            styles: {
              fontSize: 8,
              cellPadding: 2,
            },
            headStyles: {
              fillColor: [52, 152, 219],
              textColor: 255,
              fontStyle: "bold",
            },
            alternateRowStyles: {
              fillColor: [240, 240, 240],
            },
            columnStyles: {
              5: { halign: 'right' }, // Valor
            },
            margin: { left: 15, right: 15 },
          });

          currentY = (doc as any).lastAutoTable.finalY + 10;

          // Check if we need a new page
          if (currentY > 250) {
            doc.addPage();
            currentY = 20;
          }
        }
      }

      // Add page numbers
      const pageCount = doc.internal.getNumberOfPages();
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setFontSize(8);
        doc.text(`Página ${i} de ${pageCount}`, pageWidth / 2, doc.internal.pageSize.height - 10, { align: "center" });
      }

      // Save the PDF
      const reportTitle = `Contas_a_Pagar_${format(startDate, "dd-MM-yyyy")}_a_${format(endDate, "dd-MM-yyyy")}.pdf`;
      doc.save(reportTitle);

      toast({
        title: "Relatório gerado",
        description: "O relatório de contas a pagar por período foi gerado com sucesso.",
      });

      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao gerar relatório:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao gerar o relatório.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Relatório de Contas a Pagar por Período</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="startDate">Data de Início</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !startDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, "dd/MM/yyyy", { locale: ptBR }) : "Selecione a data"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={(date) => date && setStartDate(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div>
              <Label htmlFor="endDate">Data de Fim</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !endDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "dd/MM/yyyy", { locale: ptBR }) : "Selecione a data"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={(date) => date && setEndDate(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleGenerate} disabled={loading}>
            {loading ? "Gerando..." : "Gerar Relatório"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
