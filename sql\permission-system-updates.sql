-- Atualização do Sistema de Permissões

-- 1. <PERSON><PERSON><PERSON><PERSON> que a tabela club_members tenha a coluna de permissões
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'club_members' AND column_name = 'permissions'
    ) THEN
        ALTER TABLE club_members ADD COLUMN permissions JSONB DEFAULT '{}';
    END IF;
END $$;

-- 2. <PERSON><PERSON><PERSON><PERSON> que a tabela users tenha as colunas necessárias
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'role'
    ) THEN
        ALTER TABLE users ADD COLUMN role TEXT DEFAULT 'user';
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'permissions'
    ) THEN
        ALTER TABLE users ADD COLUMN permissions JSONB DEFAULT '{}';
    END IF;
END $$;

-- 3. <PERSON><PERSON><PERSON><PERSON> que a tabela departments exista
CREATE TABLE IF NOT EXISTS departments (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id),
  name TEXT NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Garantir que a tabela user_departments exista
CREATE TABLE IF NOT EXISTS user_departments (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id),
  user_id UUID REFERENCES users(id),
  department_id INTEGER REFERENCES departments(id),
  role TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Garantir que a tabela user_invitations exista
CREATE TABLE IF NOT EXISTS user_invitations (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id),
  email TEXT NOT NULL,
  role TEXT NOT NULL,
  department_id INTEGER REFERENCES departments(id),
  permissions JSONB DEFAULT '{}',
  token TEXT NOT NULL,
  status TEXT DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days')
);

-- 6. Criar índices para melhorar a performance
CREATE INDEX IF NOT EXISTS idx_club_members_club_id ON club_members(club_id);
CREATE INDEX IF NOT EXISTS idx_club_members_user_id ON club_members(user_id);
CREATE INDEX IF NOT EXISTS idx_departments_club_id ON departments(club_id);
CREATE INDEX IF NOT EXISTS idx_user_departments_club_id ON user_departments(club_id);
CREATE INDEX IF NOT EXISTS idx_user_departments_user_id ON user_departments(user_id);
CREATE INDEX IF NOT EXISTS idx_user_departments_department_id ON user_departments(department_id);
CREATE INDEX IF NOT EXISTS idx_user_invitations_club_id ON user_invitations(club_id);
CREATE INDEX IF NOT EXISTS idx_user_invitations_email ON user_invitations(email);
CREATE INDEX IF NOT EXISTS idx_user_invitations_token ON user_invitations(token);

-- 7. Criar políticas de segurança para RLS (Row Level Security)
-- Ativar RLS nas tabelas
ALTER TABLE departments ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_departments ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_invitations ENABLE ROW LEVEL SECURITY;

-- Política para departments: usuários só podem ver departamentos do seu clube
CREATE POLICY departments_club_isolation ON departments
    USING (club_id IN (
        SELECT club_id FROM club_members WHERE user_id = auth.uid()
    ));

-- Política para user_departments: usuários só podem ver associações do seu clube
CREATE POLICY user_departments_club_isolation ON user_departments
    USING (club_id IN (
        SELECT club_id FROM club_members WHERE user_id = auth.uid()
    ));

-- Política para user_invitations: usuários só podem ver convites do seu clube
CREATE POLICY user_invitations_club_isolation ON user_invitations
    USING (club_id IN (
        SELECT club_id FROM club_members WHERE user_id = auth.uid()
    ));

-- 8. Criar função para verificar permissões
CREATE OR REPLACE FUNCTION check_permission(
    p_club_id INTEGER,
    p_user_id UUID,
    p_permission TEXT
) RETURNS BOOLEAN AS $$
DECLARE
    v_role TEXT;
    v_permissions JSONB;
    v_user_permissions JSONB;
    v_member_permissions JSONB;
BEGIN
    -- Obter papel e permissões do usuário
    SELECT role, permissions INTO v_role, v_user_permissions
    FROM users
    WHERE id = p_user_id;
    
    -- Obter permissões do membro do clube
    SELECT permissions INTO v_member_permissions
    FROM club_members
    WHERE club_id = p_club_id AND user_id = p_user_id;
    
    -- Combinar permissões
    v_permissions = COALESCE(v_user_permissions, '{}'::JSONB) || COALESCE(v_member_permissions, '{}'::JSONB);
    
    -- Presidente tem todas as permissões
    IF v_role = 'president' THEN
        RETURN TRUE;
    END IF;
    
    -- Administrador tem todas as permissões exceto as específicas de presidente
    IF v_role = 'admin' AND NOT p_permission LIKE 'president.%' THEN
        RETURN TRUE;
    END IF;
    
    -- Verificar permissão específica
    RETURN COALESCE((v_permissions ->> p_permission)::BOOLEAN, FALSE);
END;
$$ LANGUAGE plpgsql;
