import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { Plus, Search, FileText } from "lucide-react";
import { getInventoryProducts, InventoryProduct } from "@/api/api";
import { MedicalInventoryRequestDialog } from "./MedicalInventoryRequestDialog";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

interface MedicalInventoryViewProps {
  clubId: number;
}

export function MedicalInventoryView({ clubId }: MedicalInventoryViewProps) {
  const [products, setProducts] = useState<InventoryProduct[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<InventoryProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [requestDialogOpen, setRequestDialogOpen] = useState(false);
  const { toast } = useToast();

  // Load products when component mounts
  useEffect(() => {
    loadProducts();
  }, [clubId]);

  // Filter products when search term changes
  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredProducts(products);
    } else {
      const term = searchTerm.toLowerCase();
      setFilteredProducts(
        products.filter(
          (product) =>
            product.name.toLowerCase().includes(term) ||
            product.department.toLowerCase().includes(term) ||
            product.description?.toLowerCase().includes(term)
        )
      );
    }
  }, [searchTerm, products]);

  // Load products from API
  const loadProducts = async () => {
    try {
      setLoading(true);
      // Only get products from the "Farmácia" department
      const data = await getInventoryProducts(clubId, "Farmácia");
      
      // Filter out products with quantity <= 0
      const availableProducts = data.filter(product => product.quantity > 0);
      
      setProducts(availableProducts);
      setFilteredProducts(availableProducts);
    } catch (error) {
      console.error("Error loading products:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao carregar os produtos. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Generate a simple inventory report
  const generateReport = () => {
    // Create a new window for the report
    const reportWindow = window.open("", "_blank");
    if (!reportWindow) {
      toast({
        title: "Erro",
        description: "Não foi possível abrir uma nova janela para o relatório. Verifique se o bloqueador de pop-ups está desativado.",
        variant: "destructive",
      });
      return;
    }

    // Generate the report HTML
    const reportHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Relatório de Estoque - Departamento Médico</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          h1 { color: #2563eb; }
          table { width: 100%; border-collapse: collapse; margin-top: 20px; }
          th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
          th { background-color: #f1f5f9; }
          .low-stock { color: #ef4444; }
          .date { margin-bottom: 20px; color: #64748b; }
          .footer { margin-top: 30px; font-size: 12px; color: #64748b; text-align: center; }
        </style>
      </head>
      <body>
        <h1>Relatório de Estoque - Departamento Médico</h1>
        <div class="date">Data: ${format(new Date(), "PPP", { locale: ptBR })}</div>
        
        <table>
          <thead>
            <tr>
              <th>Produto</th>
              <th>Descrição</th>
              <th>Quantidade Disponível</th>
              <th>Localização</th>
            </tr>
          </thead>
          <tbody>
            ${filteredProducts.map(product => `
              <tr ${product.quantity <= 5 ? 'class="low-stock"' : ''}>
                <td>${product.name}</td>
                <td>${product.description || '-'}</td>
                <td>${product.quantity} ${product.unit || 'unidades'}</td>
                <td>${product.location || '-'}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
        
        <div class="footer">
          Relatório gerado em ${format(new Date(), "PPP 'às' HH:mm", { locale: ptBR })}
        </div>
      </body>
      </html>
    `;

    // Write the report to the new window
    reportWindow.document.write(reportHtml);
    reportWindow.document.close();
  };

  return (
    <Card>
      <CardHeader className="pb-0 flex flex-col sm:flex-row justify-between">
        <div>
          <CardTitle>Estoque de Medicamentos</CardTitle>
          <CardDescription>
            Visualize os medicamentos disponíveis na farmácia
          </CardDescription>
        </div>
        <div className="flex justify-end mb-4 gap-2">
          <Button variant="outline" onClick={generateReport}>
            <FileText className="h-4 w-4 mr-2" />
            Relatório
          </Button>
          <Button className="bg-team-blue hover:bg-blue-700" onClick={() => setRequestDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Nova Solicitação
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="flex items-center mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar medicamentos..."
              className="pl-8"
              value={searchTerm}
              onChange={handleSearchChange}
            />
          </div>
        </div>

        {loading ? (
          <div className="text-center py-8">Carregando produtos...</div>
        ) : filteredProducts.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            Nenhum medicamento encontrado.
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Descrição</TableHead>
                  <TableHead>Quantidade</TableHead>
                  <TableHead>Localização</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredProducts.map((product) => (
                  <TableRow key={product.id}>
                    <TableCell className="font-medium">{product.name}</TableCell>
                    <TableCell>{product.description || "-"}</TableCell>
                    <TableCell>
                      {product.quantity} {product.unit || "unidades"}
                    </TableCell>
                    <TableCell>{product.location || "-"}</TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={
                          product.quantity <= 5
                            ? "border-red-200 bg-red-50 text-red-800"
                            : product.quantity <= 10
                            ? "border-amber-200 bg-amber-50 text-amber-800"
                            : "border-green-200 bg-green-50 text-green-800"
                        }
                      >
                        {product.quantity <= 5
                          ? "Estoque baixo"
                          : product.quantity <= 10
                          ? "Estoque médio"
                          : "Estoque adequado"}
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>

      <MedicalInventoryRequestDialog
        open={requestDialogOpen}
        onOpenChange={setRequestDialogOpen}
        clubId={clubId}
        onSuccess={loadProducts}
      />
    </Card>
  );
}
