import { supabase } from "@/integrations/supabase/client";
import { withPermission, withAuditLog } from "./middleware";
import { FINANCE_PERMISSIONS } from "@/constants/permissions";

export interface SalaryAdvance {
  id: number;
  club_id: number;
  person_id: number;
  person_type: 'player' | 'collaborator';
  amount: number;
  description?: string;
  advance_date: string;
  created_at?: string;
  created_by?: string;
  month: number;
  year: number;
  status: 'active' | 'cancelled';
  payment_method?: string;
  receipt_url?: string;
  // Campos adicionais para exibição (não estão no banco de dados)
  person_name?: string;
  person_role?: string;
}

/**
 * Obtém adiantamentos de salário (vales) para um clube
 * @param clubId ID do clube
 * @param month Mês (opcional)
 * @param year Ano (opcional)
 * @param personType Tipo de pessoa ('player' ou 'collaborator') (opcional)
 * @param personId ID da pessoa (opcional)
 * @returns Lista de adiantamentos
 */
export async function getSalaryAdvances(
  clubId: number,
  month?: number,
  year?: number,
  personType?: 'player' | 'collaborator',
  personId?: number | string
): Promise<SalaryAdvance[]> {
  try {
    let query = supabase
      .from("salary_advances")
      .select("*")
      .eq("club_id", clubId)
      .eq("status", "active");

    if (month) {
      query = query.eq("month", month);
    }

    if (year) {
      query = query.eq("year", year);
    }

    if (personType) {
      query = query.eq("person_type", personType);
    }

    if (personId) {
      // Usar person_id_str para buscar por ID
      const personIdStr = personId.toString();
      query = query.eq("person_id_str", personIdStr);
    }

    const { data, error } = await query.order("advance_date", { ascending: false });

    if (error) {
      throw new Error(`Erro ao obter adiantamentos: ${error.message}`);
    }

    // Buscar nomes das pessoas
    const advances = data || [];
    if (advances.length > 0) {
      const playerIds = advances
        .filter(adv => adv.person_type === 'player')
        .map(adv => adv.person_id);

      const collaboratorIds = advances
        .filter(adv => adv.person_type === 'collaborator')
        .map(adv => adv.person_id);

      // Buscar nomes dos jogadores
      if (playerIds.length > 0) {
        const { data: players, error: playersError } = await supabase
          .from("players")
          .select("id, name")
          .eq("club_id", clubId)
          .in("id", playerIds);

        if (!playersError && players) {
          const playerMap = new Map(players.map(p => [p.id, p.name]));

          advances.forEach(adv => {
            if (adv.person_type === 'player' && playerMap.has(adv.person_id)) {
              adv.person_name = playerMap.get(adv.person_id);
              adv.person_role = 'Atleta';
            }
          });
        }
      }

      // Buscar nomes dos colaboradores
      if (collaboratorIds.length > 0) {
        const { data: collaborators, error: collabError } = await supabase
          .from("collaborators")
          .select("id, full_name, role")
          .eq("club_id", clubId)
          .in("id", collaboratorIds);

        if (!collabError && collaborators) {
          const collabMap = new Map(collaborators.map(c => [c.id, { name: c.full_name, role: c.role }]));

          advances.forEach(adv => {
            if (adv.person_type === 'collaborator' && collabMap.has(adv.person_id)) {
              const collab = collabMap.get(adv.person_id);
              adv.person_name = collab?.name;
              adv.person_role = collab?.role;
            }
          });
        }
      }
    }

    return advances;
  } catch (error: any) {
    console.error("Erro ao obter adiantamentos:", error);
    throw new Error(error.message || "Erro ao obter adiantamentos");
  }
}

/**
 * Cria um novo adiantamento de salário (vale)
 * @param clubId ID do clube
 * @param userId ID do usuário
 * @param advance Dados do adiantamento
 * @returns Adiantamento criado
 */
export async function createSalaryAdvance(
  clubId: number,
  userId: string,
  advance: Omit<SalaryAdvance, 'id' | 'club_id' | 'created_at' | 'created_by'>
): Promise<SalaryAdvance> {
  return withPermission(
    clubId,
    userId,
    FINANCE_PERMISSIONS.EDIT,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "financial.salary_advance.create",
        {
          person_id: advance.person_id,
          person_type: advance.person_type,
          amount: advance.amount
        },
        async () => {
          // Converter person_id para número e string
          const personIdNum = typeof advance.person_id === 'string'
            ? parseInt(advance.person_id, 10)
            : advance.person_id;

          // Se a conversão falhar, usamos um valor numérico padrão
          const validPersonIdNum = isNaN(personIdNum) ? 0 : personIdNum;

          // Sempre armazenar a versão string do ID
          const personIdStr = advance.person_id.toString();

          const { data, error } = await supabase
            .from("salary_advances")
            .insert({
              club_id: clubId,
              ...advance,
              person_id: validPersonIdNum,
              person_id_str: personIdStr,
              created_by: userId
            })
            .select()
            .single();

          if (error) {
            throw new Error(`Erro ao criar adiantamento: ${error.message}`);
          }

          // NÃO criar transação financeira automaticamente
          // Os vales são apenas descontos do salário, não transações separadas

          return data;
        }
      );
    }
  );
}

/**
 * Cancela um adiantamento de salário (vale)
 * @param clubId ID do clube
 * @param userId ID do usuário
 * @param advanceId ID do adiantamento
 * @returns true se o adiantamento foi cancelado com sucesso
 */
export async function cancelSalaryAdvance(
  clubId: number,
  userId: string,
  advanceId: number
): Promise<boolean> {
  return withPermission(
    clubId,
    userId,
    FINANCE_PERMISSIONS.EDIT,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "financial.salary_advance.cancel",
        { advance_id: advanceId },
        async () => {
          // Usar as tipagens corretas para o Supabase
          const { error } = await supabase
            .from("salary_advances")
            .update({ status: "cancelled" } as any)
            .eq("id", advanceId as any)
            .eq("club_id", clubId as any);

          if (error) {
            throw new Error(`Erro ao cancelar adiantamento: ${error.message}`);
          }

          return true;
        }
      );
    }
  );
}

/**
 * Obtém o total de adiantamentos para uma pessoa em um mês/ano específico
 * @param clubId ID do clube
 * @param personType Tipo de pessoa ('player' ou 'collaborator')
 * @param personId ID da pessoa
 * @param month Mês
 * @param year Ano
 * @returns Total de adiantamentos
 */
export async function getTotalAdvances(
  clubId: number,
  personType: 'player' | 'collaborator',
  personId: number | string,
  month: number,
  year: number
): Promise<number> {
  try {
    // Converter personId para string para garantir compatibilidade
    const personIdStr = personId.toString();

    // Buscar adiantamentos usando a string do ID
    const { data, error } = await supabase
      .from("salary_advances")
      .select("amount")
      .eq("club_id", clubId as any)
      .eq("person_type", personType as any)
      .eq("person_id_str", personIdStr as any) // Usar o campo person_id_str em vez de person_id
      .eq("month", month as any)
      .eq("year", year as any)
      .eq("status", "active" as any);

    if (error) {
      throw new Error(`Erro ao obter total de adiantamentos: ${error.message}`);
    }

    return (data || []).reduce((total, item) => total + ((item as any).amount || 0), 0);
  } catch (error) {
    console.error("Erro ao obter total de adiantamentos:", error);
    return 0;
  }
}
