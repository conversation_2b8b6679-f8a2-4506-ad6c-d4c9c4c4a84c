-- Script para adicionar permissões de convocação aos usuários existentes

-- Adicionar permissões de convocação para presidentes e administradores
UPDATE club_members
SET permissions = permissions || '{
  "callups.view": true,
  "callups.create": true,
  "callups.edit": true,
  "callups.delete": true
}'::jsonb
WHERE role IN ('president', 'admin');

-- Adicionar permissões de convocação para gerentes
UPDATE club_members
SET permissions = permissions || '{
  "callups.view": true,
  "callups.create": true,
  "callups.edit": true
}'::jsonb
WHERE role = 'manager';

-- Adicionar permissões de convocação para treinadores
UPDATE club_members
SET permissions = permissions || '{
  "callups.view": true,
  "callups.create": true,
  "callups.edit": true
}'::jsonb
WHERE role = 'coach';

-- Adici<PERSON>r permis<PERSON><PERSON> de visualização para staff
UPDATE club_members
SET permissions = permissions || '{
  "callups.view": true
}'::jsonb
WHERE role = 'staff';

-- Mensagem de conclusão
DO $$
BEGIN
    RAISE NOTICE 'Permissões de convocação adicionadas com sucesso!';
END $$;
