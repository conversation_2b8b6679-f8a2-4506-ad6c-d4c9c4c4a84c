import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

interface ReportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onGenerate: (type: "month" | "all" | "payroll" | "department" | "accounts_payable" | "accounts_payable_period", month?: number, year?: number) => void;
  currentMonth: number;
  currentYear: number;
}

export function ReportDialog({ open, onOpenChange, onGenerate, currentMonth, currentYear }: ReportDialogProps) {
  const [reportType, setReportType] = useState<"month" | "all" | "payroll" | "department" | "accounts_payable" | "accounts_payable_period">("month");
  const [selectedMonth, setSelectedMonth] = useState(currentMonth);
  const [selectedYear, setSelectedYear] = useState(currentYear);

  const months = [
    "Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho",
    "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"
  ];

  const handleGenerate = () => {
    onGenerate(reportType, selectedMonth, selectedYear);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Gerar Relatório Financeiro</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <RadioGroup value={reportType} onValueChange={(value) => setReportType(value as "month" | "all" | "payroll" | "department" | "accounts_payable" | "accounts_payable_period")}>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="month" id="month" />
              <Label htmlFor="month">Relatório Financeiro Mensal</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="payroll" id="payroll" />
              <Label htmlFor="payroll">Relatório de Folha de Pagamento</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="accounts_payable" id="accounts_payable" />
              <Label htmlFor="accounts_payable">Relatório de Contas a Pagar (Geral)</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="accounts_payable_period" id="accounts_payable_period" />
              <Label htmlFor="accounts_payable_period">Relatório de Contas a Pagar (Por Período)</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="department" id="department" />
              <Label htmlFor="department">Relatório Financeiro por Departamento</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="all" id="all" />
              <Label htmlFor="all">Relatório Financeiro Completo</Label>
            </div>
          </RadioGroup>

          {(reportType === "month" || reportType === "payroll" || reportType === "department" || reportType === "accounts_payable_period") && (
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div>
                <Label htmlFor="month-select">Mês</Label>
                <select
                  id="month-select"
                  className="w-full h-10 rounded-md border border-input bg-background px-3 py-2 mt-1"
                  value={selectedMonth}
                  onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                >
                  {months.map((month, index) => (
                    <option key={index} value={index}>{month}</option>
                  ))}
                </select>
              </div>
              <div>
                <Label htmlFor="year-select">Ano</Label>
                <select
                  id="year-select"
                  className="w-full h-10 rounded-md border border-input bg-background px-3 py-2 mt-1"
                  value={selectedYear}
                  onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                >
                  {Array.from({ length: 10 }, (_, i) => selectedYear - 5 + i).map(year => (
                    <option key={year} value={year}>{year}</option>
                  ))}
                </select>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleGenerate}>
            Gerar Relatório
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
