-- Create callups table
CREATE TABLE callups (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  category_id INTEGER REFERENCES categories(id) NOT NULL,
  tournament_type TEXT NOT NULL,
  match_date TIMESTAMP WITH TIME ZONE NOT NULL,
  home_club_logo TEXT,
  away_club_logo TEXT,
  competition_image TEXT,
  match_location TEXT NOT NULL,
  hotel_image TEXT,
  bus_image TEXT,
  uniform_image TEXT,
  match_schedule TEXT,
  hotel_control TEXT,
  sponsor_image1 TEXT,
  sponsor_image2 TEXT,
  sponsor_image3 TEXT,
  sponsor_image4 TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create callup_players table
CREATE TABLE callup_players (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  callup_id INTEGER REFERENCES callups(id) NOT NULL,
  player_id UUID REFERENCES players(id),
  user_id UUID REFERENCES auth.users(id),
  role TEXT NOT NULL, -- 'player', 'coach', 'assistant_coach', etc.
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(callup_id, player_id),
  UNIQUE(callup_id, user_id)
);

-- Add RLS policies for callups table
ALTER TABLE callups ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Club members can view their own callups"
  ON callups
  FOR SELECT
  USING (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

CREATE POLICY "Club admins can insert callups"
  ON callups
  FOR INSERT
  WITH CHECK (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

CREATE POLICY "Club admins can update their own callups"
  ON callups
  FOR UPDATE
  USING (club_id = (auth.jwt() ->> 'club_id')::INTEGER)
  WITH CHECK (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

CREATE POLICY "Club admins can delete their own callups"
  ON callups
  FOR DELETE
  USING (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

-- Add RLS policies for callup_players table
ALTER TABLE callup_players ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Club members can view their own callup players"
  ON callup_players
  FOR SELECT
  USING (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

CREATE POLICY "Club admins can insert callup players"
  ON callup_players
  FOR INSERT
  WITH CHECK (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

CREATE POLICY "Club admins can update their own callup players"
  ON callup_players
  FOR UPDATE
  USING (club_id = (auth.jwt() ->> 'club_id')::INTEGER)
  WITH CHECK (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

CREATE POLICY "Club admins can delete their own callup players"
  ON callup_players
  FOR DELETE
  USING (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

-- Add new role field to players table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'players' AND column_name = 'role'
    ) THEN
        ALTER TABLE players ADD COLUMN role TEXT;
    END IF;
END $$;

-- Add new role field to club_members table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'club_members' AND column_name = 'staff_role'
    ) THEN
        ALTER TABLE club_members ADD COLUMN staff_role TEXT;
    END IF;
END $$;
