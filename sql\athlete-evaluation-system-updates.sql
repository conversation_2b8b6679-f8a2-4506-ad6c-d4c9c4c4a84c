-- Add document approval fields to player_evaluation_invitations table
ALTER TABLE player_evaluation_invitations
ADD COLUMN IF NOT EXISTS documents_status TEXT DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS documents_verified_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS documents_verified_by UUID REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS documents_rejection_reason TEXT,
ADD COLUMN IF NOT EXISTS evaluation_status TEXT DEFAULT 'em avaliacao';

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_player_evaluation_invitations_documents_status
ON player_evaluation_invitations(documents_status);

CREATE INDEX IF NOT EXISTS idx_player_evaluation_invitations_evaluation_status
ON player_evaluation_invitations(evaluation_status);

-- Add approval workflow fields to player_evaluations table
ALTER TABLE player_evaluations
ADD COLUMN IF NOT EXISTS approved_by_manager UUID REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS approved_by_president UUID REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS manager_approved_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS president_approved_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS manager_notes TEXT,
ADD COLUMN IF NOT EXISTS president_notes TEXT,
ADD COLUMN IF NOT EXISTS requires_manager_approval BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS requires_president_approval BOOLEAN DEFAULT true;

-- Create indexes for approval workflow
CREATE INDEX IF NOT EXISTS idx_player_evaluations_approved_by_manager
ON player_evaluations(approved_by_manager);

CREATE INDEX IF NOT EXISTS idx_player_evaluations_approved_by_president
ON player_evaluations(approved_by_president);

CREATE INDEX IF NOT EXISTS idx_player_evaluations_status_approval
ON player_evaluations(status, approved_by_manager, approved_by_president);

-- Update existing evaluations to not require approval (backward compatibility)
UPDATE player_evaluations
SET requires_manager_approval = false,
    requires_president_approval = false
WHERE status IN ('released', 'approved', 'monitored');

-- Add comment to explain new fields
COMMENT ON COLUMN player_evaluation_invitations.documents_status IS 'Status of document verification: pending, approved, rejected';
COMMENT ON COLUMN player_evaluation_invitations.documents_verified_at IS 'When the documents were verified';
COMMENT ON COLUMN player_evaluation_invitations.documents_verified_by IS 'Who verified the documents';
COMMENT ON COLUMN player_evaluation_invitations.documents_rejection_reason IS 'Reason for document rejection';
COMMENT ON COLUMN player_evaluation_invitations.evaluation_status IS 'Status of evaluation: em avaliacao, aprovado, disponivel';

-- Create function to update player status when evaluation status changes
CREATE OR REPLACE FUNCTION update_player_status_on_evaluation_status_change()
RETURNS TRIGGER AS $$
BEGIN
  -- Update player status when evaluation status changes to 'disponivel'
  IF NEW.evaluation_status = 'disponivel' AND
     (OLD.evaluation_status IS NULL OR OLD.evaluation_status <> 'disponivel') THEN

    UPDATE players
    SET status = 'disponivel'
    WHERE id = NEW.player_id;

  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update player status
DROP TRIGGER IF EXISTS trigger_update_player_status_on_evaluation_status_change
ON player_evaluation_invitations;

CREATE TRIGGER trigger_update_player_status_on_evaluation_status_change
AFTER UPDATE OF evaluation_status ON player_evaluation_invitations
FOR EACH ROW
EXECUTE FUNCTION update_player_status_on_evaluation_status_change();

-- Create function to log evaluation status changes
CREATE OR REPLACE FUNCTION log_evaluation_status_change()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert log entry when evaluation status changes
  IF NEW.evaluation_status <> OLD.evaluation_status THEN
    INSERT INTO audit_logs (
      club_id,
      user_id,
      action,
      entity_type,
      entity_id,
      details
    ) VALUES (
      NEW.club_id,
      auth.uid(),
      'evaluation_status_change',
      'player_evaluation_invitations',
      NEW.id,
      jsonb_build_object(
        'old_status', OLD.evaluation_status,
        'new_status', NEW.evaluation_status,
        'player_id', NEW.player_id
      )
    );
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to log evaluation status changes
DROP TRIGGER IF EXISTS trigger_log_evaluation_status_change
ON player_evaluation_invitations;

CREATE TRIGGER trigger_log_evaluation_status_change
AFTER UPDATE OF evaluation_status ON player_evaluation_invitations
FOR EACH ROW
EXECUTE FUNCTION log_evaluation_status_change();
