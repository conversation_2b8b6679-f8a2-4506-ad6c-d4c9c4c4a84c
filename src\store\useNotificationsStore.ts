import { create } from "zustand";
import { Notification, getUserNotifications, markNotificationAsRead, deleteNotification, createNotification } from "@/api/api";

interface NotificationsState {
  notifications: Notification[];
  loading: boolean;
  error: string | null;
  fetchNotifications: (userId: string) => Promise<void>;
  markAsRead: (id: number) => Promise<void>;
  deleteNotification: (id: number) => Promise<void>;
  createNotification: (data: { user_id: string; club_id?: number; title: string; description?: string }) => Promise<void>;
}

export const useNotificationsStore = create<NotificationsState>((set, get) => ({
  notifications: [],
  loading: false,
  error: null,

  fetchNotifications: async (userId: string) => {
    set({ loading: true, error: null });
    try {
      const data = await getUserNotifications(userId);
      set({ notifications: data, loading: false });
    } catch (e: any) {
      set({ error: e.message, loading: false });
    }
  },

  markAsRead: async (id: number) => {
    try {
      await markNotificationAsRead(id);
      set({ notifications: get().notifications.map(n => n.id === id ? { ...n, read: true } : n) });
    } catch (e: any) {
      set({ error: e.message });
    }
  },

  deleteNotification: async (id: number) => {
    try {
      await deleteNotification(id);
      set({ notifications: get().notifications.filter(n => n.id !== id) });
    } catch (e: any) {
      set({ error: e.message });
    }
  },

  createNotification: async (data) => {
    try {
      const newNotification = await createNotification(data);
      set({ notifications: [newNotification, ...get().notifications] });
    } catch (e: any) {
      set({ error: e.message });
    }
  },
}));
