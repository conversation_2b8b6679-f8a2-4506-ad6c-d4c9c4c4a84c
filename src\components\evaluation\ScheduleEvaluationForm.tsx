import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { useUser } from "@/context/UserContext";
import { schedulePlayerEvaluation } from "@/api/playerEvaluationInvitations";

interface ScheduleEvaluationFormProps {
  clubId: number;
  player: any;
  onSuccess: () => void;
  onCancel: () => void;
}

export function ScheduleEvaluationForm({
  clubId,
  player,
  onSuccess,
  onCancel
}: ScheduleEvaluationFormProps) {
  const { user } = useUser();
  const { toast } = useToast();

  const [date, setDate] = useState("");
  const [time, setTime] = useState("");
  const [location, setLocation] = useState("");
  const [requirements, setRequirements] = useState("Documento de identidade com foto, chuteira, meião e shorts.");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user?.id) {
      setError("Usuário não autenticado");
      return;
    }

    if (!date || !time || !location) {
      setError("Preencha todos os campos obrigatórios");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Combine date and time
      const dateTime = new Date(`${date}T${time}`);

      // Schedule evaluation
      await schedulePlayerEvaluation(
        clubId,
        player.id,
        dateTime.toISOString(),
        location,
        requirements,
        user.id
      );

      onSuccess();
    } catch (err: any) {
      console.error("Erro ao agendar pré cadastro:", err);
      setError(err.message || "Erro ao agendar pré cadastro");
      toast({
        title: "Erro",
        description: err.message || "Erro ao agendar pré cadastro",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="playerName">Jogador</Label>
        <Input
          id="playerName"
          value={player.name}
          readOnly
          disabled
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="date">Data*</Label>
          <Input
            id="date"
            type="date"
            value={date}
            onChange={(e) => setDate(e.target.value)}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="time">Horário*</Label>
          <Input
            id="time"
            type="time"
            value={time}
            onChange={(e) => setTime(e.target.value)}
            required
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="location">Local*</Label>
        <Input
          id="location"
          value={location}
          onChange={(e) => setLocation(e.target.value)}
          placeholder="Endereço completo do local do pré cadastro"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="requirements">O que levar</Label>
        <Textarea
          id="requirements"
          value={requirements}
          onChange={(e) => setRequirements(e.target.value)}
          placeholder="Itens que o atleta deve levar para o pré cadastro"
          rows={3}
        />
      </div>

      {error && (
        <div className="text-sm font-medium text-destructive">{error}</div>
      )}

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancelar
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? "Agendando..." : "Agendar Avaliação"}
        </Button>
      </div>
    </form>
  );
}
