import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import {
  getPlayerDocuments,
  verifyDocument,
  PlayerDocument,
  DOCUMENT_LABELS,
} from "@/api/api";
import { Check, X, AlertCircle } from "lucide-react";

interface PlayerDocumentViewerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  playerId: string;
  canVerify?: boolean;
}

export function PlayerDocumentViewer({
  open,
  onOpenChange,
  playerId,
  canVerify = false,
}: PlayerDocumentViewerProps) {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const [documents, setDocuments] = useState<PlayerDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedDocument, setSelectedDocument] = useState<PlayerDocument | null>(null);
  const [verifying, setVerifying] = useState(false);
  const [rejectionReason, setRejectionReason] = useState("");
  const [showRejectionForm, setShowRejectionForm] = useState(false);

  // Carregar documentos do jogador
  useEffect(() => {
    const fetchDocuments = async () => {
      if (!playerId) return;

      try {
        setLoading(true);
        const data = await getPlayerDocuments(clubId, playerId);
        setDocuments(data);
      } catch (err: any) {
        console.error("Erro ao carregar documentos:", err);
        toast({
          title: "Erro",
          description: "Não foi possível carregar os documentos",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    if (open) {
      fetchDocuments();
    }
  }, [clubId, playerId, open]);

  // Função para verificar um documento
  const handleVerifyDocument = async (status: "verified" | "rejected") => {
    if (!selectedDocument) return;

    // Se for rejeição, mostrar formulário para informar o motivo
    if (status === "rejected" && !showRejectionForm) {
      setShowRejectionForm(true);
      return;
    }

    // Se for rejeição e o motivo não foi informado, exigir
    if (status === "rejected" && !rejectionReason.trim()) {
      toast({
        title: "Atenção",
        description: "Por favor, informe o motivo da rejeição para que o jogador possa corrigir o documento.",
        variant: "destructive",
      });
      return;
    }

    try {
      setVerifying(true);
      await verifyDocument(
        selectedDocument.id,
        user?.id || "",
        status,
        status === "rejected" ? rejectionReason : undefined
      );

      // Atualizar lista de documentos
      const updatedDocuments = documents.map((doc) =>
        doc.id === selectedDocument.id
          ? {
              ...doc,
              status,
              verified_at: new Date().toISOString(),
              verified_by: user?.id || "",
              rejection_reason: status === "rejected" ? rejectionReason : null
            }
          : doc
      );

      setDocuments(updatedDocuments);

      toast({
        title: "Sucesso",
        description: `Documento ${status === "verified" ? "verificado" : "rejeitado"} com sucesso`,
      });

      // Limpar o formulário e fechar
      setRejectionReason("");
      setShowRejectionForm(false);

      // Fechar visualização do documento
      setSelectedDocument(null);
    } catch (err: any) {
      console.error("Erro ao verificar documento:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao verificar documento",
        variant: "destructive",
      });
    } finally {
      setVerifying(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Documentos do Jogador</DialogTitle>
        </DialogHeader>

        {loading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        ) : documents.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            Nenhum documento encontrado
          </div>
        ) : selectedDocument ? (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">
                {DOCUMENT_LABELS[selectedDocument.document_type] || selectedDocument.document_type}
              </h3>
              <div className="flex space-x-2">
                {canVerify && selectedDocument.status === "pending" && !showRejectionForm && (
                  <>
                    <Button
                      variant="outline"
                      className="bg-green-50 text-green-700 border-green-200 hover:bg-green-100 hover:text-green-800"
                      onClick={() => handleVerifyDocument("verified")}
                      disabled={verifying}
                    >
                      <Check className="h-4 w-4 mr-1" />
                      Aprovar
                    </Button>
                    <Button
                      variant="outline"
                      className="bg-red-50 text-red-700 border-red-200 hover:bg-red-100 hover:text-red-800"
                      onClick={() => handleVerifyDocument("rejected")}
                      disabled={verifying}
                    >
                      <X className="h-4 w-4 mr-1" />
                      Rejeitar
                    </Button>
                  </>
                )}

                {canVerify && selectedDocument.status === "pending" && showRejectionForm && (
                  <>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setShowRejectionForm(false);
                        setRejectionReason("");
                      }}
                      disabled={verifying}
                    >
                      Cancelar
                    </Button>
                    <Button
                      variant="outline"
                      className="bg-red-50 text-red-700 border-red-200 hover:bg-red-100 hover:text-red-800"
                      onClick={() => handleVerifyDocument("rejected")}
                      disabled={verifying}
                    >
                      <X className="h-4 w-4 mr-1" />
                      Confirmar Rejeição
                    </Button>
                  </>
                )}
                <Button
                  variant="outline"
                  onClick={() => setSelectedDocument(null)}
                >
                  Voltar
                </Button>
              </div>
            </div>

            {showRejectionForm && (
              <div className="mb-4 space-y-2">
                <Label htmlFor="rejection-reason">Motivo da Rejeição</Label>
                <Input
                  id="rejection-reason"
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  placeholder="Informe o motivo da rejeição para que o jogador possa corrigir o documento"
                  className="w-full"
                />
                <p className="text-xs text-gray-500">
                  Esta mensagem será exibida para o jogador para que ele possa corrigir o documento.
                </p>
              </div>
            )}

            <div className="border rounded-md p-2 overflow-hidden" style={{ maxHeight: "60vh" }}>
              {selectedDocument.file_url.endsWith(".pdf") ? (
                <iframe
                  src={`https://docs.google.com/viewer?url=${encodeURIComponent(selectedDocument.file_url)}&embedded=true`}
                  className="w-full h-[60vh] border-0"
                  title="Documento"
                  allowFullScreen
                />
              ) : (
                <div className="flex justify-center" style={{ maxHeight: "58vh", overflow: "hidden" }}>
                  <img
                    src={selectedDocument.file_url}
                    alt="Documento"
                    className="max-h-[58vh] object-contain"
                    onClick={(e) => e.preventDefault()}
                    style={{ pointerEvents: "none", display: "block" }}
                  />
                </div>
              )}
            </div>

            <div className="text-sm space-y-2">
              <div className="flex items-center">
                <span className="font-medium mr-2">Status:</span>
                {selectedDocument.status === "pending" && (
                  <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-md text-xs flex items-center">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    Pendente
                  </span>
                )}
                {selectedDocument.status === "verified" && (
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded-md text-xs flex items-center">
                    <Check className="h-3 w-3 mr-1" />
                    Aprovado
                  </span>
                )}
                {selectedDocument.status === "rejected" && (
                  <span className="bg-red-100 text-red-800 px-2 py-1 rounded-md text-xs flex items-center">
                    <X className="h-3 w-3 mr-1" />
                    Rejeitado
                  </span>
                )}
              </div>

              <p className="text-gray-500">Enviado em: {new Date(selectedDocument.uploaded_at).toLocaleString()}</p>

              {selectedDocument.verified_at && (
                <p className="text-gray-500">Verificado em: {new Date(selectedDocument.verified_at).toLocaleString()}</p>
              )}

              {selectedDocument.status === "rejected" && (
                <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="font-medium text-red-700 text-sm">Motivo da Rejeição:</p>
                  {selectedDocument.rejection_reason ? (
                    <p className="text-red-600 mt-1">{selectedDocument.rejection_reason}</p>
                  ) : (
                    <p className="text-red-600 mt-1 italic">Nenhum motivo especificado</p>
                  )}
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {documents.map((document) => (
              <div
                key={document.id}
                className="border rounded-md p-4 cursor-pointer hover:bg-gray-50"
                onClick={() => setSelectedDocument(document)}
              >
                <div className="flex justify-between items-start">
                  <h3 className="font-medium">
                    {DOCUMENT_LABELS[document.document_type] || document.document_type}
                  </h3>
                  <div className="flex items-center">
                    {document.status === "pending" && (
                      <div className="flex items-center bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        Pendente
                      </div>
                    )}
                    {document.status === "verified" && (
                      <div className="flex items-center bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
                        <Check className="h-3 w-3 mr-1" />
                        Aprovado
                      </div>
                    )}
                    {document.status === "rejected" && (
                      <div className="flex items-center bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs">
                        <X className="h-3 w-3 mr-1" />
                        Rejeitado
                      </div>
                    )}
                  </div>
                </div>
                <p className="text-sm text-gray-500 mt-2">
                  Enviado em: {new Date(document.uploaded_at).toLocaleDateString()}
                </p>
                {document.status === "rejected" && (
                  <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
                    <p className="text-xs font-medium text-red-700">Documento Rejeitado</p>
                    {document.rejection_reason ? (
                      <p className="text-xs text-red-600 mt-1">
                        <span className="font-medium">Motivo:</span> {document.rejection_reason}
                      </p>
                    ) : (
                      <p className="text-xs text-red-600 mt-1">Nenhum motivo especificado</p>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
