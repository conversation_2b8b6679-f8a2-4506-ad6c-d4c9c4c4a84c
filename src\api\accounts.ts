import { supabase } from "@/integrations/supabase/client";

export type FinancialAccount = {
  id: number;
  club_id: number;
  description: string;
  type: 'a_pagar' | 'a_receber';
  supplier_client: string;
  creation_date: string;
  due_date: string;
  amount: number;
  status: 'pendente' | 'pago' | 'recebido';
  receipt_url?: string;
  category: string;
  cost_center?: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
};

/**
 * Get all financial accounts for a club
 * @param clubId The club ID
 * @returns Array of financial accounts
 */
export async function getFinancialAccounts(clubId: number): Promise<FinancialAccount[]> {
  try {
    const { data, error } = await supabase
      .from("financial_accounts")
      .select("*")
      .eq("club_id", clubId)
      .order("due_date", { ascending: true });

    if (error) {
      console.error("Error fetching financial accounts:", error);
      throw new Error(`Error fetching financial accounts: ${error.message}`);
    }

    return data as FinancialAccount[];
  } catch (error: any) {
    console.error("Error in getFinancialAccounts:", error);
    throw new Error(error.message || "Error fetching financial accounts");
  }
}

/**
 * Create a new financial account
 * @param clubId The club ID
 * @param account The account data
 * @returns The created account
 */
export async function createFinancialAccount(
  clubId: number,
  account: Omit<FinancialAccount, "id" | "club_id" | "created_at" | "updated_at">
): Promise<FinancialAccount> {
  try {
    const { data, error } = await supabase
      .from("financial_accounts")
      .insert({
        club_id: clubId,
        description: account.description,
        type: account.type,
        supplier_client: account.supplier_client,
        creation_date: account.creation_date,
        due_date: account.due_date,
        amount: account.amount,
        status: account.status || "pendente",
        category: account.category,
        cost_center: account.cost_center,
        notes: account.notes,
      })
      .select()
      .single();

    if (error) {
      console.error("Error creating financial account:", error);
      throw new Error(`Error creating financial account: ${error.message}`);
    }

    // Create a corresponding financial transaction
    const transactionType = account.type === "a_receber" ? "receita" : "despesa";
    const transactionStatus = account.status === "pendente" ? "pendente" : 
                             (account.status === "pago" ? "pago" : "recebido");
    
    // Only create a transaction if the account is paid/received
    if (account.status === "pago" || account.status === "recebido") {
      const { error: transactionError } = await supabase
        .from("financial_transactions")
        .insert({
          club_id: clubId,
          date: new Date().toISOString().split('T')[0], // Current date
          type: transactionType,
          category: account.category,
          amount: account.amount,
          description: `${account.description} - ${account.supplier_client}`,
        });

      if (transactionError) {
        console.error("Error creating financial transaction:", transactionError);
        // Don't throw error, just log it
      }
    }

    return data as FinancialAccount;
  } catch (error: any) {
    console.error("Error in createFinancialAccount:", error);
    throw new Error(error.message || "Error creating financial account");
  }
}

/**
 * Update a financial account
 * @param clubId The club ID
 * @param id The account ID
 * @param account The account data to update
 * @returns The updated account
 */
export async function updateFinancialAccount(
  clubId: number,
  id: number,
  account: Partial<FinancialAccount>
): Promise<FinancialAccount> {
  try {
    // Get the current account to check if status changed
    const { data: currentAccount, error: fetchError } = await supabase
      .from("financial_accounts")
      .select("*")
      .eq("club_id", clubId)
      .eq("id", id)
      .single();

    if (fetchError) {
      console.error("Error fetching current account:", fetchError);
      throw new Error(`Error fetching current account: ${fetchError.message}`);
    }

    const { data, error } = await supabase
      .from("financial_accounts")
      .update(account)
      .eq("club_id", clubId)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      console.error("Error updating financial account:", error);
      throw new Error(`Error updating financial account: ${error.message}`);
    }

    // Check if status changed from pending to paid/received
    const oldStatus = currentAccount.status;
    const newStatus = account.status || oldStatus;
    
    if (oldStatus === "pendente" && (newStatus === "pago" || newStatus === "recebido")) {
      // Create a financial transaction
      const transactionType = data.type === "a_receber" ? "receita" : "despesa";
      
      const { error: transactionError } = await supabase
        .from("financial_transactions")
        .insert({
          club_id: clubId,
          date: new Date().toISOString().split('T')[0], // Current date
          type: transactionType,
          category: data.category,
          amount: data.amount,
          description: `${data.description} - ${data.supplier_client}`,
        });

      if (transactionError) {
        console.error("Error creating financial transaction:", transactionError);
        // Don't throw error, just log it
      }
    }

    return data as FinancialAccount;
  } catch (error: any) {
    console.error("Error in updateFinancialAccount:", error);
    throw new Error(error.message || "Error updating financial account");
  }
}

/**
 * Delete a financial account
 * @param clubId The club ID
 * @param id The account ID
 * @returns True if successful
 */
export async function deleteFinancialAccount(clubId: number, id: number): Promise<boolean> {
  try {
    const { error } = await supabase
      .from("financial_accounts")
      .delete()
      .eq("club_id", clubId)
      .eq("id", id);

    if (error) {
      console.error("Error deleting financial account:", error);
      throw new Error(`Error deleting financial account: ${error.message}`);
    }

    return true;
  } catch (error: any) {
    console.error("Error in deleteFinancialAccount:", error);
    throw new Error(error.message || "Error deleting financial account");
  }
}

/**
 * Upload a receipt for a financial account
 * @param clubId The club ID
 * @param id The account ID
 * @param file The file to upload
 * @returns The updated account with receipt URL
 */
export async function uploadAccountReceipt(
  clubId: number,
  id: number,
  file: File
): Promise<FinancialAccount> {
  try {
    // Get the account to check if it exists
    const { data: account, error: fetchError } = await supabase
      .from("financial_accounts")
      .select("*")
      .eq("club_id", clubId)
      .eq("id", id)
      .single();

    if (fetchError) {
      console.error("Error fetching account:", fetchError);
      throw new Error(`Error fetching account: ${fetchError.message}`);
    }

    // Upload the file to storage
    const fileExt = file.name.split('.').pop();
    const fileName = `${clubId}_${id}_${Date.now()}.${fileExt}`;
    const filePath = `financial_receipts/${fileName}`;

    const { error: uploadError, data: uploadData } = await supabase.storage
      .from("playerdocuments") // Using existing bucket
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (uploadError) {
      console.error("Error uploading receipt:", uploadError);
      throw new Error(`Error uploading receipt: ${uploadError.message}`);
    }

    // Get the public URL
    const { data: urlData } = supabase.storage
      .from("playerdocuments")
      .getPublicUrl(filePath);

    const publicUrl = urlData.publicUrl;

    // Update the account with the receipt URL
    const { data, error } = await supabase
      .from("financial_accounts")
      .update({ receipt_url: publicUrl })
      .eq("club_id", clubId)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      console.error("Error updating account with receipt URL:", error);
      throw new Error(`Error updating account with receipt URL: ${error.message}`);
    }

    return data as FinancialAccount;
  } catch (error: any) {
    console.error("Error in uploadAccountReceipt:", error);
    throw new Error(error.message || "Error uploading account receipt");
  }
}
