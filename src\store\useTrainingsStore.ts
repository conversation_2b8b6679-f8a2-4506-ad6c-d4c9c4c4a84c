import { create } from "zustand";
import {
  getTrainings,
  getCompletedTrainings,
  createTraining,
  updateTraining,
  deleteTraining,
  finalizeTraining,
  type TrainingFinalization
} from "@/api/api";

export type Training = {
  id: number;
  club_id: number;
  name: string;
  type: string;
  date: string;
  time: string;
  location: string;
  status: "concluído" | "em andamento" | "agendado";
  progress: number;
  coach: string;
  participants: number;
  description: string;
  category_id?: number;
  category_name?: string;
  player_ids?: string[]; // IDs dos jogadores associados ao treino
};

interface TrainingsState {
  trainings: Training[];
  completedTrainings: Training[];
  loading: boolean;
  completedLoading: boolean;
  error: string | null;
  fetchTrainings: (clubId: number) => Promise<void>;
  fetchCompletedTrainings: (clubId: number, year?: number, month?: number) => Promise<void>;
  addTraining: (clubId: number, training: Omit<Training, "id">) => Promise<Training>;
  updateTraining: (clubId: number, id: number, training: Partial<Training>) => Promise<void>;
  deleteTraining: (clubId: number, id: number) => Promise<void>;
  finalizeTraining: (clubId: number, id: number, summary: string, description: string) => Promise<void>;
  syncTrainings: (clubId: number) => Promise<void>;
}

export const useTrainingsStore = create<TrainingsState>((set) => ({
  trainings: [],
  completedTrainings: [],
  loading: false,
  completedLoading: false,
  error: null,
  fetchTrainings: async (clubId: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const trainings = await getTrainings(clubId);
      set({ trainings, loading: false });
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao buscar treinos", loading: false });
    }
  },

  fetchCompletedTrainings: async (clubId: number, year?: number, month?: number): Promise<void> => {
    set({ completedLoading: true, error: null });
    try {
      const completedTrainings = await getCompletedTrainings(clubId, year, month);
      set({ completedTrainings, completedLoading: false });
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao buscar treinos concluídos", completedLoading: false });
    }
  },

  addTraining: async (clubId: number, training: Omit<Training, "id">): Promise<Training> => {
    set({ loading: true, error: null });
    try {
      const newTraining = await createTraining(clubId, training);
      set((state) => ({ trainings: [...state.trainings, newTraining], loading: false }));
      return newTraining;
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao adicionar treino", loading: false });
      throw err;
    }
  },

  updateTraining: async (clubId: number, id: number, training: Partial<Training>): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const updated = await updateTraining(clubId, id, training);
      if (updated) {
        set((state) => ({ trainings: state.trainings.map(t => t.id === id ? updated : t), loading: false }));
      } else {
        set({ error: "Treino não encontrado", loading: false });
      }
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao atualizar treino", loading: false });
    }
  },

  deleteTraining: async (clubId: number, id: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const ok = await deleteTraining(clubId, id);
      if (ok) {
        set((state) => ({ trainings: state.trainings.filter(t => t.id !== id), loading: false }));
      } else {
        set({ error: "Treino não encontrado", loading: false });
      }
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao deletar treino", loading: false });
    }
  },

  finalizeTraining: async (clubId: number, id: number, summary: string, description: string): Promise<void> => {
    set({ loading: true, error: null });
    try {
      await finalizeTraining(clubId, id, summary, description);
      set((state) => ({
        trainings: state.trainings.map(t => t.id === id ? { ...t, status: "concluído", progress: 100 } : t),
        loading: false
      }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao finalizar treino", loading: false });
    }
  },

  // NOVAS FUNÇÕES DE SINCRONIZAÇÃO
  syncTrainings: async (clubId: number): Promise<void> => {
    const trainings = await getTrainings(clubId);
    set({ trainings });
  },
}));
