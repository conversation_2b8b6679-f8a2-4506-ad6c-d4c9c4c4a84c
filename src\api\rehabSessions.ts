// Funções de CRUD para reabilitação
import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";
type RehabSession = Database['public']['Tables']['rehab_sessions']['Row'];

export async function getRehabSessions(
  clubId: number,
  options?: { includeArchived?: boolean }
): Promise<RehabSession[]> {
  let query = supabase
    .from("rehab_sessions")
    .select("*")
    .eq("club_id", clubId);

  // Se não for especificado para incluir arquivados, filtra apenas os não arquivados
  if (!options?.includeArchived) {
    query = query.eq("archived", false);
  }

  const { data, error } = await query;
  if (error) throw new Error(`Erro ao buscar sessões: ${error.message}`);
  return data as RehabSession[];
}

export async function createRehabSession(clubId: number, session: Omit<RehabSession, "id">): Promise<RehabSession> {
  const { data, error } = await supabase
    .from("rehab_sessions")
    .insert({ ...session, club_id: clubId })
    .select()
    .single();
  if (error) throw new Error(`Erro ao criar sessão: ${error.message}`);
  return data as RehabSession;
}

export async function updateRehabSession(clubId: number, id: number, session: Partial<RehabSession>): Promise<RehabSession> {
  const { data, error } = await supabase
    .from("rehab_sessions")
    .update(session)
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();
  if (error) throw new Error(`Erro ao atualizar sessão: ${error.message}`);
  return data as RehabSession;
}

export async function deleteRehabSession(clubId: number, id: number): Promise<boolean> {
  const { error } = await supabase
    .from("rehab_sessions")
    .delete()
    .eq("club_id", clubId)
    .eq("id", id);
  if (error) throw new Error(`Erro ao deletar sessão: ${error.message}`);
  return true;
}

/**
 * Arquiva ou desarquiva uma sessão de reabilitação
 * @param clubId ID do clube
 * @param id ID da sessão
 * @param archived Status de arquivamento (true para arquivar, false para desarquivar)
 * @returns Sessão atualizada
 */
export async function archiveRehabSession(clubId: number, id: number, archived: boolean): Promise<RehabSession> {
  const { data, error } = await supabase
    .from("rehab_sessions")
    .update({ archived })
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();
  if (error) throw new Error(`Erro ao ${archived ? 'arquivar' : 'desarquivar'} sessão: ${error.message}`);
  return data as RehabSession;
}
