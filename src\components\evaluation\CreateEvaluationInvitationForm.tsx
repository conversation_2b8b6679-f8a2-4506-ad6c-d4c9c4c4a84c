import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { createPlayerEvaluationInvitation, sendEvaluationInvitationEmail } from "@/api/playerEvaluationInvitations";
import { validateCPF } from "@/api/external";

interface CreateEvaluationInvitationFormProps {
  onSuccess?: () => void;
}

export function CreateEvaluationInvitationForm({ onSuccess }: CreateEvaluationInvitationFormProps) {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { toast } = useToast();
  
  const [email, setEmail] = useState("");
  const [cpf, setCpf] = useState("");
  const [expirationDays, setExpirationDays] = useState(5);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate inputs
    if (!email) {
      setError("O email é obrigatório");
      return;
    }
    
    if (!validateEmail(email)) {
      setError("Email inválido");
      return;
    }
    
    if (cpf && !validateCPF(cpf)) {
      setError("CPF inválido");
      return;
    }
    
    if (!user?.id) {
      setError("Usuário não autenticado");
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      // Create invitation
      const invitation = await createPlayerEvaluationInvitation(
        clubId,
        email,
        cpf || undefined,
        user.id,
        expirationDays
      );
      
      // Get club name
      const { data: clubData } = await supabase
        .from("club_info")
        .select("name")
        .eq("id", clubId)
        .single();
      
      const clubName = clubData?.name || "Game Day Nexus";
      
      // Send invitation email
      await sendEvaluationInvitationEmail(email, invitation.token, clubName);
      
      toast({
        title: "Sucesso",
        description: "Convite para pré cadastro enviado com sucesso",
      });
      
      // Reset form
      setEmail("");
      setCpf("");
      setExpirationDays(5);
      
      // Call onSuccess callback
      if (onSuccess) {
        onSuccess();
      }
    } catch (err: any) {
      console.error("Erro ao criar convite para pré cadastro:", err);
      setError(err.message || "Erro ao criar convite para pré cadastro");
      toast({
        title: "Erro",
        description: err.message || "Erro ao criar convite para pré cadastro",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Criar Convite para pré cadastro</CardTitle>
        <CardDescription>
          Gere um link único para que um atleta ou empresário possa se cadastrar para pré cadastro.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email*</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Email do atleta ou empresário"
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="cpf">CPF (opcional)</Label>
            <Input
              id="cpf"
              value={cpf}
              onChange={(e) => setCpf(e.target.value.replace(/\D/g, ''))}
              placeholder="CPF do atleta"
              maxLength={11}
            />
            <p className="text-xs text-muted-foreground">
              Se informado, o CPF será vinculado ao convite para maior segurança.
            </p>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="expirationDays">Validade do Link (dias)</Label>
            <Input
              id="expirationDays"
              type="number"
              min={1}
              max={30}
              value={expirationDays}
              onChange={(e) => setExpirationDays(parseInt(e.target.value))}
            />
          </div>
          
          {error && (
            <div className="text-sm font-medium text-destructive">{error}</div>
          )}
        </form>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={() => {
          setEmail("");
          setCpf("");
          setExpirationDays(5);
          setError(null);
        }}>
          Limpar
        </Button>
        <Button onClick={handleSubmit} disabled={loading}>
          {loading ? "Enviando..." : "Enviar Convite"}
        </Button>
      </CardFooter>
    </Card>
  );
}

// Helper function to validate email
function validateEmail(email: string): boolean {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(email);
}

// Import supabase client
import { supabase } from "@/integrations/supabase/client";
