-- Script para criar templates padr<PERSON> baseados nas fichas HTML existentes
-- Este script deve ser executado para cada clube que quiser migrar para o sistema de fichas customizáveis

-- Template de Pré-cadastro (baseado na ficha de avaliação)
INSERT INTO club_form_templates (
  club_id,
  name,
  description,
  content,
  form_type,
  is_active,
  created_by
) VALUES (
  1, -- Substitua pelo ID do clube
  'Ficha de Pré-cadastro Padrão',
  'Ficha padrão para coleta de dados básicos do atleta durante o pré-cadastro',
  '<h2 style="text-align: center;">FICHA DE PRÉ-CADASTRO</h2>
  <p><strong>Nome completo:</strong> _________________________________</p>
  <p><strong>Data de nascimento:</strong> ___/___/______</p>
  <p><strong>CPF:</strong> _________________________________</p>
  <p><strong>RG:</strong> _________________________________</p>
  <p><strong>Endereço:</strong> _________________________________</p>
  <p><strong>Cidade:</strong> _________________ <strong>Estado:</strong> _______</p>
  <p><strong>Telefone:</strong> _________________________________</p>
  <p><strong>Email:</strong> _________________________________</p>
  <p><strong>Posição preferida:</strong> _________________________________</p>
  <p><strong>Experiência anterior:</strong> _________________________________</p>
  <p><strong>Clube anterior (se houver):</strong> _________________________________</p>
  <p><strong>Nome do responsável (se menor de idade):</strong> _________________________________</p>
  <p><strong>Telefone do responsável:</strong> _________________________________</p>
  <br>
  <p>Declaro que as informações acima são verdadeiras e estou ciente das regras do clube.</p>
  <br>
  <p>Data: ___/___/______</p>
  <p>Assinatura do atleta: _________________________________</p>
  <p>Assinatura do responsável (se menor): _________________________________</p>',
  'pre_registration',
  true,
  '00000000-0000-0000-0000-000000000000' -- Substitua por um UUID válido de usuário admin
);

-- Template de Moradia
INSERT INTO club_form_templates (
  club_id,
  name,
  description,
  content,
  form_type,
  is_active,
  created_by
) VALUES (
  1, -- Substitua pelo ID do clube
  'Ficha de Moradia Padrão',
  'Autorização para residência em alojamento do clube',
  '<h2 style="text-align: center;">FICHA DE MORADIA</h2>
  <h3 style="text-align: center;">AUTORIZAÇÃO PARA RESIDÊNCIA EM ALOJAMENTO</h3>
  <p>Eu, <u>_________________________________</u>, portador(a) do RG nº <u>_______________</u>, CPF nº <u>_______________</u>, residente e domiciliado(a) à <u>_________________________________</u>, na cidade de <u>_______________</u>, estado de <u>_______________</u>, na qualidade de responsável legal pelo atleta <u>_________________________________</u>, nascido em <u>___/___/______</u>, portador do RG nº <u>_______________</u>, CPF nº <u>_______________</u>, <strong>AUTORIZO</strong> sua residência no alojamento do clube durante o período de sua formação esportiva.</p>
  <h4>CONDIÇÕES GERAIS:</h4>
  <ol>
    <li>O atleta deverá respeitar as regras de convivência estabelecidas pelo clube;</li>
    <li>O atleta deverá manter seus estudos regularmente, com frequência e aproveitamento satisfatórios;</li>
    <li>O atleta deverá zelar pela conservação das instalações e equipamentos do alojamento;</li>
    <li>O atleta deverá respeitar os horários estabelecidos para refeições, descanso e atividades;</li>
    <li>O atleta deverá manter conduta ética e moral adequada durante sua permanência no alojamento;</li>
    <li>O clube não se responsabiliza por objetos pessoais e de valor não declarados;</li>
    <li>Visitas de familiares deverão ser previamente agendadas com a coordenação;</li>
    <li>Saídas do alojamento deverão ser autorizadas pela coordenação.</li>
  </ol>
  <p>Declaro estar ciente de que o descumprimento das regras estabelecidas poderá acarretar em advertências e, em casos graves ou reincidentes, no desligamento do atleta do alojamento e/ou do clube.</p>
  <p>Autorizo também o clube a tomar as providências necessárias em caso de emergência médica, bem como a administrar medicamentos prescritos por profissionais de saúde.</p>
  <p><strong>Contatos de emergência:</strong></p>
  <p>Nome: <u>_________________________________</u></p>
  <p>Telefone: <u>_______________</u></p>
  <p>Relação com o atleta: <u>_______________</u></p>
  <p><strong>Informações médicas relevantes (alergias, medicamentos de uso contínuo, etc.):</strong></p>
  <p><u>_________________________________</u></p>
  <p><u>_________________________________</u></p>
  <br>
  <p><u>_________________________________</u>, <u>_____</u> de <u>_________________</u> de <u>________</u>.</p>
  <br>
  <p>_________________________________</p>
  <p><strong>Assinatura do Responsável Legal</strong></p>
  <br>
  <p>_________________________________</p>
  <p><strong>Assinatura do Atleta</strong></p>
  <br>
  <p>_________________________________</p>
  <p><strong>Assinatura do Representante do Clube</strong></p>
  <br>
  <p style="text-align: center; font-size: 12px;">Este documento deve ser preenchido, assinado e entregue à coordenação do clube antes do ingresso do atleta no alojamento.</p>',
  'housing',
  true,
  '00000000-0000-0000-0000-000000000000' -- Substitua por um UUID válido de usuário admin
);

-- Template de Termo de Responsabilidade
INSERT INTO club_form_templates (
  club_id,
  name,
  description,
  content,
  form_type,
  is_active,
  created_by
) VALUES (
  1, -- Substitua pelo ID do clube
  'Termo de Isenção de Responsabilidade Padrão',
  'Termo de isenção de responsabilidade para participação em atividades esportivas',
  '<h2 style="text-align: center;">TERMO DE ISENÇÃO DE RESPONSABILIDADE</h2>
  <p>Eu, <u>_________________________________</u>, portador(a) do RG nº <u>_______________</u>, CPF nº <u>_______________</u>, residente e domiciliado(a) à <u>_________________________________</u>, na cidade de <u>_______________</u>, estado de <u>_______________</u>, na qualidade de responsável legal pelo atleta <u>_________________________________</u>, nascido em <u>___/___/______</u>, portador do RG nº <u>_______________</u>, CPF nº <u>_______________</u>, <strong>DECLARO</strong> estar ciente e de acordo com as seguintes condições para participação do atleta em avaliações técnicas e atividades esportivas:</p>
  <h4>DECLARAÇÕES E RESPONSABILIDADES:</h4>
  <ol>
    <li>Declaro que o atleta acima identificado está em plenas condições de saúde para participar de atividades físicas e esportivas, não possuindo qualquer impedimento médico para tal;</li>
    <li>Assumo total responsabilidade por qualquer lesão ou dano físico que possa ocorrer durante as avaliações técnicas e atividades esportivas;</li>
    <li>Isento o clube, seus diretores, funcionários, treinadores e colaboradores de qualquer responsabilidade por danos físicos, materiais ou morais que possam ocorrer durante a participação nas atividades;</li>
    <li>Autorizo o uso de imagem do atleta em materiais promocionais e de divulgação do clube, sem qualquer ônus;</li>
    <li>Estou ciente de que a participação em avaliações técnicas não garante a aprovação ou contratação do atleta pelo clube;</li>
    <li>Declaro que todas as informações fornecidas sobre o atleta são verdadeiras e que não omiti qualquer informação relevante sobre sua saúde ou condição física;</li>
    <li>Comprometo-me a informar imediatamente qualquer alteração na condição de saúde do atleta que possa comprometer sua participação nas atividades;</li>
    <li>Autorizo os profissionais do clube a prestarem os primeiros socorros em caso de acidente, bem como a encaminharem o atleta para atendimento médico especializado, se necessário.</li>
  </ol>
  <p>Declaro ter lido e compreendido integralmente os termos deste documento, estando ciente de que se trata de uma isenção de responsabilidade e assunção de risco.</p>
  <p><strong>Informações médicas relevantes (alergias, medicamentos de uso contínuo, lesões anteriores, etc.):</strong></p>
  <p><u>_________________________________</u></p>
  <p><u>_________________________________</u></p>
  <p><strong>Contato de emergência:</strong></p>
  <p>Nome: <u>_________________________________</u></p>
  <p>Telefone: <u>_______________</u></p>
  <p>Relação com o atleta: <u>_______________</u></p>
  <br>
  <p><u>_________________________________</u>, <u>_____</u> de <u>_________________</u> de <u>________</u>.</p>
  <br>
  <p>_________________________________</p>
  <p><strong>Assinatura do Responsável Legal</strong></p>
  <br>
  <p>_________________________________</p>
  <p><strong>Assinatura do Atleta</strong></p>
  <br>
  <p style="text-align: center; font-size: 12px;">Este documento deve ser preenchido, assinado e entregue à coordenação do clube antes da participação do atleta em qualquer atividade.</p>',
  'liability_waiver',
  true,
  '00000000-0000-0000-0000-000000000000' -- Substitua por um UUID válido de usuário admin
);

-- INSTRUÇÕES DE USO:
-- 1. Substitua o valor '1' pelo ID real do clube
-- 2. Substitua '00000000-0000-0000-0000-000000000000' por um UUID válido de um usuário administrador do clube
-- 3. Execute este script no banco de dados
-- 4. Os templates estarão disponíveis para uso e edição na interface do sistema
