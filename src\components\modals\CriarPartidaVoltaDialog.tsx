import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import { createUpcomingMatch } from "@/api/api";
import { toast } from "@/hooks/use-toast";
import { useUser } from "@/context/UserContext";

interface CriarPartidaVoltaDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  adversario: string;
  competition: string;
  seasonId: number;
  onCreated?: () => void;
}

export function CriarPartidaVoltaDialog({ open, onOpenChange, clubId, adversario, competition, seasonId, onCreated }: CriarPartidaVoltaDialogProps) {
  const [date, setDate] = useState("");
  const [time, setTime] = useState("");
  const [location, setLocation] = useState("");
  const [type, setType] = useState<"casa" | "fora" | "">("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const { user } = useUser();

  const handleSave = async () => {
    if (!date) {
      setError("A data é obrigatória.");
      return;
    }
    if (!time) {
      setError("O horário é obrigatório.");
      return;
    }
    if (!location.trim()) {
      setError("O local é obrigatório.");
      return;
    }
    if (!type) {
      setError("O tipo de jogo é obrigatório.");
      return;
    }
    setError("");
    setIsLoading(true);
    try {
      await createUpcomingMatch(
        clubId,
        {
          club_id: clubId,
          opponent: adversario,
          competition,
          date,
          time,
          location,
          type: type as "casa" | "fora",
          season_id: seasonId,
          ida_volta: false,
          escalacao: undefined,
          formation: ""
        },
        user?.id
      );
      toast({ title: "Partida de volta criada com sucesso!", variant: "default" });
      setDate("");
      setTime("");
      setLocation("");
      setType("");
      onOpenChange(false);
      onCreated && onCreated();
    } catch (e) {
      setError("Erro ao criar partida de volta.");
      toast({ title: "Erro ao criar partida de volta.", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Criar Partida de Volta</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <Input placeholder="Data*" type="date" value={date} onChange={e => setDate(e.target.value)} />
          <Input placeholder="Horário*" type="time" value={time} onChange={e => setTime(e.target.value)} />
          <Input placeholder="Local*" value={location} onChange={e => setLocation(e.target.value)} />
          <select value={type} onChange={e => setType(e.target.value as "casa" | "fora" | "")} className="border rounded px-2 py-1 w-full">
            <option value="">Tipo*</option>
            <option value="casa">Casa</option>
            <option value="fora">Fora</option>
          </select>
          {error && <div className="text-red-500 text-sm mt-1">{error}</div>}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancelar</Button>
          <Button onClick={handleSave} disabled={isLoading}>
            {isLoading ? <span className="loader mr-2" /> : null}
            Salvar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
