# Sistema de Remoção Automática de Vinculações - Status "Inativo"

## Visão Geral

Quando um jogador tem seu status alterado para "Inativo", o sistema automaticamente remove todas as suas vinculações ativas, mantendo apenas o histórico para consulta futura.

## Funcionalidades Implementadas

### 1. Trigger Automático no Banco de Dados

**Arquivo:** `sql/player-status-updates.sql`

- **Fun<PERSON> `remove_player_associations()`**: Remove todas as vinculações do jogador
- **Função `handle_player_status_change()`**: Detecta mudanças de status
- **Trigger `trigger_player_status_change`**: Executa automaticamente quando o status é alterado

### 2. Vinculações Removidas/Desativadas

#### Removidas Completamente:
- **Categorias** (`player_categories`)
- **Participação em Treinamentos** (`training_players`)
- **Convocações Futuras** (`callup_players`)
- **Agenda de Eventos** (`agenda_events.participants`)

#### Desativadas (Preserva Histórico):
- **Alojamentos** (`player_accommodations`) - Status alterado para 'completed'
- **Salários** (`player_salaries`) - Status alterado para 'inactive'
- **Contas de Usuário** (`player_accounts`) - Data de expiração definida
- **Registros Médicos** (`medical_records`) - Status alterado para 'inactive'
- **Adiantamentos** (`salary_advances`) - Status alterado para 'cancelled'
- **Avaliações** (`player_evaluation_invitations`) - Status alterado para 'inactive'

### 3. Integração com Frontend

#### Confirmação do Usuário:
- Diálogo de confirmação antes de alterar status para "Inativo"
- Lista detalhada das vinculações que serão removidas
- Opção de cancelar a operação

#### Notificações:
- Mensagem de sucesso após a operação
- Tratamento de erros com reversão automática

#### Locais de Implementação:
- **Página Elenco** (`src/pages/Elenco.tsx`)
- **Componente PlayerCard** (`src/components/player/PlayerCard.tsx`)
- **API de Jogadores** (`src/api/players.ts`)
- **API de Avaliações** (`src/api/playerEvaluationInvitations.ts`)

## Como Funciona

### 1. Detecção de Mudança de Status

```sql
-- Trigger que detecta mudança para 'inativo'
IF NEW.status = 'inativo' AND (OLD.status IS NULL OR OLD.status != 'inativo') THEN
  PERFORM remove_player_associations(NEW.club_id, NEW.id);
END IF;
```

### 2. Execução da Limpeza

```sql
-- Função que remove/desativa todas as vinculações
CREATE OR REPLACE FUNCTION remove_player_associations(p_club_id INTEGER, p_player_id UUID)
```

### 3. Confirmação no Frontend

```javascript
// Confirmação antes da alteração
if (newStatus === 'inativo') {
  const confirmed = window.confirm(
    `Tem certeza que deseja alterar o status de ${player.name} para "Inativo"?\n\n` +
    'Esta ação irá remover o jogador de:\n' +
    '• Todas as categorias\n' +
    '• Alojamentos ativos\n' +
    // ... lista completa
  );
}
```

## Preservação de Histórico

O sistema foi projetado para **preservar o histórico** sempre que possível:

- **Partidas e Gols**: Mantidos integralmente
- **Transações Financeiras**: Mantidas para auditoria
- **Registros Médicos**: Marcados como inativos, mas preservados
- **Contratos**: Mantidos para referência legal

## Reversibilidade

- Um jogador pode ser reativado alterando seu status de volta para "Disponível"
- As vinculações precisarão ser recriadas manualmente
- O histórico permanece intacto

## Logs e Auditoria

- Todas as operações são registradas no log do PostgreSQL
- Mudanças de status são auditadas através do sistema de permissões
- Logs incluem detalhes sobre quais vinculações foram removidas

## Testes Recomendados

1. **Teste de Trigger**: Alterar status diretamente no banco
2. **Teste de API**: Alterar status via função `updatePlayer()`
3. **Teste de UI**: Alterar status nas interfaces do usuário
4. **Teste de Reversão**: Cancelar operação na confirmação
5. **Teste de Histórico**: Verificar preservação de dados históricos

## Considerações de Performance

- A função `remove_player_associations()` é otimizada para execução rápida
- Operações são executadas em uma única transação
- Índices existentes garantem performance adequada

## Manutenção

- Monitorar logs para identificar possíveis problemas
- Verificar integridade dos dados após operações em lote
- Considerar backup antes de operações massivas de inativação
