import { supabase } from "@/integrations/supabase/client";
import { getFinancialTransactions, getFinancialAccounts, getPlayerById } from "./api";
import { getSalaryAdvances } from "./salaryAdvances";

// Types for consolidated financial data
export interface ConsolidatedPayable {
  id: string;
  type: 'account' | 'salary' | 'advance';
  name: string;
  description: string;
  transaction_date: string;
  due_date?: string;
  pix_key?: string;
  role?: string;
  category: string;
  amount: number;
  status: 'pendente' | 'pago';
  person_type?: 'player' | 'collaborator';
  department?: string;
}

export interface CashFlowEntry {
  id: string;
  date: string;
  type: 'receita' | 'despesa';
  description: string;
  category: string;
  amount: number;
  running_balance: number;
  payment_status: string;
}

/**
 * Get all pending payables (accounts + salaries) for a club
 * @param clubId Club ID
 * @returns Consolidated list of all pending payables
 */
export async function getAllPendingPayables(clubId: number): Promise<ConsolidatedPayable[]> {
  try {
    const payables: ConsolidatedPayable[] = [];

    // 1. Get pending financial accounts (suppliers, general expenses)
    const accounts = await getFinancialAccounts(clubId);
    const pendingAccounts = accounts.filter(account =>
      account.type === 'a_pagar' && account.status === 'pendente'
    );

    for (const account of pendingAccounts) {
      payables.push({
        id: `account-${account.id}`,
        type: 'account',
        name: account.supplier_client,
        description: account.description,
        transaction_date: account.creation_date,
        due_date: account.due_date,
        pix_key: undefined, // Suppliers don't have PIX in this table
        role: undefined,
        category: account.category,
        amount: account.amount,
        status: 'pendente',
        department: 'Fornecedores'
      });
    }

    // 2. Get pending salary transactions (players and collaborators)
    const transactions = await getFinancialTransactions(clubId);
    const pendingSalaryTransactions = transactions.filter(transaction =>
      transaction.category === 'salários' &&
      transaction.type === 'despesa' &&
      transaction.payment_status === 'pending'
    );

    // Process player salaries with salary advances calculation
    const playerTransactions = pendingSalaryTransactions.filter(t => t.player_id);
    for (const transaction of playerTransactions) {
      try {
        const playerData = await getPlayerById(clubId, transaction.player_id!);

        // Skip inactive players
        if (playerData.status === 'inativo') continue;

        const pixKey = playerData.bank_info?.pix || 'Não informado';

        // Get player category from player_categories table
        let category = 'Sem Categoria';
        try {
          const { data: playerCategoryData, error: categoryError } = await supabase
            .from("player_categories")
            .select(`
              categories!player_categories_category_id_fkey(name)
            `)
            .eq("club_id", clubId)
            .eq("player_id", playerData.id)
            .single();

          if (!categoryError && playerCategoryData?.categories?.name) {
            category = playerCategoryData.categories.name;
          }
        } catch (error) {
          console.error(`Error fetching category for player ${playerData.id}:`, error);
        }

        // Calculate salary advances for this player
        const salaryAmount = typeof transaction.amount === 'string' ? parseFloat(transaction.amount) : transaction.amount;

        // Get advances for this player (all active advances, not filtered by month)
        const playerAdvances = await getSalaryAdvances(clubId, undefined, undefined, 'player', transaction.player_id);
        const activeAdvances = playerAdvances.filter(advance => advance.status === 'active');
        const totalAdvances = activeAdvances.reduce((sum, advance) => sum + advance.amount, 0);

        // Calculate net salary (salary - advances)
        const netSalary = salaryAmount - totalAdvances;

        // Only add to payables if there's still money to pay (net salary > 0)
        if (netSalary > 0) {
          let description = `Salário Líquido - ${playerData.name}`;
          if (totalAdvances > 0) {
            description += ` (Bruto: R$ ${salaryAmount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })} - Vales: R$ ${totalAdvances.toLocaleString('pt-BR', { minimumFractionDigits: 2 })})`;
          }

          payables.push({
            id: `salary-player-${transaction.id}`,
            type: 'salary',
            name: playerData.name,
            description: description,
            transaction_date: transaction.date,
            pix_key: pixKey,
            role: 'Jogador',
            category: category,
            amount: netSalary,
            status: 'pendente',
            person_type: 'player',
            department: `Jogadores - ${category}`
          });
        }
      } catch (error) {
        console.error(`Error fetching player data for transaction ${transaction.id}:`, error);
      }
    }

    // Process collaborator salaries with salary advances calculation
    const collaboratorTransactions = pendingSalaryTransactions.filter(t => t.collaborator_id);
    for (const transaction of collaboratorTransactions) {
      try {
        const { data: collaboratorData, error } = await supabase
          .from("collaborators")
          .select("*")
          .eq("id", transaction.collaborator_id)
          .eq("club_id", clubId)
          .single();

        if (error) throw error;

        let pixKey = 'Não informado';
        if (collaboratorData.bank_info?.pix) {
          pixKey = collaboratorData.bank_info.pix;
        }

        // Calculate salary advances for this collaborator
        const salaryAmount = typeof transaction.amount === 'string' ? parseFloat(transaction.amount) : transaction.amount;

        // Get advances for this collaborator (all active advances, not filtered by month)
        const collaboratorAdvances = await getSalaryAdvances(clubId, undefined, undefined, 'collaborator', transaction.collaborator_id);
        const activeAdvances = collaboratorAdvances.filter(advance => advance.status === 'active');
        const totalAdvances = activeAdvances.reduce((sum, advance) => sum + advance.amount, 0);

        // Calculate net salary (salary - advances)
        const netSalary = salaryAmount - totalAdvances;

        // Only add to payables if there's still money to pay (net salary > 0)
        if (netSalary > 0) {
          let description = `Salário Líquido - ${collaboratorData.full_name}`;
          if (totalAdvances > 0) {
            description += ` (Bruto: R$ ${salaryAmount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })} - Vales: R$ ${totalAdvances.toLocaleString('pt-BR', { minimumFractionDigits: 2 })})`;
          }

          payables.push({
            id: `salary-collaborator-${transaction.id}`,
            type: 'salary',
            name: collaboratorData.full_name,
            description: description,
            transaction_date: transaction.date,
            pix_key: pixKey,
            role: collaboratorData.role,
            category: 'Comissão Técnica',
            amount: netSalary,
            status: 'pendente',
            person_type: 'collaborator',
            department: 'Comissão Técnica'
          });
        }
      } catch (error) {
        console.error(`Error fetching collaborator data for transaction ${transaction.id}:`, error);
      }
    }

    // 3. Add salary advances (vales) as informational items (amount = 0 since already deducted from salaries)
    // This provides transparency about existing advances without double-counting
    const advances = await getSalaryAdvances(clubId);
    const activeAdvances = advances.filter(advance => advance.status === 'active');

    for (const advance of activeAdvances) {
      try {
        let name = 'Desconhecido';
        let pixKey = 'Não informado';
        let category = 'Vales';
        let department = 'Vales (Informativos)';

        if (advance.person_type === 'player') {
          const playerData = await getPlayerById(clubId, advance.person_id);
          if (playerData.status === 'inativo') continue; // Skip inactive players

          name = playerData.name;
          pixKey = playerData.bank_info?.pix || 'Não informado';

          // Get player category from player_categories table
          try {
            const { data: playerCategoryData, error: categoryError } = await supabase
              .from("player_categories")
              .select(`
                categories!player_categories_category_id_fkey(name)
              `)
              .eq("club_id", clubId)
              .eq("player_id", playerData.id)
              .single();

            if (!categoryError && playerCategoryData?.categories?.name) {
              category = playerCategoryData.categories.name;
            }
          } catch (error) {
            console.error(`Error fetching category for player ${playerData.id}:`, error);
          }

          department = `Vales - Jogadores ${category} (Informativos)`;
        } else if (advance.person_type === 'collaborator') {
          const { data: collaboratorData, error } = await supabase
            .from("collaborators")
            .select("*")
            .eq("id", advance.person_id)
            .eq("club_id", clubId)
            .single();

          if (error) throw error;

          name = collaboratorData.full_name;
          if (collaboratorData.bank_info?.pix) {
            pixKey = collaboratorData.bank_info.pix;
          }
          department = 'Vales - Comissão Técnica (Informativos)';
        }

        payables.push({
          id: `advance-info-${advance.id}`,
          type: 'advance',
          name: name,
          description: `Vale Concedido - ${advance.description || 'Adiantamento'} (Já descontado do salário)`,
          transaction_date: advance.advance_date,
          pix_key: pixKey,
          role: advance.person_type === 'player' ? 'Jogador' : 'Colaborador',
          category: category,
          amount: 0, // Amount is 0 since it's already deducted from salary
          status: 'pendente',
          person_type: advance.person_type,
          department: department
        });
      } catch (error) {
        console.error(`Error fetching data for advance ${advance.id}:`, error);
      }
    }

    // Sort by due date, then by amount (descending)
    payables.sort((a, b) => {
      const dateA = new Date(a.due_date || a.transaction_date);
      const dateB = new Date(b.due_date || b.transaction_date);

      if (dateA.getTime() !== dateB.getTime()) {
        return dateA.getTime() - dateB.getTime();
      }

      return b.amount - a.amount;
    });

    return payables;
  } catch (error) {
    console.error("Error getting all pending payables:", error);
    throw new Error("Erro ao buscar contas a pagar");
  }
}

/**
 * Get cash flow entries for a specific date range
 * @param clubId Club ID
 * @param startDate Start date (YYYY-MM-DD)
 * @param endDate End date (YYYY-MM-DD)
 * @returns Cash flow entries with running balance
 */
export async function getCashFlowByDateRange(
  clubId: number,
  startDate: string,
  endDate: string
): Promise<CashFlowEntry[]> {
  try {
    const transactions = await getFinancialTransactions(clubId);

    // Filter transactions by date range - include all transactions regardless of payment status
    const filteredTransactions = transactions.filter(transaction => {
      const transactionDate = transaction.date;
      return transactionDate >= startDate && transactionDate <= endDate;
    });

    // Sort by date
    filteredTransactions.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    // Calculate running balance
    let runningBalance = 0;
    const cashFlowEntries: CashFlowEntry[] = [];

    for (const transaction of filteredTransactions) {
      const amount = typeof transaction.amount === 'string' ? parseFloat(transaction.amount) : transaction.amount;

      if (transaction.type === 'receita') {
        runningBalance += amount;
      } else {
        runningBalance -= amount;
      }

      cashFlowEntries.push({
        id: `transaction-${transaction.id}`,
        date: transaction.date,
        type: transaction.type as 'receita' | 'despesa',
        description: transaction.description,
        category: transaction.category,
        amount: amount,
        running_balance: runningBalance,
        payment_status: transaction.payment_status || 'pending'
      });
    }

    return cashFlowEntries;
  } catch (error) {
    console.error("Error getting cash flow by date range:", error);
    throw new Error("Erro ao buscar fluxo de caixa");
  }
}

/**
 * Get pending payables for a specific period
 * @param clubId Club ID
 * @param startDate Start date (YYYY-MM-DD)
 * @param endDate End date (YYYY-MM-DD)
 * @returns Filtered list of pending payables
 */
export async function getPendingPayablesByPeriod(
  clubId: number,
  startDate: string,
  endDate: string
): Promise<ConsolidatedPayable[]> {
  try {
    const allPayables = await getAllPendingPayables(clubId);

    // Filter by date range (using due_date if available, otherwise transaction_date)
    const filteredPayables = allPayables.filter(payable => {
      const dateToCheck = payable.due_date || payable.transaction_date;
      return dateToCheck >= startDate && dateToCheck <= endDate;
    });

    return filteredPayables;
  } catch (error) {
    console.error("Error getting pending payables by period:", error);
    throw new Error("Erro ao buscar contas a pagar por período");
  }
}
