import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { Search, Eye, Calendar, User, CheckCircle, Clock, XCircle } from "lucide-react";
import { useCurrentClubId } from "@/context/ClubContext";
import { getPlayerEvaluations, PlayerEvaluation } from "@/api/api";
import { formatDate } from "@/utils/formatters";
import { ViewEvaluationDialog } from "./ViewEvaluationDialog";

interface PlayerEvaluationsListProps {
  playerId?: number;
  showHeader?: boolean;
}

export function PlayerEvaluationsList({ playerId, showHeader = true }: PlayerEvaluationsListProps) {
  const clubId = useCurrentClubId();
  const { toast } = useToast();
  const [evaluations, setEvaluations] = useState<PlayerEvaluation[]>([]);
  const [filteredEvaluations, setFilteredEvaluations] = useState<PlayerEvaluation[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("all");
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [selectedEvaluation, setSelectedEvaluation] = useState<PlayerEvaluation | null>(null);

  // Carregar avaliações
  useEffect(() => {
    const fetchEvaluations = async () => {
      try {
        setLoading(true);
        const data = await getPlayerEvaluations(clubId, playerId);
        setEvaluations(data);
        setFilteredEvaluations(data);
      } catch (error) {
        console.error("Erro ao carregar avaliações:", error);
        toast({
          title: "Erro",
          description: "Não foi possível carregar as avaliações",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchEvaluations();
  }, [clubId, playerId, toast]);

  // Filtrar avaliações por termo de busca e status
  useEffect(() => {
    let filtered = evaluations;

    // Filtrar por termo de busca
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (evaluation) =>
          evaluation.player_name?.toLowerCase().includes(term) ||
          evaluation.evaluator_name?.toLowerCase().includes(term) ||
          evaluation.notes?.toLowerCase().includes(term)
      );
    }

    // Filtrar por status
    if (activeTab !== "all") {
      filtered = filtered.filter((evaluation) => evaluation.status === activeTab);
    }

    setFilteredEvaluations(filtered);
  }, [searchTerm, activeTab, evaluations]);

  // Função para visualizar uma avaliação
  const handleViewEvaluation = (evaluation: PlayerEvaluation) => {
    setSelectedEvaluation(evaluation);
    setViewDialogOpen(true);
  };

  // Renderizar status com badge
  const renderStatus = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
            <Clock className="h-3 w-3 mr-1" />
            Pendente
          </Badge>
        );
      case "approved":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            Aprovado
          </Badge>
        );
      case "rejected":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">
            <XCircle className="h-3 w-3 mr-1" />
            Reprovado
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200">
            {status}
          </Badge>
        );
    }
  };

  return (
    <Card>
      {showHeader && (
        <CardHeader>
          <CardTitle>Avaliações de Atletas</CardTitle>
          <CardDescription>
            Visualize as avaliações realizadas
          </CardDescription>
        </CardHeader>
      )}
      <CardContent>
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar pré cadastros..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full sm:w-auto">
              <TabsList>
                <TabsTrigger value="all">Todas</TabsTrigger>
                <TabsTrigger value="pending">Pendentes</TabsTrigger>
                <TabsTrigger value="approved">Aprovadas</TabsTrigger>
                <TabsTrigger value="rejected">Reprovadas</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          {loading ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Carregando avaliações...</p>
            </div>
          ) : filteredEvaluations.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                {searchTerm
                  ? "Nenhuma avaliação encontrado com esse termo."
                  : "Nenhuma avaliação cadastrado."}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredEvaluations.map((evaluation) => (
                <Card key={evaluation.id} className="overflow-hidden">
                  <div className="p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          {evaluation.player_image ? (
                            <AvatarImage src={evaluation.player_image} alt={evaluation.player_name} />
                          ) : null}
                          <AvatarFallback className="bg-team-blue text-white">
                            {evaluation.player_name?.slice(0, 2).toUpperCase() || "AT"}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <h4 className="font-medium text-sm">{evaluation.player_name}</h4>
                          <p className="text-xs text-muted-foreground">
                            {evaluation.position || "Posição não informada"}
                          </p>
                        </div>
                      </div>
                      {renderStatus(evaluation.status)}
                    </div>

                    <div className="space-y-2 text-sm mb-3">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3.5 w-3.5 text-muted-foreground" />
                        <span>{formatDate(evaluation.evaluation_date)}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <User className="h-3.5 w-3.5 text-muted-foreground" />
                        <span>{evaluation.evaluator_name || "Avaliador não informado"}</span>
                      </div>
                    </div>

                    <div className="line-clamp-2 text-sm text-muted-foreground mb-3">
                      {evaluation.notes || "Sem observações"}
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full"
                      onClick={() => handleViewEvaluation(evaluation)}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      Ver Detalhes
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>
      </CardContent>

      {selectedEvaluation && (
        <ViewEvaluationDialog
          open={viewDialogOpen}
          onOpenChange={setViewDialogOpen}
          evaluation={selectedEvaluation}
        />
      )}
    </Card>
  );
}
