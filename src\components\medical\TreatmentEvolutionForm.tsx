import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { CalendarIcon, Plus, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { useTreatmentEvolutionsStore } from "@/store/useTreatmentEvolutionsStore";
import { getMedicalProfessionalByUserId, MedicalProfessional } from "@/api/api";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { usePermission } from "@/hooks/usePermission";

interface TreatmentEvolutionFormProps {
  recordId: number;
  onEvolutionAdded?: () => void;
}

export function TreatmentEvolutionForm({ recordId, onEvolutionAdded }: TreatmentEvolutionFormProps) {
  const { toast } = useToast();
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { role } = usePermission();
  const { addEvolution, loading } = useTreatmentEvolutionsStore();

  const [dialogOpen, setDialogOpen] = useState(false);
  const [medicalProfessional, setMedicalProfessional] = useState<MedicalProfessional | null>(null);

  // Form state
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [description, setDescription] = useState("");
  const [procedures, setProcedures] = useState<string[]>([]);
  const [currentProcedure, setCurrentProcedure] = useState("");
  const [response, setResponse] = useState("");
  const [status, setStatus] = useState<"Alta médica" | "Em tratamento" | "Treina e joga">("Em tratamento");

  // Load medical professional when component mounts
  useEffect(() => {
    if (clubId && user?.id && role === "medical") {
      loadMedicalProfessional();
    }
  }, [clubId, user?.id, role]);

  // Load medical professional data
  const loadMedicalProfessional = async () => {
    try {
      const data = await getMedicalProfessionalByUserId(clubId, user?.id || "");
      setMedicalProfessional(data);
    } catch (error) {
      console.error("Erro ao carregar dados do profissional médico:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar os dados do profissional médico",
        variant: "destructive",
      });
    }
  };

  // Reset form
  const resetForm = () => {
    setDate(new Date());
    setDescription("");
    setProcedures([]);
    setCurrentProcedure("");
    setResponse("");
    setStatus("Em tratamento");
  };

  // Add procedure to list
  const addProcedure = () => {
    if (currentProcedure.trim()) {
      setProcedures([...procedures, currentProcedure.trim()]);
      setCurrentProcedure("");
    }
  };

  // Remove procedure from list
  const removeProcedure = (index: number) => {
    const newProcedures = [...procedures];
    newProcedures.splice(index, 1);
    setProcedures(newProcedures);
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!medicalProfessional) {
      toast({
        title: "Erro",
        description: "Profissional médico não encontrado",
        variant: "destructive",
      });
      return;
    }

    if (!description || !date) {
      toast({
        title: "Campos obrigatórios",
        description: "Por favor, preencha todos os campos obrigatórios",
        variant: "destructive",
      });
      return;
    }

    try {
      // Format date
      const formattedDate = format(date, "yyyy-MM-dd");

      await addEvolution(clubId, user?.id || "", {
        record_id: recordId,
        date: formattedDate,
        description,
        procedures,
        response,
        professional_id: medicalProfessional.id,
        status,
      });

      toast({
        title: "Evolução registrada",
        description: "A evolução do tratamento foi registrada com sucesso",
      });

      resetForm();
      setDialogOpen(false);
      if (onEvolutionAdded) {
        onEvolutionAdded();
      }
    } catch (error) {
      console.error("Erro ao registrar evolução:", error);
      toast({
        title: "Erro",
        description: "Não foi possível registrar a evolução do tratamento",
        variant: "destructive",
      });
    }
  };

  // Get status badge
  const getStatusBadge = (statusValue: string) => {
    switch (statusValue) {
      case "Alta médica":
        return <Badge className="bg-green-500">Alta médica</Badge>;
      case "Em tratamento":
        return <Badge className="bg-amber-500">Em tratamento</Badge>;
      case "Treina e joga":
        return <Badge className="bg-primary">Treina e joga</Badge>;
      default:
        return <Badge variant="outline">{statusValue}</Badge>;
    }
  };

  return (
    <>
      <Button onClick={() => setDialogOpen(true)} className="bg-primary hover:bg-primary/90">
        <Plus className="h-4 w-4 mr-2" />
        Nova Evolução
      </Button>

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Registrar Evolução do Tratamento</DialogTitle>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="date" className="text-right">
                Data*
              </Label>
              <div className="col-span-3">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !date && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {date ? format(date, "PPP", { locale: ptBR }) : <span>Selecione uma data</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={date}
                      onSelect={setDate}
                      initialFocus
                      locale={ptBR}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="description" className="text-right">
                Descrição*
              </Label>
              <Textarea
                id="description"
                placeholder="Descreva a evolução do tratamento"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="col-span-3"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="procedures" className="text-right">
                Procedimentos
              </Label>
              <div className="col-span-3 space-y-2">
                <div className="flex space-x-2">
                  <Input
                    id="procedures"
                    placeholder="Adicione um procedimento"
                    value={currentProcedure}
                    onChange={(e) => setCurrentProcedure(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault();
                        addProcedure();
                      }
                    }}
                  />
                  <Button type="button" onClick={addProcedure} variant="outline">
                    Adicionar
                  </Button>
                </div>
                {procedures.length > 0 && (
                  <Card>
                    <CardContent className="p-2">
                      <ul className="space-y-1">
                        {procedures.map((procedure, index) => (
                          <li key={index} className="flex items-center justify-between">
                            <span>{procedure}</span>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeProcedure(index)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>

            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="response" className="text-right">
                Resposta
              </Label>
              <Textarea
                id="response"
                placeholder="Descreva a resposta do atleta ao tratamento"
                value={response}
                onChange={(e) => setResponse(e.target.value)}
                className="col-span-3"
                rows={2}
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="status" className="text-right">
                Status*
              </Label>
              <div className="col-span-3">
                <Select value={status} onValueChange={(value: "Alta médica" | "Em tratamento" | "Treina e joga") => setStatus(value)}>
                  <SelectTrigger id="status">
                    <SelectValue placeholder="Selecione o status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Alta médica">
                      <div className="flex items-center">
                        {getStatusBadge("Alta médica")}
                        <span className="ml-2">Alta médica</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="Em tratamento">
                      <div className="flex items-center">
                        {getStatusBadge("Em tratamento")}
                        <span className="ml-2">Em tratamento</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="Treina e joga">
                      <div className="flex items-center">
                        {getStatusBadge("Treina e joga")}
                        <span className="ml-2">Treina e joga</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleSubmit} disabled={loading}>
              {loading ? "Salvando..." : "Salvar Evolução"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
