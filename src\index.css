/* Import enhanced form styles */
@import './styles/enhanced-forms.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 210 100% 35%;
    --primary-foreground: 210 40% 98%;

    --secondary: 142 80% 38%;
    --secondary-foreground: 210 40% 98%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 25 95% 53%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 210 100% 35%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }

  /* Animações personalizadas para a plataforma */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in {
    animation: fadeIn 0.3s ease-out forwards;
  }

  /* Responsividade global */
  .responsive-container {
    @apply w-full max-w-full overflow-hidden;
  }

  .responsive-grid {
    @apply grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }

  .responsive-text {
    @apply text-sm sm:text-base;
  }

  .responsive-heading {
    @apply text-xl sm:text-2xl lg:text-3xl;
  }

  .responsive-padding {
    @apply p-2 sm:p-4 lg:p-6;
  }

  .responsive-margin {
    @apply m-2 sm:m-4 lg:m-6;
  }

  /* Melhorias para tabelas responsivas */
  .table-responsive {
    @apply w-full overflow-x-auto;
  }

  .table-responsive table {
    @apply min-w-full;
  }

  /* Melhorias para formulários em mobile */
  .form-responsive input,
  .form-responsive select,
  .form-responsive textarea {
    @apply text-base; /* Evita zoom no iOS */
  }

  /* Melhorias para modais em mobile */
  .modal-responsive {
    @apply max-h-screen overflow-y-auto;
  }

  /* Scrollbar personalizada */
  .custom-scrollbar::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-gray-100 rounded;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded hover:bg-gray-400;
  }

  /* Melhorias para touch devices */
  @media (hover: none) and (pointer: coarse) {
    .hover-touch {
      @apply active:bg-gray-100;
    }
  }
}