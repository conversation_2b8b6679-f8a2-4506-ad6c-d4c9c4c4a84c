import { supabase } from "@/integrations/supabase/client";
import { withPermission, withAuditLog } from "./middleware";
import { DEPARTMENT_PERMISSIONS } from "@/constants/permissions";

export type Department = {
  id: number;
  club_id: number;
  name: string;
  description: string | null;
  created_at: string;
};

export type UserDepartment = {
  id: number;
  club_id: number;
  user_id: string;
  department_id: number;
  role: string;
  permissions?: Record<string, boolean>; // Permissões específicas do departamento
  created_at: string;
  department_name?: string; // Campo adicional para junção
  user_name?: string; // Campo adicional para junção
};

/**
 * Obtém todos os departamentos de um clube
 * @param clubId ID do clube
 * @returns Lista de departamentos
 */
export async function getDepartments(clubId: number): Promise<Department[]> {
  try {
    const { data, error } = await supabase
      .from("departments")
      .select("*")
      .eq("club_id", clubId)
      .order("name");

    if (error) {
      throw new Error(`Erro ao obter departamentos: ${error.message}`);
    }

    return data || [];
  } catch (error: any) {
    console.error("Erro ao obter departamentos:", error);
    throw new Error(error.message || "Erro ao obter departamentos");
  }
}

/**
 * Cria um novo departamento
 * @param clubId ID do clube
 * @param name Nome do departamento
 * @param description Descrição do departamento
 * @returns Departamento criado
 */
export async function createDepartment(
  clubId: number,
  name: string,
  description?: string
): Promise<Department> {
  try {
    const { data, error } = await supabase
      .from("departments")
      .insert({
        club_id: clubId,
        name,
        description,
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Erro ao criar departamento: ${error.message}`);
    }

    return data;
  } catch (error: any) {
    console.error("Erro ao criar departamento:", error);
    throw new Error(error.message || "Erro ao criar departamento");
  }
}

/**
 * Atualiza um departamento
 * @param departmentId ID do departamento
 * @param name Nome do departamento
 * @param description Descrição do departamento
 * @returns Departamento atualizado
 */
export async function updateDepartment(
  departmentId: number,
  name: string,
  description?: string
): Promise<Department> {
  try {
    const { data, error } = await supabase
      .from("departments")
      .update({
        name,
        description,
      })
      .eq("id", departmentId)
      .select()
      .single();

    if (error) {
      throw new Error(`Erro ao atualizar departamento: ${error.message}`);
    }

    return data;
  } catch (error: any) {
    console.error("Erro ao atualizar departamento:", error);
    throw new Error(error.message || "Erro ao atualizar departamento");
  }
}

/**
 * Exclui um departamento
 * @param departmentId ID do departamento
 * @returns true se o departamento foi excluído com sucesso
 */
export async function deleteDepartment(departmentId: number): Promise<boolean> {
  try {
    // Verificar se há usuários associados ao departamento
    const { data: users, error: usersError } = await supabase
      .from("user_departments")
      .select("id")
      .eq("department_id", departmentId);

    if (usersError) {
      throw new Error(`Erro ao verificar usuários do departamento: ${usersError.message}`);
    }

    if (users && users.length > 0) {
      throw new Error("Não é possível excluir um departamento que possui usuários associados");
    }

    // Excluir o departamento
    const { error } = await supabase
      .from("departments")
      .delete()
      .eq("id", departmentId);

    if (error) {
      throw new Error(`Erro ao excluir departamento: ${error.message}`);
    }

    return true;
  } catch (error: any) {
    console.error("Erro ao excluir departamento:", error);
    throw new Error(error.message || "Erro ao excluir departamento");
  }
}

/**
 * Obtém todos os usuários de um departamento
 * @param clubId ID do clube
 * @param departmentId ID do departamento
 * @returns Lista de usuários do departamento
 */
export async function getDepartmentUsers(
  clubId: number,
  departmentId: number
): Promise<UserDepartment[]> {
  try {
    const { data, error } = await supabase
      .from("user_departments")
      .select(`
        *,
        users:user_id (
          id,
          name,
          email
        )
      `)
      .eq("club_id", clubId)
      .eq("department_id", departmentId);

    if (error) {
      throw new Error(`Erro ao obter usuários do departamento: ${error.message}`);
    }

    // Formatar os dados para incluir o nome do usuário
    return (data || []).map((item) => ({
      ...item,
      user_name: item.users?.name || "Usuário desconhecido",
    }));
  } catch (error: any) {
    console.error("Erro ao obter usuários do departamento:", error);
    throw new Error(error.message || "Erro ao obter usuários do departamento");
  }
}

/**
 * Obtém todos os departamentos de um usuário
 * @param clubId ID do clube
 * @param userId ID do usuário
 * @returns Lista de departamentos do usuário
 */
export async function getUserDepartments(
  clubId: number,
  userId: string
): Promise<UserDepartment[]> {
  try {
    const { data, error } = await supabase
      .from("user_departments")
      .select(`
        *,
        departments:department_id (
          id,
          name,
          description
        )
      `)
      .eq("club_id", clubId)
      .eq("user_id", userId);

    if (error) {
      throw new Error(`Erro ao obter departamentos do usuário: ${error.message}`);
    }

    // Formatar os dados para incluir o nome do departamento
    return (data || []).map((item) => ({
      ...item,
      department_name: item.departments?.name || "Departamento desconhecido",
    }));
  } catch (error: any) {
    console.error("Erro ao obter departamentos do usuário:", error);
    throw new Error(error.message || "Erro ao obter departamentos do usuário");
  }
}

/**
 * Adiciona um usuário a um departamento
 * @param clubId ID do clube
 * @param userId ID do usuário
 * @param departmentId ID do departamento
 * @param role Papel do usuário no departamento
 * @returns Associação criada
 */
export async function addUserToDepartment(
  clubId: number,
  userId: string,
  departmentId: number,
  role: string,
  permissions?: Record<string, boolean>,
  requestUserId?: string
): Promise<UserDepartment> {
  // Se requestUserId não for fornecido, não verificamos permissões (para compatibilidade)
  if (!requestUserId) {
    try {
      // Verificar se o usuário já está no departamento
      const { data: existing, error: existingError } = await supabase
        .from("user_departments")
        .select("id")
        .eq("club_id", clubId)
        .eq("user_id", userId)
        .eq("department_id", departmentId)
        .maybeSingle();

      if (existingError) {
        throw new Error(`Erro ao verificar associação existente: ${existingError.message}`);
      }

      if (existing) {
        throw new Error("O usuário já está associado a este departamento");
      }

      // Adicionar o usuário ao departamento
      const { data, error } = await supabase
        .from("user_departments")
        .insert({
          club_id: clubId,
          user_id: userId,
          department_id: departmentId,
          role,
          permissions: permissions || {}
        })
        .select()
        .single();

      if (error) {
        throw new Error(`Erro ao adicionar usuário ao departamento: ${error.message}`);
      }

      return data;
    } catch (error: any) {
      console.error("Erro ao adicionar usuário ao departamento:", error);
      throw new Error(error.message || "Erro ao adicionar usuário ao departamento");
    }
  }

  // Com requestUserId, verificamos permissões e registramos no log de auditoria
  return withPermission(
    clubId,
    requestUserId,
    DEPARTMENT_PERMISSIONS.EDIT,
    async () => {
      return withAuditLog(
        clubId,
        requestUserId,
        "department.add_user",
        { department_id: departmentId, user_id: userId, role },
        async () => {
          // Verificar se o usuário já está no departamento
          const { data: existing, error: existingError } = await supabase
            .from("user_departments")
            .select("id")
            .eq("club_id", clubId)
            .eq("user_id", userId)
            .eq("department_id", departmentId)
            .maybeSingle();

          if (existingError) {
            throw new Error(`Erro ao verificar associação existente: ${existingError.message}`);
          }

          if (existing) {
            throw new Error("O usuário já está associado a este departamento");
          }

          // Adicionar o usuário ao departamento
          const { data, error } = await supabase
            .from("user_departments")
            .insert({
              club_id: clubId,
              user_id: userId,
              department_id: departmentId,
              role,
              permissions: permissions || {}
            })
            .select()
            .single();

          if (error) {
            throw new Error(`Erro ao adicionar usuário ao departamento: ${error.message}`);
          }

          return data;
        }
      );
    }
  );
}

/**
 * Remove um usuário de um departamento
 * @param userDepartmentId ID da associação entre usuário e departamento
 * @returns true se o usuário foi removido com sucesso
 */
export async function removeUserFromDepartment(userDepartmentId: number): Promise<boolean> {
  try {
    const { error } = await supabase
      .from("user_departments")
      .delete()
      .eq("id", userDepartmentId);

    if (error) {
      throw new Error(`Erro ao remover usuário do departamento: ${error.message}`);
    }

    return true;
  } catch (error: any) {
    console.error("Erro ao remover usuário do departamento:", error);
    throw new Error(error.message || "Erro ao remover usuário do departamento");
  }
}

/**
 * Atualiza o papel de um usuário em um departamento
 * @param userDepartmentId ID da associação entre usuário e departamento
 * @param role Novo papel do usuário
 * @returns Associação atualizada
 */
export async function updateUserDepartmentRole(
  userDepartmentId: number,
  role: string
): Promise<UserDepartment> {
  try {
    const { data, error } = await supabase
      .from("user_departments")
      .update({
        role,
      })
      .eq("id", userDepartmentId)
      .select()
      .single();

    if (error) {
      throw new Error(`Erro ao atualizar papel do usuário: ${error.message}`);
    }

    return data;
  } catch (error: any) {
    console.error("Erro ao atualizar papel do usuário:", error);
    throw new Error(error.message || "Erro ao atualizar papel do usuário");
  }
}
