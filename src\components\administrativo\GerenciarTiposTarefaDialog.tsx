import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/use-toast";
import { Loader2, Plus, Trash2, MoveUp, MoveDown, Palette } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

type TaskType = Database['public']['Tables']['task_types']['Row'];

interface GerenciarTiposTarefaDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  onSuccess?: (updatedItem?: TaskType, allItems?: TaskType[]) => void;
}

export function GerenciarTiposTarefaDialog({
  open,
  onOpenChange,
  clubId,
  onSuccess
}: GerenciarTiposTarefaDialogProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [taskTypes, setTaskTypes] = useState<TaskType[]>([]);
  const [newTypeName, setNewTypeName] = useState("");
  const [reordering, setReordering] = useState(false);

  // Carregar tipos de tarefas existentes
  useEffect(() => {
    if (open) {
      fetchTaskTypes();
    }
  }, [open, clubId]);

  const fetchTaskTypes = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from("task_types")
        .select("*")
        .eq("club_id", clubId)
        .order("position", { ascending: true });

      if (error) {
        throw new Error(`Erro ao buscar tipos de tarefas: ${error.message}`);
      }

      setTaskTypes(data || []);
    } catch (err: any) {
      console.error("Erro ao buscar tipos de tarefas:", err);
      setError(err.message || "Erro ao buscar tipos de tarefas");
      toast({
        title: "Erro",
        description: err.message || "Erro ao buscar tipos de tarefas",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddTaskType = async () => {
    if (!newTypeName.trim()) {
      toast({
        title: "Erro",
        description: "O nome do tipo de tarefa é obrigatório",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      // Verificar se já existe um tipo com esse nome
      const existingType = taskTypes.find(
        type => type.name.toLowerCase() === newTypeName.trim().toLowerCase()
      );

      if (existingType) {
        toast({
          title: "Erro",
          description: "Já existe um tipo de tarefa com esse nome",
          variant: "destructive",
        });
        return;
      }

      // Obter a maior posição atual
      const maxPosition = taskTypes.length > 0
        ? Math.max(...taskTypes.map(type => type.position || 0))
        : 0;

      // Inserir novo tipo
      const { data, error } = await supabase
        .from("task_types")
        .insert({
          club_id: clubId,
          name: newTypeName.trim(),
          position: maxPosition + 1
        })
        .select()
        .single();

      if (error) {
        throw new Error(`Erro ao adicionar tipo de tarefa: ${error.message}`);
      }
      if (!data) { // Check if data is null
        throw new Error("Nenhum dado retornado ao adicionar tipo de tarefa.");
      }

      // Atualizar lista
      setTaskTypes([...taskTypes, data]);
      setNewTypeName("");

      toast({
        title: "Sucesso",
        description: "Tipo de tarefa adicionado com sucesso",
      });
    } catch (err: any) {
      console.error("Erro ao adicionar tipo de tarefa:", err);
      setError(err.message || "Erro ao adicionar tipo de tarefa");
      toast({
        title: "Erro",
        description: err.message || "Erro ao adicionar tipo de tarefa",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteTaskType = async (id: number) => {
    const typeToDelete = taskTypes.find(type => type.id === id);
    if (!typeToDelete) return;

    setLoading(true);
    try {
      // Verificar se existem tarefas usando esse tipo
      const { data: tasksUsingType, error: checkError } = await supabase
        .from("administrative_tasks")
        .select("id")
        .eq("club_id", clubId)
        .eq("status", typeToDelete.name);

      if (checkError) {
        throw new Error(`Erro ao verificar tarefas: ${checkError.message}`);
      }

      if (tasksUsingType && tasksUsingType.length > 0) {
        toast({
          title: "Erro",
          description: `Existem ${tasksUsingType.length} tarefas usando este tipo. Mova-as para outro tipo antes de excluir.`,
          variant: "destructive",
        });
        return;
      }

      // Excluir o tipo
      const { error } = await supabase
        .from("task_types")
        .delete()
        .eq("id", id);

      if (error) {
        throw new Error(`Erro ao excluir tipo de tarefa: ${error.message}`);
      }

      // Atualizar lista
      setTaskTypes(taskTypes.filter(type => type.id !== id));

      toast({
        title: "Sucesso",
        description: "Tipo de tarefa excluído com sucesso",
      });
      if (onSuccess) onSuccess(undefined, taskTypes.filter(type => type.id !== id));
    } catch (err: any) {
      console.error("Erro ao excluir tipo de tarefa:", err);
      setError(err.message || "Erro ao excluir tipo de tarefa");
      toast({
        title: "Erro",
        description: err.message || "Erro ao excluir tipo de tarefa",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Função para atualizar a cor de um tipo de tarefa
  const handleColorChange = async (typeId: number, color: string) => {
    setLoading(true);
    try {
      // Prepare update payload according to Supabase Update type
      const updatePayload: Database['public']['Tables']['task_types']['Update'] = {
        color,
        // updated_at is handled by the DB, so not explicitly set here for update.
        // If the lint error persists, it means 'color' alone isn't enough, or client typing is off.
      };

      const { error } = await supabase
        .from("task_types")
        .update(updatePayload)
        .eq("id", typeId)
        .eq("club_id", clubId);

      if (error) {
        throw new Error(`Erro ao atualizar cor: ${error.message}`);
      }

      // Atualizar localmente após a mudança de cor bem-sucedida
      const newlyUpdatedTypes = taskTypes.map(t => t.id === typeId ? { ...t, color } : t);
      setTaskTypes(newlyUpdatedTypes);

      toast({ title: "Sucesso", description: "Cor atualizada com sucesso." });
      if (onSuccess) {
        const updatedSingleType = newlyUpdatedTypes.find(t => t.id === typeId);
        onSuccess(updatedSingleType, undefined); // Pass the single updated type
      }
    } catch (err: any) {
      console.error("Erro ao atualizar cor:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao atualizar cor",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Função para mover um tipo de tarefa para cima
  const handleMoveUp = async (index: number) => {
    if (index === 0) return; // Já está no topo

    // Reordenar localmente
    const reorderedTypes = Array.from(taskTypes);
    const temp = reorderedTypes[index];
    reorderedTypes[index] = reorderedTypes[index - 1];
    reorderedTypes[index - 1] = temp;

    // Atualizar posições
    const updatedTypes = reorderedTypes.map((type, idx) => ({
      ...type,
      position: idx + 1
    }));

    // Atualizar estado imediatamente para responsividade da UI
    setTaskTypes(updatedTypes);

    // Atualizar no banco de dados
    await updatePositionsInDatabase(updatedTypes);
  };

  // Função para mover um tipo de tarefa para baixo
  const handleMoveDown = async (index: number) => {
    if (index === taskTypes.length - 1) return; // Já está no final

    // Reordenar localmente
    const reorderedTypes = Array.from(taskTypes);
    const temp = reorderedTypes[index];
    reorderedTypes[index] = reorderedTypes[index + 1];
    reorderedTypes[index + 1] = temp;

    // Atualizar posições
    const updatedTypes = reorderedTypes.map((type, idx) => ({
      ...type,
      position: idx + 1
    }));

    // Atualizar estado imediatamente para responsividade da UI
    setTaskTypes(updatedTypes);

    // Atualizar no banco de dados
    await updatePositionsInDatabase(updatedTypes);
  };

  // Função auxiliar para atualizar posições no banco de dados
  const updatePositionsInDatabase = async (updatedTypes: TaskType[]) => {
    setReordering(true);
    try {
      // Atualizar em lote todas as posições em uma única transação
      const updates: Array<Database['public']['Tables']['task_types']['Update']> = updatedTypes.map(type => ({
        id: type.id,
        club_id: type.club_id,
        name: type.name,
        position: type.position,
        // color and updated_at are not part of this specific batch update's core change
        // but 'name' is included as it was part of the original upsert logic.
        // If upsert expects all fields or specific ones, this might need adjustment.
      }));

      // Usar UPSERT para atualizar todos os registros de uma vez
      const { error } = await supabase
        .from("task_types")
        .upsert(updates, { onConflict: 'id' }); 

      if (error) {
        console.error("Erro Supabase ao atualizar posições:", error);
        throw new Error(`Erro ao atualizar posições: ${error.message}`);
      }

      // Atualiza o estado local para refletir a nova ordem
      setTaskTypes(updatedTypes); // Use the function parameter 'updatedTypes'

      toast({ title: "Sucesso", description: "Ordem dos tipos de tarefa atualizada." });
      if (onSuccess) {
        onSuccess(undefined, updatedTypes); // Use the function parameter 'updatedTypes'
      }
    } catch (err: any) {
      console.error("Erro ao reordenar tipos de tarefa:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao reordenar tipos de tarefa",
        variant: "destructive",
      });

      // Buscar ordem original em caso de erro
      fetchTaskTypes();
    } finally {
      setReordering(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Gerenciar Tipos de Tarefa</DialogTitle>
        </DialogHeader>

        {error && (
          <div className="bg-red-50 text-red-700 p-3 rounded-md text-sm mb-4">
            {error}
          </div>
        )}

        <div className="space-y-4">
          <div className="flex items-end gap-2">
            <div className="flex-grow space-y-2">
              <Label htmlFor="newTypeName">Novo Tipo de Tarefa</Label>
              <Input
                id="newTypeName"
                value={newTypeName}
                onChange={(e) => setNewTypeName(e.target.value)}
                placeholder="Ex: Em Revisão, Aguardando Aprovação"
              />
            </div>
            <Button
              onClick={handleAddTaskType}
              disabled={loading || !newTypeName.trim()}
              className="mb-0.5"
            >
              <Plus className="h-4 w-4 mr-1" />
              Adicionar
            </Button>
          </div>

          <div className="border rounded-md p-4 space-y-2">
            <h3 className="font-medium text-sm mb-2">
              Tipos de Tarefa Existentes
              <span className="text-xs text-muted-foreground ml-2">
                (Arraste para reordenar)
              </span>
            </h3>

            {loading ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
                <span className="ml-2 text-muted-foreground">Carregando...</span>
              </div>
            ) : taskTypes.length === 0 ? (
              <div className="text-center py-4 text-muted-foreground">
                Nenhum tipo de tarefa encontrado.
              </div>
            ) : (
              <div className="space-y-2">
                {taskTypes.map((type, index) => (
                  <div
                    key={type.id.toString()}
                    className="flex items-center justify-between bg-gray-50 p-2 rounded-md"
                    style={{
                      borderLeft: type.color ? `4px solid ${type.color}` : undefined,
                    }}
                  >
                    <div className="flex items-center">
                      <span className="font-medium">
                        {type.name === 'a_fazer' ? 'A Fazer' :
                         type.name === 'em_andamento' ? 'Em Andamento' :
                         type.name === 'concluido' ? 'Concluído' :
                         type.name}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleMoveUp(index)}
                        disabled={index === 0}
                        className="mr-1"
                        title="Mover para cima"
                      >
                        <MoveUp className="h-4 w-4" />
                      </Button>

                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleMoveDown(index)}
                        disabled={index === taskTypes.length - 1}
                        className="mr-1"
                        title="Mover para baixo"
                      >
                        <MoveDown className="h-4 w-4" />
                      </Button>

                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="mr-1"
                            title="Escolher cor"
                          >
                            <Palette className="h-4 w-4" style={{ color: type.color || 'currentColor' }} />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-64">
                          <div className="space-y-2">
                            <h4 className="font-medium text-sm">Escolha uma cor</h4>
                            <div className="grid grid-cols-5 gap-2">
                              {[
                                '#ef4444', // red
                                '#f97316', // orange
                                '#eab308', // yellow
                                '#22c55e', // green
                                '#3b82f6', // blue
                                '#a855f7', // purple
                                '#ec4899', // pink
                                '#6b7280', // gray
                                '#000000', // black
                                '#ffffff', // white
                              ].map((color) => (
                                <button
                                  key={color}
                                  className="w-8 h-8 rounded-full border border-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2"
                                  style={{ backgroundColor: color }}
                                  onClick={() => handleColorChange(type.id, color)}
                                />
                              ))}
                            </div>
                          </div>
                        </PopoverContent>
                      </Popover>

                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDeleteTaskType(type.id)}
                        title="Excluir"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Fechar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
