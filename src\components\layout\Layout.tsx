
import { Outlet } from "react-router-dom";
import { Sidebar } from "./Sidebar";
import { Header } from "./Header";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { Activity, Menu } from "lucide-react";
import { useState, useEffect } from "react";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";

export function Layout() {
  const navigate = useNavigate();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Detectar se é mobile
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);

    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Desktop Sidebar */}
      <div className="hidden md:block">
        <Sidebar />
      </div>

      {/* Mobile Sidebar */}
      <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
        <SheetContent side="left" className="p-0 w-64 z-50">
          <Sidebar />
        </SheetContent>
      </Sheet>

      <div className="flex-1 flex flex-col overflow-hidden min-w-0">
        <Header>
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden mr-2 flex-shrink-0"
            onClick={() => setMobileMenuOpen(true)}
          >
            <Menu className="h-5 w-5" />
            <span className="sr-only">Menu</span>
          </Button>
        </Header>

        <main className="flex-1 overflow-y-auto p-2 sm:p-4 md:p-6">
          <div className="max-w-full">
            <Outlet />
          </div>
        </main>

        <footer className="bg-white border-t py-2 px-2 sm:py-3 sm:px-4 md:px-6 flex flex-col sm:flex-row justify-between items-center text-xs sm:text-sm text-muted-foreground gap-2">
          <p className="text-center sm:text-left">Game Day Nexus © {new Date().getFullYear()}</p>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="text-xs flex items-center gap-1 px-2 py-1"
              onClick={() => navigate("/jogos-passados")}
            >
              <Activity className="h-3 w-3 sm:h-3.5 sm:w-3.5" />
              <span className="hidden sm:inline">Histórico de Jogos</span>
              <span className="sm:hidden">Histórico</span>
            </Button>
          </div>
        </footer>
      </div>
    </div>
  );
}
