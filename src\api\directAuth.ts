import { supabase } from "@/integrations/supabase/client";
import { generateRandomPassword } from "@/services/brevoEmailService";
import { sendWelcomeWithCredentials } from "./email";

/**
 * Cria um usuário usando a Edge Function do Supabase
 * e envia um email com as credenciais usando o Brevo
 *
 * @param email Email do usuário
 * @param name Nome do usuário
 * @param clubName Nome do clube
 * @param role Papel do usuário (player, medical, etc.)
 * @param password Senha opcional (será gerada se não fornecida)
 * @returns Objeto com userId e success
 */
export async function createUserDirectly(
  email: string,
  name: string,
  clubName: string,
  role: string,
  clubId?: number,
  password?: string
): Promise<{ userId: string | null; success: boolean; message: string }> {
  try {
    console.log(`Criando usuário via Edge Function: ${email}, ${name}, ${role}`);

    // Verificar se o email já está em uso
    const { data: existingUsers, error: checkError } = await supabase
      .from("users")
      .select("id")
      .eq("email", email);

    if (checkError) {
      console.error("Erro ao verificar email existente:", checkError);
      return {
        userId: null,
        success: false,
        message: `Erro ao verificar email: ${checkError.message}`
      };
    }

    if (existingUsers && existingUsers.length > 0) {
      console.log(`Email ${email} já está em uso, retornando usuário existente`);

      // Atualizar o role do usuário existente para garantir consistência
      try {
        const { error: updateError } = await supabase
          .from("users")
          .update({ role })
          .eq("id", existingUsers[0].id);

        if (updateError) {
          console.error("Erro ao atualizar role do usuário existente:", updateError);
        } else {
          console.log(`Role do usuário ${existingUsers[0].id} atualizado para ${role}`);
        }
      } catch (updateError) {
        console.error("Erro ao atualizar role do usuário existente:", updateError);
      }

      return {
        userId: existingUsers[0].id,
        success: true,
        message: "Usuário já existe, role atualizado"
      };
    }

    // Obter token de autenticação do usuário atual
    let token;

    // Tentar obter o token da sessão atual
    const { data: { session } } = await supabase.auth.getSession();
    token = session?.access_token;

    // Se não houver token, verificar diferentes cenários
    if (!token) {
      // Caso 1: Fluxo de registro inicial (admin)
      if (role === "admin") {
        console.log("Fluxo de registro inicial detectado, usando chave anônima");
        token = import.meta.env.VITE_SUPABASE_ANON_KEY;
      }
      // Caso 2: Aceitação de convite (password fornecido)
      else if (password) {
        console.log("Fluxo de aceitação de convite detectado, usando chave anônima");
        token = import.meta.env.VITE_SUPABASE_ANON_KEY;
      }
      // Caso 3: Outros casos não autorizados
      else {
        console.error("Usuário não está autenticado");
        return {
          userId: null,
          success: false,
          message: "Não autorizado: usuário não está logado"
        };
      }
    }

    // Gerar senha aleatória se não fornecida
    const userPassword = password || generateRandomPassword(10);

    // Chamar a Edge Function para criar o usuário
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://qoujacltecwxvymynbsh.supabase.co';
    const edgeFunctionUrl = `${supabaseUrl}/functions/v1/create-user`;

    console.log("Chamando Edge Function:", edgeFunctionUrl);
    console.log("Dados enviados:", {
      email,
      password: userPassword ? "***" : undefined, // Não logar a senha real
      name,
      role,
      clubName,
      clubId
    });
    console.log("Token de autorização:", token ? token.substring(0, 10) + "..." : "nenhum");

    const response = await fetch(edgeFunctionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        email,
        password: userPassword,
        name,
        role,
        clubName,
        clubId
      })
    });

    if (!response.ok) {
      console.error("Resposta da Edge Function não foi OK:", response.status, response.statusText);

      let errorMessage;
      try {
        const errorData = await response.json();
        console.error("Detalhes do erro da Edge Function:", errorData);
        errorMessage = errorData.error || `Erro ao criar usuário: ${response.statusText}`;
      } catch (parseError) {
        console.error("Erro ao analisar resposta de erro:", parseError);
        errorMessage = `Erro ao criar usuário: ${response.status} ${response.statusText}`;
      }

      return {
        userId: null,
        success: false,
        message: errorMessage
      };
    }

    const result = await response.json();

    if (!result.success) {
      console.error("Erro retornado pela Edge Function:", result.error);
      return {
        userId: null,
        success: false,
        message: result.error || "Erro ao criar usuário"
      };
    }

    const userId = result.userId;

    // Enviar email com as credenciais
    try {
      const emailSent = await sendWelcomeWithCredentials(email, name, userPassword, clubName);
      console.log(`Email de boas-vindas ${emailSent ? 'enviado com sucesso' : 'falhou ao enviar'} para ${email}`);
    } catch (emailError) {
      console.error("Erro ao enviar email de boas-vindas:", emailError);
      // Não interrompemos o fluxo se o email falhar
    }

    return {
      userId,
      success: true,
      message: "Usuário criado com sucesso"
    };
  } catch (error) {
    console.error("Erro ao criar usuário via Edge Function:", error);
    return {
      userId: null,
      success: false,
      message: error instanceof Error ? error.message : "Erro desconhecido"
    };
  }
}
