import { useState } from "react";
import { useSeasonStore } from "@/store/useSeasonStore";
import { SeasonDialog } from "./SeasonDialog";
import { Pencil, Trash2, Plus } from "lucide-react";
import { toast } from "react-toastify";
import { deleteSeason } from "@/api/seasonApi";

interface SeasonManagerDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
}

export function SeasonManagerDialog({ open, onOpenChange, clubId }: SeasonManagerDialogProps) {
  const { seasons, fetchSeasons, setActiveSeason, activeSeason } = useSeasonStore();
  const [editDialog, setEditDialog] = useState<{ open: boolean; season: any | null }>({ open: false, season: null });
  const [deleteConfirm, setDeleteConfirm] = useState<{ open: boolean; season: any | null }>({ open: false, season: null });
  const [dialogOpen, setDialogOpen] = useState(false);

  if (!open) return null;
  return (
    <div className="fixed inset-0 bg-black bg-opacity-40 z-50 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-2xl flex flex-col gap-4 relative">
        <button className="absolute top-3 right-3 text-gray-400 hover:text-gray-600" onClick={() => onOpenChange(false)}>
          <span className="text-xl">&times;</span>
        </button>
        <div className="flex items-center justify-between mb-2">
          <h2 className="font-bold text-xl">Gerenciar Temporadas</h2>
          <button
            className="flex items-center gap-1 px-3 py-1 rounded bg-blue-600 text-white hover:bg-blue-700 shadow-sm text-sm font-medium"
            onClick={() => setDialogOpen(true)}
            type="button"
          >
            <Plus size={18} /> Nova Temporada
          </button>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full text-sm border rounded-lg">
            <thead>
              <tr className="bg-gray-50 text-gray-700">
                <th className="py-2 px-3 text-left">Nome</th>
                <th className="py-2 px-3 text-left">Datas</th>
                <th className="py-2 px-3 text-left">Status</th>
                <th className="py-2 px-3 text-center">Ações</th>
              </tr>
            </thead>
            <tbody>
              {seasons.map(season => (
                <tr key={season.id} className={`border-b ${activeSeason?.id === season.id ? 'bg-blue-50' : ''}`}>
                  <td className="py-2 px-3 font-medium">{season.name}</td>
                  <td className="py-2 px-3">{season.start_date} - {season.end_date}</td>
                  <td className="py-2 px-3">{activeSeason?.id === season.id ? <span className="text-blue-600 font-semibold">Ativa</span> : <span className="text-gray-400">Inativa</span>}</td>
                  <td className="py-2 px-3 flex gap-2 justify-center">
                    <button
                      className="p-1 rounded hover:bg-yellow-50 text-yellow-600 border border-transparent hover:border-yellow-300"
                      onClick={() => setEditDialog({ open: true, season })}
                      type="button"
                      title="Editar"
                    >
                      <Pencil size={18} />
                    </button>
                    <button
                      className="p-1 rounded hover:bg-red-50 text-red-600 border border-transparent hover:border-red-300"
                      onClick={() => setDeleteConfirm({ open: true, season })}
                      type="button"
                      title="Excluir"
                    >
                      <Trash2 size={18} />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <SeasonDialog open={dialogOpen} onOpenChange={setDialogOpen} clubId={clubId} />
        {editDialog.open && (
          <SeasonDialog
            open={editDialog.open}
            onOpenChange={open => setEditDialog({ open, season: open ? editDialog.season : null })}
            clubId={clubId}
            season={{
              ...editDialog.season,
              club_id: (editDialog.season && 'club_id' in editDialog.season)
                ? editDialog.season.club_id
                : clubId
            }}
          />
        )}
        {deleteConfirm.open && (
          <div className="fixed inset-0 bg-black bg-opacity-30 z-50 flex items-center justify-center">
            <div className="bg-white rounded shadow p-6 w-full max-w-xs flex flex-col gap-3">
              <h2 className="font-semibold text-lg mb-2">Excluir Temporada</h2>
              <p>Tem certeza que deseja excluir a temporada <b>{deleteConfirm.season.name}</b>?</p>
              <div className="flex gap-2 mt-2">
                <button type="button" className="flex-1 border rounded py-1" onClick={() => setDeleteConfirm({ open: false, season: null })}>
                  Cancelar
                </button>
                <button type="button" className="flex-1 bg-red-600 text-white rounded py-1"
                  onClick={async () => {
                    try {
                      // Função de exclusão
                      if (deleteConfirm.season) {
                        // Usar a função da API em vez de fazer fetch diretamente
                        await deleteSeason(clubId, deleteConfirm.season.id);

                        toast.success('Temporada excluída com sucesso');

                        // Se a temporada excluída for a ativa, limpar a seleção
                        if (activeSeason && activeSeason.id === deleteConfirm.season.id) {
                          setActiveSeason(null);
                        }

                        await fetchSeasons(clubId);
                      }
                    } catch (error: any) {
                      toast.error(error.message || 'Erro ao excluir temporada');
                      console.error('Erro ao excluir temporada:', error);
                    } finally {
                      setDeleteConfirm({ open: false, season: null });
                    }
                  }}>
                  Excluir
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
