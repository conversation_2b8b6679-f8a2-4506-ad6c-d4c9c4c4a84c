import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import type { UpcomingMatch } from "@/api/api";

interface GameDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  game?: UpcomingMatch;
}

export function GameDialog({ open, onOpenChange, game }: GameDialogProps) {
  const [opponent, setOpponent] = useState(game?.opponent || "");
  const [date, setDate] = useState(game?.date || "");
  const [gameType, setGameType] = useState(game?.type || "");
  const [error, setError] = useState("");

  const handleSave = () => {
    if (!opponent.trim()) {
      setError("O adversário é obrigatório.");
      return;
    }
    if (!date) {
      setError("A data é obrigatória.");
      return;
    }
    setError("");
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Adicionar/Editar Jogo</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <Input placeholder="Adversário*" value={opponent} onChange={e => setOpponent(e.target.value)} />
          <Input type="date" value={date} onChange={e => setDate(e.target.value)} />
          <Input placeholder="Tipo de jogo" value={gameType} onChange={e => setGameType(e.target.value)} />
          {error && <div className="text-red-500 text-sm mt-1">{error}</div>}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancelar</Button>
          <Button onClick={handleSave} disabled={!opponent.trim() || !date}>Salvar</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
