import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState, useEffect } from "react";
import { useStaffStore } from "@/store/useStaffStore";
import type { Staff } from "@/api/api";

interface StaffDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  staff?: Staff;
  editMode?: boolean;
  clubId: number;
}

export function StaffDialog({ open, onOpenChange, staff, editMode, clubId }: StaffDialogProps) {
  const [name, setName] = useState(staff?.name || "");
  const [role, setRole] = useState(staff?.role || "");
  const [age, setAge] = useState(staff?.age?.toString() || "");
  const [nationality, setNationality] = useState(staff?.nationality || "");
  const [experience, setExperience] = useState(staff?.experience || "");
  const [error, setError] = useState("");

  const { addStaff, updateStaff, loading: loadingStaff } = useStaffStore();

  // Preencher campos ao abrir em modo edição
  useEffect(() => {
    if (open && editMode && staff) {
      setName(staff.name || "");
      setRole(staff.role || "");
      setAge(staff.age?.toString() || "");
      setNationality(staff.nationality || "");
      setExperience(staff.experience || "");
    } else if (open && !editMode) {
      setName("");
      setRole("");
      setAge("");
      setNationality("");
      setExperience("");
    }
  }, [open, editMode, staff]);

  const handleSave = async () => {
    if (!name.trim()) {
      setError("O nome é obrigatório.");
      return;
    }
    if (!role.trim()) {
      setError("O cargo é obrigatório.");
      return;
    }
    setError("");
    const staffData = {
      name,
      role,
      age: age ? parseInt(age) : undefined,
      nationality,
      experience,
      club_id: clubId,
    };
    try {
      if (editMode && staff?.id) {
        await updateStaff(clubId, Number(staff.id), staffData);
      } else {
        await addStaff(clubId, staffData);
      }
      onOpenChange(false);
    } catch (err: unknown) {
      const error = err as Error;
      setError(error.message || "Erro ao salvar staff");
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader className="pb-4 border-b">
          <DialogTitle className="text-xl font-semibold flex items-center gap-2">
            {editMode ? (
              <>
                <span className="w-2 h-2 bg-primary rounded-full"></span>
                Editar Staff
              </>
            ) : (
              <>
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                Adicionar Staff
              </>
            )}
          </DialogTitle>
          {error && (
            <div className="mt-2 p-3 bg-destructive/10 border border-destructive/20 rounded-md">
              <p className="text-sm text-destructive font-medium">{error}</p>
            </div>
          )}
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">
              Nome <span className="text-destructive">*</span>
            </label>
            <Input
              placeholder="Nome completo"
              value={name}
              onChange={e => setName(e.target.value)}
              className={error && !name.trim() ? "border-destructive" : ""}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">
              Cargo <span className="text-destructive">*</span>
            </label>
            <Input
              placeholder="Cargo/Função"
              value={role}
              onChange={e => setRole(e.target.value)}
              className={error && !role.trim() ? "border-destructive" : ""}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Idade</label>
              <Input
                placeholder="Idade"
                value={age}
                onChange={e => setAge(e.target.value)}
                type="number"
                min={18}
                max={80}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Nacionalidade</label>
              <Input
                placeholder="Nacionalidade"
                value={nationality}
                onChange={e => setNationality(e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Experiência</label>
            <Input
              placeholder="Experiência profissional"
              value={experience}
              onChange={e => setExperience(e.target.value)}
            />
          </div>
        </div>

        <DialogFooter className="pt-4 border-t bg-muted/20">
          <div className="flex gap-3 w-full">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="flex-1"
            >
              Cancelar
            </Button>
            <Button
              onClick={handleSave}
              disabled={!name.trim() || !role.trim() || loadingStaff}
              className="flex-1 bg-primary hover:bg-primary/90"
            >
              {loadingStaff ? (
                <>
                  <span className="animate-spin mr-2">⏳</span>
                  Salvando...
                </>
              ) : (
                editMode ? "Atualizar Staff" : "Adicionar Staff"
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
