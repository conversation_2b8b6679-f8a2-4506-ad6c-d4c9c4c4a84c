-- Add loan-related fields to players table
ALTER TABLE players
ADD COLUMN loan_end_date TIMESTAMP WITH TIME ZONE,
ADD COLUMN loan_club_name TEXT;

-- Add rejection_reason to player_documents if it doesn't exist
ALTER TABLE player_documents
ADD COLUMN IF NOT EXISTS rejection_reason TEXT;

-- Function to remove all player associations when status becomes 'inativo'
CREATE OR REPLACE FUNCTION remove_player_associations(p_club_id INTEGER, p_player_id UUID)
RETURNS VOID AS $$
DECLARE
  v_user_id UUID;
BEGIN
  -- Log the operation
  RAISE NOTICE 'Removendo vinculações do jogador % do clube %', p_player_id, p_club_id;

  -- Get the user_id associated with this player
  SELECT user_id INTO v_user_id
  FROM players
  WHERE id = p_player_id AND club_id = p_club_id;

  -- 1. Remove from categories
  DELETE FROM player_categories
  WHERE club_id = p_club_id AND player_id = p_player_id;

  -- 2. Remove from accommodations (set status to 'completed' instead of deleting for history)
  UPDATE player_accommodations
  SET status = 'completed', check_out_date = CURRENT_DATE
  WHERE club_id = p_club_id AND player_id = p_player_id AND status = 'active';

  -- 3. Deactivate salaries (set status to 'inactive' instead of deleting for history)
  UPDATE player_salaries
  SET status = 'inactive', end_date = CURRENT_DATE
  WHERE club_id = p_club_id AND player_id = p_player_id AND status = 'active';

  -- 4. Remove from training participants
  DELETE FROM training_players
  WHERE club_id = p_club_id AND player_id = p_player_id;

  -- 5. Remove from callups (only future callups)
  DELETE FROM callup_players
  WHERE club_id = p_club_id AND player_id = p_player_id
  AND callup_id IN (
    SELECT id FROM callups
    WHERE club_id = p_club_id AND match_date > NOW()
  );

  -- 6. Deactivate player accounts (suspend access)
  UPDATE player_accounts
  SET expires_at = NOW()
  WHERE club_id = p_club_id AND player_id = p_player_id;

  -- 7. Remove user from club_members (suspend system access)
  IF v_user_id IS NOT NULL THEN
    UPDATE club_members
    SET status = 'inativo'
    WHERE club_id = p_club_id AND user_id = v_user_id;

    RAISE NOTICE 'Usuário % suspenso do clube %', v_user_id, p_club_id;
  END IF;

  -- 8. Remove from agenda events participants
  UPDATE agenda_events
  SET participants = array_remove(participants, p_player_id::text)
  WHERE club_id = p_club_id AND participants @> ARRAY[p_player_id::text];

  -- 9. Mark medical records as inactive (keep for history)
  UPDATE medical_records
  SET status = 'inactive'
  WHERE club_id = p_club_id AND player_id = p_player_id::text AND status != 'completed';

  -- 10. Cancel pending salary advances
  UPDATE salary_advances
  SET status = 'cancelled'
  WHERE club_id = p_club_id AND person_id::text = p_player_id::text
  AND person_type = 'player' AND status = 'active';

  -- 11. Mark evaluation invitations as inactive
  UPDATE player_evaluation_invitations
  SET status = 'inactive'
  WHERE club_id = p_club_id AND player_id = p_player_id AND status = 'pending';

  RAISE NOTICE 'Vinculações removidas e usuário suspenso com sucesso para o jogador %', p_player_id;
END;
$$ LANGUAGE plpgsql;

-- Function to handle player status changes
CREATE OR REPLACE FUNCTION handle_player_status_change()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if status changed to 'inativo'
  IF NEW.status = 'inativo' AND (OLD.status IS NULL OR OLD.status != 'inativo') THEN
    -- Remove all associations
    PERFORM remove_player_associations(NEW.club_id, NEW.id);

    -- Log the status change
    RAISE NOTICE 'Jogador % alterado para status inativo. Vinculações removidas.', NEW.id;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically handle status changes
DROP TRIGGER IF EXISTS trigger_player_status_change ON players;

CREATE TRIGGER trigger_player_status_change
  AFTER UPDATE OF status ON players
  FOR EACH ROW
  EXECUTE FUNCTION handle_player_status_change();

-- Function to completely remove a player and their user account
CREATE OR REPLACE FUNCTION remove_player_completely(p_club_id INTEGER, p_player_id UUID)
RETURNS VOID AS $$
DECLARE
  v_user_id UUID;
BEGIN
  -- Log the operation
  RAISE NOTICE 'Removendo jogador % completamente do clube %', p_player_id, p_club_id;

  -- Get the user_id associated with this player
  SELECT user_id INTO v_user_id
  FROM players
  WHERE id = p_player_id AND club_id = p_club_id;

  -- First remove all associations (same as inactivation)
  PERFORM remove_player_associations(p_club_id, p_player_id);

  -- Remove from all tables that reference this player
  -- (This is handled by the removePlayerDependencies function in the API)

  -- If player has a user account, mark it for deletion
  IF v_user_id IS NOT NULL THEN
    -- Remove user from club_members completely
    DELETE FROM club_members
    WHERE club_id = p_club_id AND user_id = v_user_id;

    -- Remove player_accounts entry
    DELETE FROM player_accounts
    WHERE club_id = p_club_id AND player_id = p_player_id;

    -- Note: The actual user deletion from auth.users should be handled by the Edge Function
    -- We'll mark this in a way that the API can identify and call the delete-user function

    RAISE NOTICE 'Usuário % marcado para exclusão completa', v_user_id;
  END IF;

  RAISE NOTICE 'Jogador % removido completamente do sistema', p_player_id;
END;
$$ LANGUAGE plpgsql;
