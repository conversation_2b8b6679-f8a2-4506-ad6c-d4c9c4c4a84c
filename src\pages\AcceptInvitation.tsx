import { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/use-toast";
import { acceptInvitationWithPassword } from "@/api/auth";
import { CheckCircle } from "lucide-react";

export default function AcceptInvitation() {
  const [searchParams] = useSearchParams();
  const token = searchParams.get("token");
  const navigate = useNavigate();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [invitation, setInvitation] = useState<any>(null);
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [success, setSuccess] = useState(false);

  // Carregar informações do convite
  useEffect(() => {
    async function loadInvitation() {
      if (!token) {
        setError("Token de convite não fornecido");
        setLoading(false);
        return;
      }

      try {
        console.log(`Carregando convite com token: ${token}`);

        // Buscar o convite diretamente do banco de dados
        const { supabase } = await import('@/integrations/supabase/client');

        const { data, error } = await supabase
          .from("user_invitations")
          .select("*")
          .eq("token", token)
          .limit(1);

        if (error) {
          console.error(`Erro ao obter convite: ${error.message}`, error);
          setError(`Erro ao carregar convite: ${error.message}`);
          toast({
            title: "Erro",
            description: `Erro ao carregar convite: ${error.message}`,
            variant: "destructive",
          });
          setLoading(false);
          return;
        }

        if (!data || data.length === 0) {
          console.error(`Nenhum convite encontrado com o token: ${token}`);
          setError("Convite não encontrado ou expirado");
          toast({
            title: "Erro",
            description: "Convite não encontrado ou expirado",
            variant: "destructive",
          });
          setLoading(false);
          return;
        }

        const invitationData = data[0];
        console.log(`Convite encontrado para o email: ${invitationData.email}`);
        setInvitation(invitationData);
      } catch (err: any) {
        console.error("Erro ao carregar convite:", err);
        setError(err.message || "Erro ao carregar convite");
        toast({
          title: "Erro",
          description: err.message || "Erro ao carregar convite",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    }

    loadInvitation();
  }, [token]);

  // Função para aceitar o convite
  const handleAcceptInvitation = async () => {
    if (!token) {
      const errorMsg = "Token de convite não fornecido";
      setError(errorMsg);
      toast({
        title: "Erro",
        description: errorMsg,
        variant: "destructive",
      });
      return;
    }

    if (!password) {
      const errorMsg = "Senha é obrigatória";
      setError(errorMsg);
      toast({
        title: "Erro",
        description: errorMsg,
        variant: "destructive",
      });
      return;
    }

    if (password.length < 6) {
      const errorMsg = "A senha deve ter pelo menos 6 caracteres";
      setError(errorMsg);
      toast({
        title: "Erro",
        description: errorMsg,
        variant: "destructive",
      });
      return;
    }

    if (password !== confirmPassword) {
      const errorMsg = "As senhas não coincidem";
      setError(errorMsg);
      toast({
        title: "Erro",
        description: errorMsg,
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log(`Tentando aceitar convite com token: ${token}`);
      await acceptInvitationWithPassword(token, password);
      console.log("Convite aceito com sucesso");

      setSuccess(true);
      toast({
        title: "Sucesso",
        description: "Convite aceito com sucesso. Você pode fazer login agora.",
      });

      // Redirecionar para a página de login após 3 segundos
      setTimeout(() => {
        navigate("/login");
      }, 3000);
    } catch (err: any) {
      console.error("Erro ao aceitar convite:", err);
      const errorMessage = err.message || "Erro ao aceitar convite";
      setError(errorMessage);
      toast({
        title: "Erro",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-team-blue"></div>
      </div>
    );
  }

  if (error && !invitation) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Erro ao Carregar Convite</CardTitle>
            <CardDescription>Não foi possível carregar as informações do convite.</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-red-500">{error}</p>
          </CardContent>
          <CardFooter>
            <Button onClick={() => navigate("/login")} className="w-full">
              Voltar para Login
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  if (success) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-6 w-6 text-green-500" />
              Convite Aceito com Sucesso
            </CardTitle>
            <CardDescription>Sua conta foi criada com sucesso.</CardDescription>
          </CardHeader>
          <CardContent>
            <p>Você será redirecionado para a página de login em alguns segundos...</p>
          </CardContent>
          <CardFooter>
            <Button onClick={() => navigate("/login")} className="w-full">
              Ir para Login
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex justify-center items-center min-h-screen">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Aceitar Convite</CardTitle>
          <CardDescription>
            Você foi convidado para acessar a plataforma. Defina sua senha para continuar.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={invitation?.email || ""}
              disabled
              className="bg-gray-100"
            />
          </div>
          <div>
            <Label htmlFor="password">Senha*</Label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Digite sua senha"
              required
            />
          </div>
          <div>
            <Label htmlFor="confirmPassword">Confirmar Senha*</Label>
            <Input
              id="confirmPassword"
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              placeholder="Confirme sua senha"
              required
            />
          </div>
          {error && <p className="text-red-500 text-sm">{error}</p>}
        </CardContent>
        <CardFooter className="flex flex-col gap-4">
          <Button
            onClick={handleAcceptInvitation}
            disabled={loading}
            className="w-full"
          >
            {loading ? "Processando..." : "Aceitar Convite"}
          </Button>
          <Button
            variant="outline"
            onClick={() => navigate("/login")}
            className="w-full"
          >
            Voltar para Login
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
