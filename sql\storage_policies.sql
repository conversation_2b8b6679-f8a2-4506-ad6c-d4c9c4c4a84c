-- Configurar políticas de acesso para o bucket profileimages
-- Permitir acesso de leitura público para todos os arquivos
INSERT INTO storage.policies (name, definition, bucket_id)
VALUES (
  'Public Read Access for profileimages',
  '(bucket_id = ''profileimages''::text)',
  'profileimages'
);

-- Permitir acesso de leitura público para todos os arquivos no bucket playerdocuments
INSERT INTO storage.policies (name, definition, bucket_id)
VALUES (
  'Public Read Access for playerdocuments',
  '(bucket_id = ''playerdocuments''::text)',
  'playerdocuments'
);

-- Permitir acesso de escrita para usuários autenticados no bucket profileimages
INSERT INTO storage.policies (name, definition, bucket_id)
VALUES (
  'Authenticated Users can upload to profileimages',
  '(bucket_id = ''profileimages''::text) AND (auth.role() = ''authenticated''::text)',
  'profileimages'
);

-- Permitir acesso de escrita para usuários autenticados no bucket playerdocuments
INSERT INTO storage.policies (name, definition, bucket_id)
VALUES (
  'Authenticated Users can upload to playerdocuments',
  '(bucket_id = ''playerdocuments''::text) AND (auth.role() = ''authenticated''::text)',
  'playerdocuments'
);
