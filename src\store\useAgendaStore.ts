import { create } from "zustand";
import { AgendaEvent } from "../api/api";
import { getAgendaEvents, createAgendaEvent, updateAgendaEvent, deleteAgendaEvent } from "../api/api";

interface AgendaState {
  agenda: AgendaEvent[];
  loading: boolean;
  error: string | null;
  fetchAgenda: (clubId: number) => Promise<void>;
  addAgendaEvent: (clubId: number, event: Omit<AgendaEvent, "id">) => Promise<void>;
  updateAgendaEvent: (clubId: number, id: number, event: Partial<AgendaEvent>) => Promise<void>;
  deleteAgendaEvent: (clubId: number, id: number) => Promise<void>;
}

export const useAgendaStore = create<AgendaState>((set) => ({
  agenda: [],
  loading: false,
  error: null,

  fetchAgenda: async (clubId: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const agenda = await getAgendaEvents(clubId);
      set({ agenda, loading: false });
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao buscar agenda", loading: false });
    }
  },

  addAgendaEvent: async (clubId: number, event: Omit<AgendaEvent, "id">): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const newEvent = await createAgendaEvent(clubId, event);
      set((state) => ({ agenda: [...state.agenda, newEvent], loading: false }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao adicionar evento", loading: false });
    }
  },

  updateAgendaEvent: async (clubId: number, id: number, event: Partial<AgendaEvent>): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const updated = await updateAgendaEvent(clubId, id, event);
      if (updated) {
        set((state) => ({ agenda: state.agenda.map(e => e.id === id ? updated : e), loading: false }));
      } else {
        set({ error: "Evento não encontrado", loading: false });
      }
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao atualizar evento", loading: false });
    }
  },

  deleteAgendaEvent: async (clubId: number, id: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const ok = await deleteAgendaEvent(clubId, id);
      if (ok) {
        set((state) => ({ agenda: state.agenda.filter(e => e.id !== id), loading: false }));
      } else {
        set({ error: "Evento não encontrado", loading: false });
      }
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao deletar evento", loading: false });
    }
  },
}));
