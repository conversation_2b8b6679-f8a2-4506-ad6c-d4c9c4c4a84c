import { create } from "zustand";
import { getUserPermissions, hasPermission as checkPermission } from "@/api/permissions";

interface PermissionsState {
  // State
  role: string | null;
  permissions: Record<string, boolean>;
  isLoaded: boolean;
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchUserPermissions: (clubId: number, userId: string) => Promise<void>;
  hasPermission: (clubId: number, userId: string, permission: string) => Promise<boolean>;
  hasAnyPermission: (clubId: number, userId: string, permissions: string[]) => Promise<boolean>;
  hasAllPermissions: (clubId: number, userId: string, permissions: string[]) => Promise<boolean>;
  clearPermissions: () => void;
}

export const usePermissionsStore = create<PermissionsState>((set, get) => ({
  // Initial state
  role: null,
  permissions: {},
  isLoaded: false,
  loading: false,
  error: null,
  
  // Fetch user permissions and store them
  fetchUserPermissions: async (clubId: number, userId: string) => {
    try {
      set({ loading: true, error: null });
      
      const userPermissions = await getUserPermissions(clubId, userId);
      
      if (userPermissions) {
        set({ 
          role: userPermissions.role,
          permissions: userPermissions.permissions,
          isLoaded: true,
          loading: false
        });
      } else {
        set({ 
          role: null,
          permissions: {},
          isLoaded: true,
          loading: false
        });
      }
    } catch (err: any) {
      console.error("Erro ao carregar permissões:", err);
      set({ 
        error: err.message || "Erro ao carregar permissões",
        loading: false
      });
    }
  },
  
  // Check if user has a specific permission
  hasPermission: async (clubId: number, userId: string, permission: string) => {
    // If permissions are already loaded, check locally
    if (get().isLoaded) {
      const { role, permissions } = get();
      
      // Admin and president roles have special handling
      if (role === "president") return true;
      if (role === "admin" && !permission.startsWith("president.")) return true;
      
      // Check specific permission
      return !!permissions[permission];
    }
    
    // Otherwise, check via API
    try {
      return await checkPermission(clubId, userId, permission);
    } catch (err) {
      console.error("Erro ao verificar permissão:", err);
      return false;
    }
  },
  
  // Check if user has any of the specified permissions
  hasAnyPermission: async (clubId: number, userId: string, permissions: string[]) => {
    // If permissions are already loaded, check locally
    if (get().isLoaded) {
      const { role, permissions: userPermissions } = get();
      
      // Admin and president roles have special handling
      if (role === "president") return true;
      if (role === "admin" && !permissions.some(p => p.startsWith("president."))) return true;
      
      // Check if user has any of the specified permissions
      return permissions.some(p => !!userPermissions[p]);
    }
    
    // Otherwise, check each permission via API
    try {
      for (const permission of permissions) {
        if (await checkPermission(clubId, userId, permission)) {
          return true;
        }
      }
      return false;
    } catch (err) {
      console.error("Erro ao verificar permissões:", err);
      return false;
    }
  },
  
  // Check if user has all of the specified permissions
  hasAllPermissions: async (clubId: number, userId: string, permissions: string[]) => {
    // If permissions are already loaded, check locally
    if (get().isLoaded) {
      const { role, permissions: userPermissions } = get();
      
      // Admin and president roles have special handling
      if (role === "president") return true;
      if (role === "admin" && !permissions.some(p => p.startsWith("president."))) return true;
      
      // Check if user has all of the specified permissions
      return permissions.every(p => !!userPermissions[p]);
    }
    
    // Otherwise, check each permission via API
    try {
      for (const permission of permissions) {
        if (!(await checkPermission(clubId, userId, permission))) {
          return false;
        }
      }
      return true;
    } catch (err) {
      console.error("Erro ao verificar permissões:", err);
      return false;
    }
  },
  
  // Clear permissions (e.g., on logout)
  clearPermissions: () => {
    set({
      role: null,
      permissions: {},
      isLoaded: false,
      loading: false,
      error: null
    });
  }
}));
