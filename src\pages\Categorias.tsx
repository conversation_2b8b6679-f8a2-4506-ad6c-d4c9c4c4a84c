import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { Plus, Edit, Trash2, Users, Tag, ChevronRight } from "lucide-react";
import { useCurrentClubId } from "@/context/ClubContext";
import { useCategoriesStore } from "@/store/useCategoriesStore";
import { usePlayersStore } from "@/store/usePlayersStore";
import { Category, Player } from "@/api/api";

const positionMap: Record<string, string> = {
  "goleiro": "GOL",
  "zagueiro": "ZAG",
  "lateral-direito": "LD",
  "lateral-esquerdo": "LE",
  "volante": "VOL",
  "meia": "MEI",
  "atacante": "ATA",
  "extremo-direito": "PD",
  "extremo-esquerdo": "PE"
};

const statusColors: Record<string, string> = {
  "ativo": "text-green-700 bg-green-100",
  "lesionado": "text-red-700 bg-red-100",
  "emprestado": "text-amber-700 bg-amber-100",
  "suspenso": "text-purple-700 bg-purple-100"
};

export default function Categorias() {
  const clubId = useCurrentClubId();
  const navigate = useNavigate();

  // Estados
  const [searchQuery, setSearchQuery] = useState("");
  const [isAddCategoryDialogOpen, setIsAddCategoryDialogOpen] = useState(false);
  const [isEditCategoryDialogOpen, setIsEditCategoryDialogOpen] = useState(false);
  const [isAssignPlayerDialogOpen, setIsAssignPlayerDialogOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [categoryPlayers, setCategoryPlayers] = useState<Player[]>([]);
  const [availablePlayers, setAvailablePlayers] = useState<Player[]>([]);
  const [selectedPlayerId, setSelectedPlayerId] = useState<string>("");

  // Formulário para categoria
  const [categoryForm, setCategoryForm] = useState({
    name: "",
    type: "age_group",
    description: ""
  });

  // Stores
  const {
    categories,
    loading,
    error,
    fetchCategories,
    addCategory,
    updateCategory,
    deleteCategory,
    getCategoryPlayers,
    assignPlayerToCategory,
    removePlayerFromCategory,
    migrateYouthPlayers
  } = useCategoriesStore();

  const { players, fetchPlayers } = usePlayersStore();

  // Efeitos
  useEffect(() => {
    if (clubId) {
      fetchCategories(clubId);
      fetchPlayers(clubId);
    }
  }, [clubId, fetchCategories, fetchPlayers]);

  useEffect(() => {
    if (error) {
      toast({
        title: "Erro",
        description: error,
        variant: "destructive"
      });
    }
  }, [error]);

  useEffect(() => {
    if (categories.length > 0 && !selectedCategory) {
      setSelectedCategory(categories[0]);
    }
  }, [categories, selectedCategory]);

  useEffect(() => {
    const loadCategoryPlayers = async () => {
      if (selectedCategory) {
        try {
          const playersData = await getCategoryPlayers(clubId, selectedCategory.id);
          setCategoryPlayers(playersData);

          // Filtrar jogadores disponíveis (que não estão nesta categoria)
          const playerIds = playersData.map(p => p.id);
          setAvailablePlayers(players.filter(p => !playerIds.includes(p.id)));
        } catch (err) {
          console.error("Erro ao carregar jogadores da categoria:", err);
        }
      }
    };

    loadCategoryPlayers();
  }, [selectedCategory, clubId, getCategoryPlayers, players]);

  // Filtrar categorias com base na pesquisa
  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    category.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Handlers
  const handleAddCategory = () => {
    setCategoryForm({
      name: "",
      type: "age_group",
      description: ""
    });
    setIsAddCategoryDialogOpen(true);
  };

  const handleEditCategory = (category: Category) => {
    setCategoryForm({
      name: category.name,
      type: category.type,
      description: category.description || ""
    });
    setSelectedCategory(category);
    setIsEditCategoryDialogOpen(true);
  };

  const handleDeleteCategory = async (category: Category) => {
    if (window.confirm(`Tem certeza que deseja excluir a categoria ${category.name}?`)) {
      try {
        await deleteCategory(clubId, category.id);
        toast({
          title: "Categoria excluída",
          description: `A categoria ${category.name} foi excluída com sucesso.`,
        });

        if (selectedCategory?.id === category.id) {
          setSelectedCategory(categories.length > 1 ? categories[0] : null);
        }
      } catch (err: any) {
        toast({
          title: "Erro ao excluir categoria",
          description: err.message || "Ocorreu um erro ao excluir a categoria.",
          variant: "destructive"
        });
      }
    }
  };

  const handleSaveCategory = async () => {
    if (!categoryForm.name) {
      toast({
        title: "Erro",
        description: "O nome da categoria é obrigatório.",
        variant: "destructive"
      });
      return;
    }

    try {
      await addCategory(clubId, categoryForm);
      toast({
        title: "Categoria adicionada",
        description: `A categoria ${categoryForm.name} foi adicionada com sucesso.`,
      });
      setIsAddCategoryDialogOpen(false);
    } catch (err: any) {
      toast({
        title: "Erro ao adicionar categoria",
        description: err.message || "Ocorreu um erro ao adicionar a categoria.",
        variant: "destructive"
      });
    }
  };

  const handleUpdateCategory = async () => {
    if (!categoryForm.name || !selectedCategory) {
      toast({
        title: "Erro",
        description: "O nome da categoria é obrigatório.",
        variant: "destructive"
      });
      return;
    }

    try {
      await updateCategory(clubId, selectedCategory.id, categoryForm);
      toast({
        title: "Categoria atualizada",
        description: `A categoria ${categoryForm.name} foi atualizada com sucesso.`,
      });
      setIsEditCategoryDialogOpen(false);
    } catch (err: any) {
      toast({
        title: "Erro ao atualizar categoria",
        description: err.message || "Ocorreu um erro ao atualizar a categoria.",
        variant: "destructive"
      });
    }
  };

  const handleAssignPlayer = async () => {
    if (!selectedPlayerId || !selectedCategory) {
      toast({
        title: "Erro",
        description: "Selecione um jogador para adicionar à categoria.",
        variant: "destructive"
      });
      return;
    }

    try {
      await assignPlayerToCategory(clubId, selectedPlayerId, selectedCategory.id);
      toast({
        title: "Jogador adicionado",
        description: "Jogador adicionado à categoria com sucesso.",
      });
      setIsAssignPlayerDialogOpen(false);

      // Recarregar jogadores da categoria
      const playersData = await getCategoryPlayers(clubId, selectedCategory.id);
      setCategoryPlayers(playersData);

      // Atualizar jogadores disponíveis
      const playerIds = playersData.map(p => p.id);
      setAvailablePlayers(players.filter(p => !playerIds.includes(p.id)));

      // Limpar seleção
      setSelectedPlayerId("");
    } catch (err: any) {
      toast({
        title: "Erro ao adicionar jogador",
        description: err.message || "Ocorreu um erro ao adicionar o jogador à categoria.",
        variant: "destructive"
      });
    }
  };

  const handleRemovePlayer = async (playerId: string) => {
    if (!selectedCategory) return;

    if (window.confirm("Tem certeza que deseja remover este jogador da categoria?")) {
      try {
        await removePlayerFromCategory(clubId, playerId, selectedCategory.id);
        toast({
          title: "Jogador removido",
          description: "Jogador removido da categoria com sucesso.",
        });

        // Recarregar jogadores da categoria
        const playersData = await getCategoryPlayers(clubId, selectedCategory.id);
        setCategoryPlayers(playersData);

        // Atualizar jogadores disponíveis
        const playerIds = playersData.map(p => p.id);
        setAvailablePlayers(players.filter(p => !playerIds.includes(p.id)));
      } catch (err: any) {
        toast({
          title: "Erro ao remover jogador",
          description: err.message || "Ocorreu um erro ao remover o jogador da categoria.",
          variant: "destructive"
        });
      }
    }
  };

  const handleMigrateYouthPlayers = async () => {
    if (window.confirm("Deseja migrar os jogadores da base juvenil para o novo sistema de categorias? Esta ação criará categorias padrão (Sub-15, Sub-17, Sub-20, Profissional) e migrará os jogadores automaticamente.")) {
      try {
        await migrateYouthPlayers(clubId);
        toast({
          title: "Migração concluída",
          description: "Jogadores da base juvenil migrados com sucesso para o novo sistema de categorias.",
        });

        // Recarregar categorias e jogadores
        fetchCategories(clubId);
        fetchPlayers(clubId);
      } catch (err: any) {
        toast({
          title: "Erro na migração",
          description: err.message || "Ocorreu um erro ao migrar os jogadores da base juvenil.",
          variant: "destructive"
        });
      }
    }
  };

  return (
    <div>
      <div className="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Categorias</h1>
          <p className="text-muted-foreground">
            Gerencie as categorias e os jogadores em cada categoria
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleAddCategory}>
            <Plus className="h-4 w-4 mr-1" />
            Nova Categoria
          </Button>
          <Button variant="outline" onClick={handleMigrateYouthPlayers}>
            <ChevronRight className="h-4 w-4 mr-1" />
            Migrar Base Juvenil
          </Button>
        </div>
      </div>

      <div className="mb-6">
        <Input
          placeholder="Buscar categoria por nome ou descrição..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-lg"
        />
      </div>

      {loading && !categories.length ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-team-blue"></div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Coluna da esquerda - Lista de categorias */}
          <Card className="md:col-span-1">
            <CardHeader>
              <CardTitle>Lista de Categorias</CardTitle>
            </CardHeader>
            <CardContent>
              {filteredCategories.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  Nenhuma categoria encontrada
                </div>
              ) : (
                <div className="space-y-2">
                  {filteredCategories.map((category) => (
                    <div
                      key={category.id}
                      className={`p-3 rounded-md cursor-pointer hover:bg-gray-100 flex justify-between items-center ${
                        selectedCategory?.id === category.id ? "bg-gray-100" : ""
                      }`}
                      onClick={() => setSelectedCategory(category)}
                    >
                      <div>
                        <div className="font-medium">{category.name}</div>
                        <div className="text-sm text-gray-500">
                          {category.type === "age_group" ? "Faixa Etária" :
                           category.type === "championship" ? "Campeonato" : "Personalizada"}
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditCategory(category);
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteCategory(category);
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Coluna da direita - Detalhes da categoria */}
          <Card className="md:col-span-2">
            {!selectedCategory ? (
              <CardContent className="pt-6">
                <div className="text-center py-8 text-muted-foreground">
                  Selecione uma categoria para ver os detalhes
                </div>
              </CardContent>
            ) : (
              <>
                <CardHeader className="flex flex-row items-start justify-between">
                  <div>
                    <CardTitle>{selectedCategory.name}</CardTitle>
                    <div className="text-sm text-gray-500 mt-1">
                      {selectedCategory.type === "age_group" ? "Faixa Etária" :
                       selectedCategory.type === "championship" ? "Campeonato" : "Personalizada"}
                    </div>
                  </div>
                  <Button
                    onClick={() => setIsAssignPlayerDialogOpen(true)}
                  >
                    <Users className="h-4 w-4 mr-1" />
                    Adicionar Jogador
                  </Button>
                </CardHeader>
                <CardContent>
                  <Tabs defaultValue="players">
                    <TabsList className="mb-4">
                      <TabsTrigger value="players">Jogadores</TabsTrigger>
                      <TabsTrigger value="info">Informações</TabsTrigger>
                    </TabsList>

                    <TabsContent value="players">
                      {categoryPlayers.length === 0 ? (
                        <div className="text-center py-8 text-muted-foreground">
                          Nenhum jogador nesta categoria
                        </div>
                      ) : (
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead className="w-12">#</TableHead>
                              <TableHead>Nome</TableHead>
                              <TableHead className="hidden md:table-cell">Posição</TableHead>
                              <TableHead className="hidden md:table-cell">Idade</TableHead>
                              <TableHead className="hidden sm:table-cell">Status</TableHead>
                              <TableHead className="w-24 text-right">Ações</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {categoryPlayers.map((player) => (
                              <TableRow key={player.id}>
                                <TableCell className="font-medium">{player.number}</TableCell>
                                <TableCell>
                                  <div className="flex items-center gap-2">
                                    {player.image ? (
                                      <div className="h-8 w-8 rounded-full overflow-hidden">
                                        <img
                                          src={player.image}
                                          alt={player.name}
                                          className="h-full w-full object-cover"
                                        />
                                      </div>
                                    ) : (
                                      <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center">
                                        <span className="text-xs font-medium">{player.name.charAt(0)}</span>
                                      </div>
                                    )}
                                    <span>{player.name}</span>
                                  </div>
                                </TableCell>
                                <TableCell className="hidden md:table-cell">
                                  {positionMap[player.position] || player.position}
                                </TableCell>
                                <TableCell className="hidden md:table-cell">{player.age}</TableCell>
                                <TableCell className="hidden sm:table-cell">
                                  <Badge className={statusColors[player.status] || "bg-gray-100 text-gray-700"}>
                                    {player.status}
                                  </Badge>
                                </TableCell>
                                <TableCell className="text-right">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="text-red-500 hover:text-red-700 hover:bg-red-50"
                                    onClick={() => handleRemovePlayer(player.id)}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      )}
                    </TabsContent>

                    <TabsContent value="info">
                      <div className="space-y-4">
                        <div>
                          <h3 className="font-medium">Descrição</h3>
                          <p className="text-gray-600">
                            {selectedCategory.description || "Nenhuma descrição disponível"}
                          </p>
                        </div>

                        <div>
                          <h3 className="font-medium">Tipo</h3>
                          <p className="text-gray-600">
                            {selectedCategory.type === "age_group" ? "Faixa Etária" :
                             selectedCategory.type === "championship" ? "Campeonato" : "Personalizada"}
                          </p>
                        </div>

                        <div>
                          <h3 className="font-medium">Total de Jogadores</h3>
                          <p className="text-gray-600">{categoryPlayers.length}</p>
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </>
            )}
          </Card>
        </div>
      )}

      {/* Diálogo para adicionar nova categoria */}
      <Dialog open={isAddCategoryDialogOpen} onOpenChange={setIsAddCategoryDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Adicionar Nova Categoria</DialogTitle>
          </DialogHeader>

          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="name">Nome *</Label>
              <Input
                id="name"
                value={categoryForm.name}
                onChange={(e) => setCategoryForm({ ...categoryForm, name: e.target.value })}
                placeholder="Nome da categoria"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="type">Tipo *</Label>
              <Select
                value={categoryForm.type}
                onValueChange={(value) => setCategoryForm({ ...categoryForm, type: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="age_group">Faixa Etária</SelectItem>
                  <SelectItem value="championship">Campeonato</SelectItem>
                  <SelectItem value="custom">Personalizada</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Descrição</Label>
              <Input
                id="description"
                value={categoryForm.description}
                onChange={(e) => setCategoryForm({ ...categoryForm, description: e.target.value })}
                placeholder="Descrição da categoria"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddCategoryDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleSaveCategory}>
              Salvar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para editar categoria */}
      <Dialog open={isEditCategoryDialogOpen} onOpenChange={setIsEditCategoryDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar Categoria</DialogTitle>
          </DialogHeader>

          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="edit-name">Nome *</Label>
              <Input
                id="edit-name"
                value={categoryForm.name}
                onChange={(e) => setCategoryForm({ ...categoryForm, name: e.target.value })}
                placeholder="Nome da categoria"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-type">Tipo *</Label>
              <Select
                value={categoryForm.type}
                onValueChange={(value) => setCategoryForm({ ...categoryForm, type: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="age_group">Faixa Etária</SelectItem>
                  <SelectItem value="championship">Campeonato</SelectItem>
                  <SelectItem value="custom">Personalizada</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-description">Descrição</Label>
              <Input
                id="edit-description"
                value={categoryForm.description}
                onChange={(e) => setCategoryForm({ ...categoryForm, description: e.target.value })}
                placeholder="Descrição da categoria"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditCategoryDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleUpdateCategory}>
              Salvar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para adicionar jogador à categoria */}
      <Dialog open={isAssignPlayerDialogOpen} onOpenChange={setIsAssignPlayerDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Adicionar Jogador à Categoria</DialogTitle>
          </DialogHeader>

          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="player">Jogador *</Label>
              <Select
                value={selectedPlayerId}
                onValueChange={setSelectedPlayerId}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um jogador" />
                </SelectTrigger>
                <SelectContent>
                  {availablePlayers.map((player) => (
                    <SelectItem key={player.id} value={player.id}>
                      {player.name} ({positionMap[player.position] || player.position})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAssignPlayerDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleAssignPlayer}>
              Adicionar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
