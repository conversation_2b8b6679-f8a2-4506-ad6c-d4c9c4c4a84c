import React from "react";

interface ColorPickerProps {
  label: string;
  color: string;
  onChange: (color: string) => void;
}

export default function ColorPicker({ label, color, onChange }: ColorPickerProps) {
  return (
    <div className="flex flex-col gap-1">
      <label className="font-medium mb-1">{label}</label>
      <input
        type="color"
        value={color}
        onChange={e => onChange(e.target.value)}
        className="w-10 h-10 p-0 border-none bg-transparent cursor-pointer rounded shadow"
        style={{ background: color }}
      />
      <span className="text-xs text-muted-foreground mt-1">{color}</span>
    </div>
  );
}
