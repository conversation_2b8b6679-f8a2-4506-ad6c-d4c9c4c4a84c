// Funções relacionadas a Staff serão migradas para cá a partir do api.ts

import { supabase } from "@/integrations/supabase/client";
// Exporta tipo Staff para re-export em api.ts
export type Staff = {
    id: string;
    club_id: number;
    name: string;
    role: string;
    age?: number;
    nationality?: string;
    experience?: string;
  };
import { v4 as uuidv4 } from "uuid";

// Funções para Staff
export async function getStaff(clubId: number): Promise<Staff[]> {
    const { data, error } = await supabase
      .from("coaches")
      .select("*")
      .eq("club_id", clubId);
  
    if (error) {
      console.error("Erro ao buscar staff:", error);
      throw new Error(`Erro ao buscar staff: ${error.message}`);
    }
  
    return data as Staff[];
  }
  
  export async function createStaff(clubId: number, staff: Omit<Staff, "id">): Promise<Staff> {
    const newStaff = {
      ...staff,
      id: uuidv4(),
      club_id: clubId
    };
  
    const { data, error } = await supabase
      .from("coaches")
      .insert(newStaff)
      .select()
      .single();
  
    if (error) {
      console.error("Erro ao criar staff:", error);
      throw new Error(`Erro ao criar staff: ${error.message}`);
    }
  
    return data as Staff;
  }
  
  export async function updateStaff(clubId: number, id: string, staff: Partial<Staff>): Promise<Staff> {
    const { data, error } = await supabase
    .from("coaches")
    .update({ ...staff, club_id: clubId })
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error("Erro ao atualizar staff:", error);
    throw new Error(`Erro ao atualizar staff: ${error.message}`);
  }

  return data as Staff;
}

export async function deleteStaff(clubId: number, id: string): Promise<boolean> {
  const { error } = await supabase
    .from("coaches")
    .delete()
    .eq("club_id", clubId)
    .eq("id", id);

  if (error) {
    console.error("Erro ao deletar staff:", error);
    throw new Error(`Erro ao deletar staff: ${error.message}`);
  }

  return true;
}