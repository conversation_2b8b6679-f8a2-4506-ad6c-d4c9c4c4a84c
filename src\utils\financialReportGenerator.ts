import { jsPD<PERSON> } from "jspdf";
import autoTable from "jspdf-autotable";
import { ClubInfo } from "@/api/api";
import { getClubPrimaryColorRgb } from '@/utils/themeUtils';

// Interface for jsPDF with autoTable support
interface jsPDFWithAutoTable extends jsPDF {
  autoTable: typeof autoTable;
  lastAutoTable: {
    finalY: number;
  };
  setFont(fontName?: string, fontStyle?: string): jsPDF;
  setLineDashPattern(pattern: number[], patternOffset: number): jsPDF;
}

// Type for financial data by department
export type FinancialDataByDepartment = {
  departmentName: string;
  functions: {
    functionName: string;
    collaborators: {
      id: number;
      name: string;
      amount: number;
      details?: string;
    }[];
  }[];
};

/**
 * Generate a financial report PDF
 * @param financialData Financial data organized by department and function
 * @param clubInfo Club information
 * @param month Month number (0-11)
 * @param year Year
 * @param filename Optional filename for the PDF
 * @returns Promise resolving to void
 */
export async function generateFinancialReport(
  financialData: FinancialDataByDepartment[],
  clubInfo: ClubInfo,
  month: number,
  year: number,
  filename: string = 'relatorio-financeiro.pdf'
): Promise<void> {
  // Create a new PDF document
  const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

  // Page configuration
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const margin = 10;

  // Add title
  const months = [
    'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
    'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
  ];
  const title = `RELATÓRIO FINANCEIRO`;
  const subtitle = `${months[month]} ${year}`;

  doc.setFontSize(18);
  doc.text(title, pageWidth / 2, 20, { align: 'center' });

  doc.setFontSize(14);
  doc.text(subtitle, pageWidth / 2, 28, { align: 'center' });

  // Add club information
  doc.setFontSize(12);
  doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, margin, 38);

  // Try to add the club logo if available
  if (clubInfo.logo_url) {
    try {
      const img = new Image();

      // Create a promise to handle image loading
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            // Calculate ratio to maintain proportions
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;

            // Add the image to the PDF (right-aligned)
            doc.addImage(img, 'PNG', pageWidth - margin - imgWidth, 10, imgWidth, imgHeight);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });

      // Start loading the image
      img.src = clubInfo.logo_url;

      // Wait for the image to load and process
      await loadImage;
    } catch (logoError) {
      console.error("Error adding logo to PDF:", logoError);
    }
  }

  // Add report date
  const currentDate = new Date().toLocaleDateString('pt-BR');
  doc.setFontSize(10);
  doc.text(`Data: ${currentDate}`, pageWidth - margin, 38, { align: 'right' });

  // Initial Y position for content
  let yPosition = 45;

  // Calculate totals
  let grandTotal = 0;
  financialData.forEach(department => {
    department.functions.forEach(func => {
      func.collaborators.forEach(collaborator => {
        grandTotal += collaborator.amount;
      });
    });
  });

  // Add summary
  doc.setFontSize(14);
  doc.text("Resumo Financeiro", margin, yPosition);
  yPosition += 8;

  doc.setFontSize(10);
  doc.text(`Total da Folha de Pagamento: R$ ${grandTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, margin, yPosition);
  yPosition += 15;

  // For each department, show functions and collaborators
  financialData.forEach((department, index) => {
    // Department title
    doc.setFontSize(14);
    doc.setTextColor(0, 0, 0);
    doc.setFillColor(242, 242, 242);
    doc.rect(margin, yPosition, pageWidth - 2 * margin, 8, 'F');
    doc.text(`Departamento: ${department.departmentName}`, margin + 2, yPosition + 5);
    yPosition += 12;

    // Calculate department total
    let departmentTotal = 0;
    department.functions.forEach(func => {
      func.collaborators.forEach(collaborator => {
        departmentTotal += collaborator.amount;
      });
    });

    // Add department total
    doc.setFontSize(10);
    doc.text(`Total do Departamento: R$ ${departmentTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, margin, yPosition);
    yPosition += 8;

    // For each function in the department
    department.functions.forEach(func => {
      // Function title
      doc.setFontSize(12);
      doc.text(`Função: ${func.functionName}`, margin, yPosition);
      yPosition += 6;

      // Calculate function total
      const functionTotal = func.collaborators.reduce((sum, collaborator) => sum + collaborator.amount, 0);

      // Prepare data for the table
      const tableData = func.collaborators.map(collaborator => [
        collaborator.name,
        `R$ ${collaborator.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`,
        collaborator.details || '-'
      ]);

      // Add the table to the PDF
      autoTable(doc, {
        startY: yPosition,
        head: [['Nome', 'Valor', 'Detalhes']],
        body: tableData,
        theme: 'striped',
        headStyles: { fillColor: getClubPrimaryColorRgb(), textColor: [255, 255, 255] },
        margin: { left: margin, right: margin },
        columnStyles: {
          0: { cellWidth: 'auto' }, // Name
          1: { cellWidth: 40, halign: 'right' }, // Amount
          2: { cellWidth: 'auto' } // Details
        },
        foot: [['Total', `R$ ${functionTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, '']],
        footStyles: { fillColor: [240, 240, 240], fontStyle: 'bold' },
        didDrawPage: (data) => {
          // Add header on each page
          if (data.pageNumber > 1) {
            doc.setFontSize(10);
            doc.text(title, pageWidth / 2, 10, { align: 'center' });
            doc.setFontSize(8);
            doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, margin, 15);
            doc.text(`Data: ${currentDate}`, pageWidth - margin, 15, { align: 'right' });
          }
        }
      });

      // Update Y position for next content
      const docWithTable = doc as jsPDFWithAutoTable;
      yPosition = docWithTable.lastAutoTable.finalY + 10;
    });

    // Add dividing line between departments (except the last one)
    if (index < financialData.length - 1) {
      doc.setDrawColor(200, 200, 200);
      doc.setLineDashPattern([3, 3], 0);
      doc.line(margin, yPosition, pageWidth - margin, yPosition);
      doc.setLineDashPattern([], 0); // Reset to solid line
      yPosition += 10;
    }
  });

  // Add footer
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.text(
      `© ${new Date().getFullYear()} ${clubInfo.name} - Todos os direitos reservados`,
      pageWidth / 2,
      pageHeight - 10,
      { align: 'center' }
    );
    doc.text(
      `Página ${i} de ${pageCount}`,
      pageWidth - margin,
      pageHeight - 10,
      { align: 'right' }
    );
  }

  // Save the PDF
  doc.save(filename);
}
