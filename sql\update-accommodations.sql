-- Add rooms_count field to accommodations table
ALTER TABLE accommodations
ADD COLUMN rooms_count INTEGER;

-- Create hotel_rooms table
CREATE TABLE hotel_rooms (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id),
  accommodation_id INTEGER REFERENCES accommodations(id),
  room_number TEXT NOT NULL,
  capacity INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX idx_hotel_rooms_accommodation_id ON hotel_rooms(accommodation_id);

-- Add constraint to ensure room_number is unique per accommodation
ALTER TABLE hotel_rooms
ADD CONSTRAINT unique_room_per_accommodation UNIQUE (accommodation_id, room_number);

-- Add hotel_room_id to player_accommodations table
ALTER TABLE player_accommodations
ADD COLUMN hotel_room_id INTEGER REFERENCES hotel_rooms(id);
