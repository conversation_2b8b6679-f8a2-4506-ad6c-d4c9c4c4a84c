import { create } from "zustand";
import { createSession, Session as ApiSession } from "../api/api";

export type Session = {
  id: number;
  club_id: number;
  player: string;
  type: string;
  date: string;
  professional: string;
  notes?: string;
};

interface SessionsState {
  sessions: Session[];
  loading: boolean;
  error: string | null;
  addSession: (clubId: number, session: Omit<Session, "id">) => Promise<void>;
}

export const useSessionsStore = create<SessionsState>((set) => ({
  sessions: [],
  loading: false,
  error: null,

  addSession: async (clubId: number, session: Omit<Session, "id">): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const newSession = await createSession(clubId, session);
      set((state) => ({ sessions: [...state.sessions, newSession], loading: false }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao adicionar sessão", loading: false });
    }
  },
}));
