import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "@/components/ui/use-toast";
import { createUserInvitation } from "@/api/api";
import { DepartmentSelector } from "@/components/DepartmentSelector";
import { CustomPermissionsSelector } from "@/components/users/CustomPermissionsSelector";
import { ROLES, ROLE_PERMISSIONS } from "@/constants/permissions";
import { Collaborator } from "@/api/api";
import { Mail, Settings, Lock } from "lucide-react";

interface ColaboradorInviteDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  collaborator: Collaborator;
  onSuccess?: () => void;
}

export function ColaboradorInviteDialog({
  open,
  onOpenChange,
  clubId,
  collaborator,
  onSuccess
}: ColaboradorInviteDialogProps) {
  // Estados para o formulário de convite
  const [email, setEmail] = useState("");
  const [role, setRole] = useState("staff");
  const [departmentId, setDepartmentId] = useState("");
  const [formError, setFormError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState("basic");
  const [customPermissions, setCustomPermissions] = useState<Record<string, boolean>>({});
  const [useCustomPermissions, setUseCustomPermissions] = useState(false);
  const [generatePassword, setGeneratePassword] = useState(true);
  const [password, setPassword] = useState("");

  // Mapear a função do colaborador para um papel de usuário
  const mapCollaboratorRoleToUserRole = (collaboratorRole: string): string => {
    // Mapeamento simples de funções comuns
    const roleMap: Record<string, string> = {
      "Técnico": "coach",
      "Auxiliar Técnico": "coach",
      "Preparador Físico": "coach",
      "Fisioterapeuta": "medical",
      "Médico": "medical",
      "Nutricionista": "medical",
      "Psicólogo": "medical",
      "Diretor": "admin",
      "Presidente": "admin",
      "Gerente": "admin",
      "Coordenador": "admin",
      "Secretário": "staff",
      "Assistente": "staff",
      "Roupeiro": "staff",
      "Massagista": "staff",
      "Administrativo": "staff"
    };

    return roleMap[collaboratorRole] || "staff";
  };

  // Preencher o email do colaborador quando o diálogo é aberto
  useEffect(() => {
    if (open && collaborator) {
      setEmail(collaborator.email || "");
      // Usar a função do colaborador como papel padrão
      if (collaborator.role) {
        setRole(mapCollaboratorRoleToUserRole(collaborator.role));
      }
    }
  }, [open, collaborator]);

  // Função para criar um convite
  const handleCreateInvitation = async () => {
    // Validar campos
    if (!email.trim()) {
      setFormError("Email é obrigatório");
      return;
    }

    if (!generatePassword && !password.trim()) {
      setFormError("Senha é obrigatória quando não for gerada automaticamente");
      return;
    }

    try {
      setSubmitting(true);
      setFormError(null);

      // Determinar quais permissões usar
      const permissionsToUse = useCustomPermissions
        ? customPermissions
        : ROLE_PERMISSIONS[role as keyof typeof ROLE_PERMISSIONS] || {};

      // Criar o convite vinculado ao colaborador
      await createUserInvitation(
        clubId,
        email,
        role,
        departmentId ? parseInt(departmentId) : undefined,
        permissionsToUse,
        generatePassword ? undefined : password,
        collaborator.id
      );

      toast({
        title: "Sucesso",
        description: "Convite enviado com sucesso",
      });

      // Limpar formulário e fechar modal
      resetForm();
      onOpenChange(false);

      // Chamar callback de sucesso se fornecido
      if (onSuccess) {
        onSuccess();
      }
    } catch (err: any) {
      console.error("Erro ao criar convite:", err);
      setFormError(err.message || "Erro ao criar convite");
      toast({
        title: "Erro",
        description: err.message || "Erro ao criar convite",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Resetar o formulário
  const resetForm = () => {
    setEmail(collaborator?.email || "");
    setRole(collaborator?.role ? mapCollaboratorRoleToUserRole(collaborator.role) : "staff");
    setDepartmentId("");
    setCustomPermissions({});
    setUseCustomPermissions(false);
    setGeneratePassword(true);
    setPassword("");
    setActiveTab("basic");
    setFormError(null);
  };

  return (
    <Dialog open={open} onOpenChange={(open) => {
      onOpenChange(open);
      if (!open) {
        resetForm();
      } else if (collaborator) {
        setEmail(collaborator.email || "");
        if (collaborator.role) {
          setRole(mapCollaboratorRoleToUserRole(collaborator.role));
        }
      }
    }}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>Criar Conta para {collaborator?.full_name}</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="basic" className="flex items-center gap-1">
              <Mail className="h-4 w-4" />
              <span>Informações Básicas</span>
            </TabsTrigger>
            <TabsTrigger value="permissions" className="flex items-center gap-1">
              <Settings className="h-4 w-4" />
              <span>Permissões</span>
            </TabsTrigger>
            <TabsTrigger value="password" className="flex items-center gap-1">
              <Lock className="h-4 w-4" />
              <span>Senha</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email*</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Email do usuário"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="role">Função*</Label>
              <Select
                value={role}
                onValueChange={(value) => {
                  setRole(value);
                  // Se não estiver usando permissões customizadas, atualizar as permissões
                  if (!useCustomPermissions) {
                    setCustomPermissions(ROLE_PERMISSIONS[value as keyof typeof ROLE_PERMISSIONS] || {});
                  }
                }}
              >
                <SelectTrigger id="role">
                  <SelectValue placeholder="Selecione a função" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(ROLES).map(([key, value]) => (
                    <SelectItem key={key} value={key}>
                      {value.label} - {value.description}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <DepartmentSelector
                value={departmentId}
                onChange={setDepartmentId}
                label="Departamento (opcional)"
                placeholder="Selecione um departamento"
              />
            </div>
          </TabsContent>

          <TabsContent value="permissions" className="space-y-4">
            <div className="flex items-center space-x-2 mb-4">
              <Checkbox
                id="useCustomPermissions"
                checked={useCustomPermissions}
                onCheckedChange={(checked) => {
                  setUseCustomPermissions(checked as boolean);
                  if (!checked) {
                    // Se desmarcar, voltar para as permissões padrão do papel
                    setCustomPermissions(ROLE_PERMISSIONS[role as keyof typeof ROLE_PERMISSIONS] || {});
                  }
                }}
              />
              <Label htmlFor="useCustomPermissions">
                Personalizar permissões (sobrescreve as permissões padrão da função)
              </Label>
            </div>

            {useCustomPermissions ? (
              <CustomPermissionsSelector
                value={customPermissions}
                onChange={setCustomPermissions}
                showRoleSelector={true}
              />
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                Ative a opção acima para personalizar as permissões.
                <br />
                Atualmente usando as permissões padrão da função {ROLES[role as keyof typeof ROLES]?.label || role}.
              </div>
            )}
          </TabsContent>

          <TabsContent value="password" className="space-y-4">
            <div className="flex items-center space-x-2 mb-4">
              <Checkbox
                id="generatePassword"
                checked={generatePassword}
                onCheckedChange={(checked) => {
                  setGeneratePassword(checked as boolean);
                  if (checked) {
                    setPassword("");
                  }
                }}
              />
              <Label htmlFor="generatePassword">
                Gerar senha aleatória automaticamente
              </Label>
            </div>

            {!generatePassword && (
              <div className="space-y-2">
                <Label htmlFor="password">Senha*</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Digite a senha para o usuário"
                  required
                />
                <p className="text-xs text-muted-foreground">
                  A senha deve ter pelo menos 6 caracteres.
                </p>
              </div>
            )}

            {generatePassword && (
              <div className="text-center py-8 text-muted-foreground">
                Uma senha aleatória será gerada e enviada por email para o usuário.
              </div>
            )}
          </TabsContent>
        </Tabs>

        {formError && <p className="text-red-500 text-sm mt-4">{formError}</p>}

        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleCreateInvitation} disabled={submitting}>
            {submitting ? "Enviando..." : "Enviar Convite"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
