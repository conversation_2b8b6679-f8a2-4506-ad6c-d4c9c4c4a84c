import { supabase } from "@/integrations/supabase/client";
import { withPermission, withAuditLog } from "./middleware";
import { v4 as uuidv4 } from "uuid";

// Permissions
export const COLLABORATOR_PERMISSIONS = {
  VIEW: "collaborators.view",
  CREATE: "collaborators.create",
  EDIT: "collaborators.edit",
  DELETE: "collaborators.delete",
};

// Types
export type Collaborator = {
  id: number;
  club_id: number;
  user_id?: string;
  registration_number: string;
  full_name: string;
  birth_date?: string;
  phone?: string;
  role: string;
  role_type: 'technical' | 'assistant_technical';
  cpf?: string;
  zip_code?: string;
  state?: string;
  city?: string;
  address?: string;
  address_number?: string;
  credential_number?: string;
  email?: string;
  created_at: string;
  updated_at: string;
  // New fields
  image?: string;
  salary?: number;
  bonus?: number;
  bank_info?: {
    bank_name?: string;
    account_number?: string;
    agency?: string;
    pix?: string;
  };
  entry_date?: string;
  status?: string;
  document_id?: string;
  document_id_url?: string;
  certificate_url?: string;
  medical_certificate_url?: string;
  resume_url?: string;
  criminal_record_url?: string;
  // Department and function fields
  department_type_id?: number;
  job_function_id?: number;
  department_name?: string; // For joins
  job_function_name?: string; // For joins
};

export type CollaboratorDocument = {
  id: number;
  club_id: number;
  collaborator_id: number;
  document_type: string;
  file_url: string;
  status: "pending" | "verified" | "rejected";
  uploaded_at: string;
  verified_at?: string;
  verified_by?: string;
  rejection_reason?: string;
  verifier_name?: string; // For joins
};

// Document types
export const COLLABORATOR_DOCUMENT_TYPES = [
  "identity",
  "cpf",
  "credential",
  "certificate",
  "other"
];

export const COLLABORATOR_DOCUMENT_LABELS: Record<string, string> = {
  "identity": "Documento de Identidade",
  "cpf": "CPF",
  "credential": "Credencial Profissional",
  "certificate": "Certificado",
  "other": "Outro Documento"
};

/**
 * Get all collaborators for a club
 * @param clubId Club ID
 * @returns List of collaborators
 */
export async function getCollaborators(clubId: number): Promise<Collaborator[]> {
  try {
    // First try with the full join
    const { data, error } = await supabase
      .from("collaborators_view")
      .select(`
        *,
        department_types:department_type_id (
          id,
          name
        ),
        job_functions:job_function_id (
          id,
          name
        )
      `)
      .eq("club_id", clubId)
      .order("full_name");

    if (!error) {
      // If successful, format the data to include department and function names
      return (data || []).map(item => ({
        ...item,
        department_name: item.department_types?.name || null,
        job_function_name: item.job_functions?.name || null,
        // Remove nested objects to avoid confusion
        department_types: undefined,
        job_functions: undefined
      }));
    }

    // If there's an error with the join, try a simpler query without the joins
    console.warn("Error with joined query, falling back to simple query:", error);
    const { data: simpleData, error: simpleError } = await supabase
      .from("collaborators_view")
      .select("*")
      .eq("club_id", clubId)
      .order("full_name");

    if (simpleError) {
      console.error("Error fetching collaborators:", simpleError);
      throw new Error(`Error fetching collaborators: ${simpleError.message}`);
    }

    return simpleData || [];
  } catch (error: any) {
    console.error("Error fetching collaborators:", error);
    throw new Error(`Error fetching collaborators: ${error.message}`);
  }
}

/**
 * Get a collaborator by ID
 * @param clubId Club ID
 * @param id Collaborator ID
 * @returns Collaborator
 */
export async function getCollaboratorById(clubId: number, id: number): Promise<Collaborator> {
  try {
    // First try with the full join
    const { data, error } = await supabase
      .from("collaborators_view")
      .select(`
        *,
        department_types:department_type_id (
          id,
          name
        ),
        job_functions:job_function_id (
          id,
          name
        )
      `)
      .eq("club_id", clubId)
      .eq("id", id)
      .single();

    if (!error) {
      // If successful, format the data to include department and function names
      return {
        ...data,
        department_name: data.department_types?.name || null,
        job_function_name: data.job_functions?.name || null,
        // Remove nested objects to avoid confusion
        department_types: undefined,
        job_functions: undefined
      };
    }

    // If there's an error with the join, try a simpler query without the joins
    console.warn("Error with joined query in getCollaboratorById, falling back to simple query:", error);
    const { data: simpleData, error: simpleError } = await supabase
      .from("collaborators_view")
      .select("*")
      .eq("club_id", clubId)
      .eq("id", id)
      .single();

    if (simpleError) {
      console.error("Error fetching collaborator:", simpleError);
      throw new Error(`Error fetching collaborator: ${simpleError.message}`);
    }

    return simpleData;
  } catch (error: any) {
    console.error("Error fetching collaborator:", error);
    throw new Error(`Error fetching collaborator: ${error.message}`);
  }
}

/**
 * Create a new collaborator
 * @param clubId Club ID
 * @param userId User ID
 * @param collaborator Collaborator data
 * @returns Created collaborator
 */
export async function createCollaborator(
  clubId: number,
  userId: string,
  collaborator: Omit<Collaborator, "id" | "club_id" | "created_at" | "updated_at" | "registration_number">
): Promise<Collaborator> {
  return withPermission(
    clubId,
    userId,
    COLLABORATOR_PERMISSIONS.CREATE,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "collaborator.create",
        { name: collaborator.full_name, role: collaborator.role },
        async () => {
          try {
            const { data, error } = await supabase
              .from("collaborators")
              .insert({
                club_id: clubId,
                ...collaborator,
              })
              .select()
              .single();

            if (error) {
              console.error("Error creating collaborator:", error);
              throw new Error(`Error creating collaborator: ${error.message}`);
            }

            return data;
          } catch (error: any) {
            console.error("Error creating collaborator:", error);
            throw new Error(error.message || "Error creating collaborator");
          }
        }
      );
    }
  );
}

/**
 * Update a collaborator
 * @param clubId Club ID
 * @param userId User ID
 * @param id Collaborator ID
 * @param collaborator Collaborator data
 * @returns Updated collaborator
 */
export async function updateCollaborator(
  clubId: number,
  userId: string,
  id: number,
  collaborator: Partial<Omit<Collaborator, "id" | "club_id" | "created_at" | "updated_at" | "registration_number">>
): Promise<Collaborator> {
  return withPermission(
    clubId,
    userId,
    COLLABORATOR_PERMISSIONS.EDIT,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "collaborator.update",
        { id, ...collaborator },
        async () => {
          try {
            // Verificar se o status está sendo alterado para 'inactive'
            const statusChangedToInactive = collaborator.status === 'inactive';

            // Buscar o colaborador atual para comparar o status
            let oldStatus = null;
            if (statusChangedToInactive) {
              const { data: currentCollaborator } = await supabase
                .from("collaborators")
                .select("status")
                .eq("club_id", clubId)
                .eq("id", id)
                .single();

              oldStatus = currentCollaborator?.status;
            }

            const { data, error } = await supabase
              .from("collaborators")
              .update({
                ...collaborator,
                updated_at: new Date().toISOString(),
              })
              .eq("club_id", clubId)
              .eq("id", id)
              .select()
              .single();

            if (error) {
              console.error("Error updating collaborator:", error);
              throw new Error(`Error updating collaborator: ${error.message}`);
            }

            // Se o status foi alterado para 'inactive', executar limpeza de vinculações
            if (statusChangedToInactive && oldStatus !== 'inactive') {
              try {
                console.log(`Colaborador ${id} alterado para status 'inactive'. Executando limpeza de vinculações...`);

                // Chamar a função SQL para remover vinculações
                const { error: cleanupError } = await supabase
                  .rpc('remove_collaborator_associations', {
                    p_club_id: clubId,
                    p_collaborator_id: id
                  });

                if (cleanupError) {
                  console.error("Erro ao executar limpeza de vinculações do colaborador:", cleanupError);
                  // Não lançamos erro aqui para não interromper o fluxo principal
                } else {
                  console.log(`Vinculações removidas com sucesso para o colaborador ${id}`);
                }
              } catch (cleanupError) {
                console.error("Erro ao executar limpeza de vinculações do colaborador:", cleanupError);
              }
            }

            return data;
          } catch (error: any) {
            console.error("Error updating collaborator:", error);
            throw new Error(error.message || "Error updating collaborator");
          }
        }
      );
    }
  );
}

/**
 * Delete a collaborator
 * @param clubId Club ID
 * @param userId User ID
 * @param id Collaborator ID
 * @returns Success status
 */
export async function deleteCollaborator(
  clubId: number,
  userId: string,
  id: number
): Promise<boolean> {
  return withPermission(
    clubId,
    userId,
    COLLABORATOR_PERMISSIONS.DELETE,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "collaborator.delete",
        { id },
        async () => {
          try {
            // First, delete all documents associated with this collaborator
            await supabase
              .from("collaborator_documents")
              .delete()
              .eq("club_id", clubId)
              .eq("collaborator_id", id);

            // Then delete the collaborator
            const { error } = await supabase
              .from("collaborators")
              .delete()
              .eq("club_id", clubId)
              .eq("id", id);

            if (error) {
              console.error("Error deleting collaborator:", error);
              throw new Error(`Error deleting collaborator: ${error.message}`);
            }

            return true;
          } catch (error: any) {
            console.error("Error deleting collaborator:", error);
            throw new Error(error.message || "Error deleting collaborator");
          }
        }
      );
    }
  );
}

/**
 * Upload a document for a collaborator
 * @param clubId Club ID
 * @param userId User ID
 * @param collaboratorId Collaborator ID
 * @param documentType Document type
 * @param file Document file
 * @returns Uploaded document
 */
export async function uploadCollaboratorDocument(
  clubId: number,
  userId: string,
  collaboratorId: number,
  documentType: string,
  file: File
): Promise<CollaboratorDocument> {
  return withPermission(
    clubId,
    userId,
    COLLABORATOR_PERMISSIONS.EDIT,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "collaborator.upload_document",
        { collaboratorId, documentType },
        async () => {
          try {
            // Upload the file to storage
            const fileUrl = await uploadCollaboratorDocumentToStorage(clubId, collaboratorId, documentType, file);

            // Register the document in the database
            const { data, error } = await supabase
              .from("collaborator_documents")
              .insert({
                club_id: clubId,
                collaborator_id: collaboratorId,
                document_type: documentType,
                file_url: fileUrl,
                status: "pending",
              })
              .select()
              .single();

            if (error) {
              console.error("Error registering collaborator document:", error);
              throw new Error(`Error registering collaborator document: ${error.message}`);
            }

            return data;
          } catch (error: any) {
            console.error("Error uploading collaborator document:", error);
            throw new Error(error.message || "Error uploading collaborator document");
          }
        }
      );
    }
  );
}

/**
 * Upload a document file to storage
 * @param clubId Club ID
 * @param collaboratorId Collaborator ID
 * @param documentType Document type
 * @param file Document file
 * @returns File URL
 */
async function uploadCollaboratorDocumentToStorage(
  clubId: number,
  collaboratorId: number,
  documentType: string,
  file: File
): Promise<string> {
  try {
    // Limit file size (5MB)
    const MAX_SIZE = 5 * 1024 * 1024; // 5MB
    if (file.size > MAX_SIZE) {
      throw new Error("Document must be at most 5MB");
    }

    // Generate unique file name
    const fileExt = file.name.split(".").pop();
    const fileName = `${documentType}-${uuidv4()}.${fileExt}`;
    const filePath = `${clubId}/collaborators/${collaboratorId}/${fileName}`;

    // Upload file with metadata
    const { error } = await supabase.storage
      .from("playerdocuments") // Using the same bucket as player documents
      .upload(filePath, file, {
        cacheControl: "3600",
        upsert: true,
        contentType: file.type,
        metadata: {
          club_id: clubId.toString(),
          collaborator_id: collaboratorId.toString(),
          document_type: documentType,
        },
      });

    if (error) {
      throw new Error(`Error uploading document: ${error.message}`);
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from("playerdocuments")
      .getPublicUrl(filePath);

    return urlData.publicUrl;
  } catch (error: any) {
    console.error("Error uploading document to storage:", error);
    throw new Error(error.message || "Error uploading document to storage");
  }
}

/**
 * Get collaborators by department type
 * @param clubId Club ID
 * @param departmentTypeId Department type ID
 * @returns List of collaborators in the department
 */
export async function getCollaboratorsByDepartment(
  clubId: number,
  departmentTypeId: number
): Promise<Collaborator[]> {
  try {
    // First try with the full join
    const { data, error } = await supabase
      .from("collaborators_view")
      .select(`
        *,
        department_types:department_type_id (
          id,
          name
        ),
        job_functions:job_function_id (
          id,
          name
        )
      `)
      .eq("club_id", clubId)
      .eq("department_type_id", departmentTypeId)
      .order("full_name");

    if (!error) {
      // If successful, format the data to include department and function names
      return (data || []).map(item => ({
        ...item,
        department_name: item.department_types?.name || null,
        job_function_name: item.job_functions?.name || null,
        // Remove nested objects to avoid confusion
        department_types: undefined,
        job_functions: undefined
      }));
    }

    // If there's an error with the join, try a simpler query without the joins
    console.warn("Error with joined query in getCollaboratorsByDepartment, falling back to simple query:", error);
    const { data: simpleData, error: simpleError } = await supabase
      .from("collaborators_view")
      .select("*")
      .eq("club_id", clubId)
      .eq("department_type_id", departmentTypeId)
      .order("full_name");

    if (simpleError) {
      console.error("Error fetching collaborators by department:", simpleError);
      throw new Error(`Error fetching collaborators by department: ${simpleError.message}`);
    }

    return simpleData || [];
  } catch (error: any) {
    console.error("Error fetching collaborators by department:", error);
    throw new Error(`Error fetching collaborators by department: ${error.message}`);
  }
}

/**
 * Get documents for a collaborator
 * @param clubId Club ID
 * @param collaboratorId Collaborator ID
 * @returns List of documents
 */
export async function getCollaboratorDocuments(
  clubId: number,
  collaboratorId: number
): Promise<CollaboratorDocument[]> {
  try {
    // Primeiro, buscar os documentos sem o join
    const { data, error } = await supabase
      .from("collaborator_documents")
      .select("*")
      .eq("club_id", clubId)
      .eq("collaborator_id", collaboratorId)
      .order("document_type");

    if (error) {
      throw new Error(`Error fetching documents: ${error.message}`);
    }

    // Se não houver documentos, retornar array vazio
    if (!data || data.length === 0) {
      return [];
    }

    // Para cada documento que tem um verificador, buscar o nome do verificador
    const documentsWithVerifierInfo = await Promise.all(
      data.map(async (doc) => {
        let verifierName = null;

        // Se o documento tiver sido verificado, buscar o nome do verificador
        if (doc.verified_by) {
          try {
            const { data: userData, error: userError } = await supabase
              .from("users")
              .select("name")
              .eq("id", doc.verified_by)
              .single();

            if (!userError && userData) {
              verifierName = userData.name;
            }
          } catch (err) {
            console.error("Error fetching verifier name:", err);
          }
        }

        return {
          ...doc,
          verifier_name: verifierName
        };
      })
    );

    return documentsWithVerifierInfo;
  } catch (error: any) {
    console.error("Error fetching collaborator documents:", error);
    throw new Error(error.message || "Error fetching collaborator documents");
  }
}
