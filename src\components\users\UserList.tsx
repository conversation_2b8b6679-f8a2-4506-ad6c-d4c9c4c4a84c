import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { getClubUsers } from "@/api/api";
import { removeUserFromClub } from "@/api/users";
import { deleteUser } from "@/api/userManagement";
import { UserAvatar } from "@/components/users/UserAvatar";
import { SyncRolesButton } from "@/components/admin/SyncRolesButton";
import { Search, UserCog, UserX, Trash2, RefreshCw } from "lucide-react";

interface ClubUser {
  id: string;
  name: string;
  email: string;
  role: string;
  profile_image?: string;
  created_at: string;
}

interface UserListProps {
  onManagePermissions: (user: ClubUser) => void;
}

export function UserList({ onManagePermissions }: UserListProps) {
  const clubId = useCurrentClubId();
  const [users, setUsers] = useState<ClubUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");

  // Carregar usuários
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        // Excluir jogadores e médicos da lista
        const data = await getClubUsers(clubId, ["player", "medical"]);
        setUsers(data);
      } catch (err: any) {
        console.error("Erro ao carregar usuários:", err);
        toast({
          title: "Erro",
          description: "Não foi possível carregar os usuários",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [clubId]);

  // Filtrar usuários com base na pesquisa
  const filteredUsers = users.filter((user) =>
    user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.role.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Função para remover um usuário do clube
  const handleRemoveUser = async (user: ClubUser) => {
    if (!confirm(`Tem certeza que deseja remover ${user.name} do clube?`)) {
      return;
    }

    try {
      await removeUserFromClub(clubId, user.id);

      // Atualizar lista de usuários
      setUsers(users.filter((u) => u.id !== user.id));

      toast({
        title: "Sucesso",
        description: "Usuário removido do clube com sucesso",
      });
    } catch (err: any) {
      console.error("Erro ao remover usuário do clube:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao remover usuário do clube",
        variant: "destructive",
      });
    }
  };

  // Função para excluir um usuário completamente
  const handleDeleteUser = async (user: ClubUser) => {
    if (!confirm(`ATENÇÃO: Tem certeza que deseja EXCLUIR PERMANENTEMENTE o usuário ${user.name}? Esta ação não pode ser desfeita.`)) {
      return;
    }

    try {
      const result = await deleteUser(user.id);

      if (result.success) {
        // Atualizar lista de usuários
        setUsers(users.filter((u) => u.id !== user.id));

        toast({
          title: "Sucesso",
          description: "Usuário excluído com sucesso",
        });
      } else {
        throw new Error(result.message);
      }
    } catch (err: any) {
      console.error("Erro ao excluir usuário:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao excluir usuário",
        variant: "destructive",
      });
    }
  };

  // Função para obter o rótulo do papel
  const getRoleLabel = (role: string) => {
    switch (role) {
      case "admin":
        return (
          <Badge variant="outline" className="bg-purple-100 text-purple-800 border-purple-200">
            Administrador
          </Badge>
        );
      case "manager":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">
            Gerente
          </Badge>
        );
      case "coach":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
            Treinador
          </Badge>
        );
      case "staff":
        return (
          <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-200">
            Funcionário
          </Badge>
        );
      case "player":
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200">
            Jogador
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200">
            {role}
          </Badge>
        );
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Usuários</CardTitle>
        <div className="flex items-center gap-2">
          <SyncRolesButton
            size="sm"
            onSuccess={(result) => {
              toast({
                title: "Sincronização concluída",
                description: `${result.checked} usuários verificados, ${result.fixed} corrigidos, ${result.errors} erros`,
              });
              // Recarregar a lista de usuários após a sincronização
              if (result.fixed > 0) {
                setLoading(true);
                getClubUsers(clubId, ["player", "medical"]).then(data => {
                  setUsers(data);
                  setLoading(false);
                });
              }
            }}
          />
        </div>
      </CardHeader>
      <CardContent>
        <div className="mb-4 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Buscar usuário..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        ) : filteredUsers.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            {searchQuery
              ? "Nenhum usuário encontrado para a pesquisa"
              : "Nenhum usuário cadastrado"}
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Usuário</TableHead>
                <TableHead className="hidden md:table-cell">Email</TableHead>
                <TableHead>Papel</TableHead>
                <TableHead className="hidden md:table-cell">Desde</TableHead>
                <TableHead className="w-[120px] text-right">Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <UserAvatar user={user} />
                      <span className="font-medium">{user.name}</span>
                    </div>
                  </TableCell>
                  <TableCell className="hidden md:table-cell">{user.email}</TableCell>
                  <TableCell>{getRoleLabel(user.role)}</TableCell>
                  <TableCell className="hidden md:table-cell">
                    {new Date(user.created_at).toLocaleDateString()}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onManagePermissions(user)}
                        title="Gerenciar permissões"
                      >
                        <UserCog className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveUser(user)}
                        title="Remover do clube"
                        className="text-amber-500 hover:text-amber-700 hover:bg-amber-50"
                      >
                        <UserX className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteUser(user)}
                        title="Excluir usuário permanentemente"
                        className="text-red-500 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
}
