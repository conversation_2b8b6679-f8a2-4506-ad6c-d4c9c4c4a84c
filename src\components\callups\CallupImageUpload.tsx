import { useState } from "react";
import { toast } from "@/components/ui/use-toast";
import { ImageUpload } from "@/components/ui/image-upload";
import { uploadCallupImage } from "@/api/storage-simple";

interface CallupImageUploadProps {
  imageUrl: string;
  onImageChange: (url: string | null) => void;
  maxWidth?: number;
  maxHeight?: number;
  className?: string;
  clubId?: string | number;
  imageType: string;
}

export function CallupImageUpload({
  imageUrl,
  onImageChange,
  maxWidth = 200,
  maxHeight = 200,
  className = "",
  clubId = "",
  imageType = "image"
}: CallupImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);

  const handleImageChange = async (url: string | null, file?: File) => {
    try {
      // Se não houver arquivo ou URL, apenas atualiza
      if (!file || !url) {
        onImageChange(url);
        return;
      }
      
      // Verifica se o clubId está disponível
      if (!clubId) {
        console.error("Erro: clubId não está disponível");
        toast({
          title: "Erro",
          description: "Não foi possível identificar o clube. Por favor, recarregue a página.",
          variant: "destructive",
        });
        return;
      }

      // Se for uma URL de blob (prévia local), faz o upload para o Supabase
      if (url.startsWith('blob:')) {
        setIsUploading(true);
        
        try {
          // Fazer upload da imagem para o Supabase
          const publicUrl = await uploadCallupImage(clubId, file, imageType);
          
          // Atualizar com a URL pública do Supabase
          onImageChange(publicUrl);
          toast({
            title: "Imagem enviada com sucesso!",
            variant: "default",
          });
        } catch (error) {
          console.error("Erro ao fazer upload da imagem:", error);
          toast({
            title: "Erro ao enviar imagem",
            description: error instanceof Error ? error.message : "Tente novamente mais tarde.",
            variant: "destructive",
          });
          // Maném a URL antiga em caso de erro
          onImageChange(imageUrl);
        } finally {
          setIsUploading(false);
        }
      } else {
        // Se já for uma URL válida, apenas atualiza
        onImageChange(url);
      }
    } catch (error) {
      console.error("Erro ao processar imagem:", error);
      toast({
        title: "Erro ao processar imagem",
        description: "Ocorreu um erro ao processar a imagem. Tente novamente.",
        variant: "destructive",
      });
      setIsUploading(false);
    }
  };

  return (
    <div className={className}>
      <ImageUpload
        value={imageUrl}
        onChange={handleImageChange}
        disabled={isUploading}
        previewSize={Math.min(maxWidth, maxHeight)}
        maxSize={5} // 5MB
        placeholder={isUploading ? "Enviando imagem..." : undefined}
      />
      {isUploading && (
        <p className="text-xs text-muted-foreground mt-1">
          Enviando imagem, aguarde...
        </p>
      )}
    </div>
  );
}
