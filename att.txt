
Sistema de hierarquia, o ADM consegue criar uma conta para médico/treinador/jogador e decidir o que ele terá acesso dentro do sistema.

Ao criar a escalação, pegar os cartões dos jogadores (ultimas duas partidas por exemplo), para ver se está expulso, pode jogar ou não, mesma coisa para jogadores lesionados, jogadores assim ficaram indisponiveis.

Próximos Passos
Para uma implementação completa do envio de email, você precisaria:

Escolher um serviço de email (SendGrid, Mailgun, etc.)
Configurar as credenciais do serviço no seu projeto
Atualizar a função sendEmail no arquivo email.ts para usar o serviço escolhido