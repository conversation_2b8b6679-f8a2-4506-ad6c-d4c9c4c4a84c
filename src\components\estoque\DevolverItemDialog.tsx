import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { InventoryRequestItem, returnInventoryRequestItem } from "@/api/api";
import { ArrowUpCircle } from "lucide-react";

// Schema for validation
const returnSchema = z.object({
  quantity: z.coerce
    .number()
    .positive("Quantidade deve ser maior que zero")
    .refine((val) => val > 0, {
      message: "Quantidade deve ser maior que zero",
    }),
});

type ReturnFormValues = z.infer<typeof returnSchema>;

interface DevolverItemDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  item: InventoryRequestItem | null;
  onSuccess: () => void;
}

export function DevolverItemDialog({
  open,
  onOpenChange,
  item,
  onSuccess,
}: DevolverItemDialogProps) {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  // Calculate maximum returnable quantity
  const maxReturnableQuantity = item ? item.quantity - item.returned_quantity : 0;

  // Initialize form
  const form = useForm<ReturnFormValues>({
    resolver: zodResolver(returnSchema),
    defaultValues: {
      quantity: 1,
    },
  });

  // Function to return item
  const onSubmit = async (data: ReturnFormValues) => {
    if (!item) return;

    try {
      setIsLoading(true);

      // Validate return quantity
      if (data.quantity > maxReturnableQuantity) {
        toast({
          title: "Quantidade inválida",
          description: `Quantidade máxima para devolução: ${maxReturnableQuantity}`,
          variant: "destructive",
        });
        return;
      }

      // Return the item
      await returnInventoryRequestItem(
        clubId,
        item.id,
        data.quantity,
        user?.id
      );

      toast({
        title: "Item devolvido",
        description: `${data.quantity} unidades de ${item.product_name} foram devolvidas ao estoque.`,
      });

      onSuccess();
      onOpenChange(false);
    } catch (error) {
      console.error("Error returning item:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao devolver o item. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[400px]">
        <DialogHeader>
          <DialogTitle>Devolver Item ao Estoque</DialogTitle>
        </DialogHeader>

        {item && (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="space-y-2">
                <p className="text-sm font-medium">Produto: {item.product_name}</p>
                <p className="text-sm">Departamento: {item.product_department}</p>
                <p className="text-sm">Quantidade retirada: {item.quantity}</p>
                <p className="text-sm">Quantidade já devolvida: {item.returned_quantity}</p>
                <p className="text-sm">Quantidade disponível para devolução: {maxReturnableQuantity}</p>
              </div>

              <FormField
                control={form.control}
                name="quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Quantidade para Devolução</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        max={maxReturnableQuantity}
                        {...field}
                        onChange={(e) => {
                          const value = parseInt(e.target.value);
                          if (isNaN(value)) {
                            field.onChange(1);
                          } else if (value > maxReturnableQuantity) {
                            field.onChange(maxReturnableQuantity);
                          } else {
                            field.onChange(value);
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                >
                  Cancelar
                </Button>
                <Button
                  type="submit"
                  isLoading={isLoading}
                  className="gap-2"
                >
                  <ArrowUpCircle className="h-4 w-4" />
                  Devolver ao Estoque
                </Button>
              </DialogFooter>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
}
