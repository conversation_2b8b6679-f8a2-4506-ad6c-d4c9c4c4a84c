-- Create or update department_types table
CREATE TABLE IF NOT EXISTS department_types (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_department_types_club_id ON department_types(club_id);

-- Create or update job_functions table
CREATE TABLE IF NOT EXISTS job_functions (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  department_type_id INTEGER REFERENCES department_types(id) NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_job_functions_club_id ON job_functions(club_id);
CREATE INDEX IF NOT EXISTS idx_job_functions_department_type_id ON job_functions(department_type_id);

-- Add department_type_id and job_function_id to collaborators table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'collaborators' AND column_name = 'department_type_id'
    ) THEN
        ALTER TABLE collaborators ADD COLUMN department_type_id INTEGER REFERENCES department_types(id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'collaborators' AND column_name = 'job_function_id'
    ) THEN
        ALTER TABLE collaborators ADD COLUMN job_function_id INTEGER REFERENCES job_functions(id);
    END IF;
END $$;

-- Insert default department types for all clubs
DO $$
DECLARE
    club_record RECORD;
BEGIN
    FOR club_record IN SELECT id FROM club_info LOOP
        INSERT INTO department_types (club_id, name, description)
        VALUES
          (club_record.id, 'Médico', 'Departamento médico do clube'),
          (club_record.id, 'Manutenção', 'Departamento de manutenção do clube'),
          (club_record.id, 'Profissional', 'Departamento profissional do clube'),
          (club_record.id, 'Base', 'Departamento de base do clube'),
          (club_record.id, 'Serviços Gerais', 'Departamento de serviços gerais do clube'),
          (club_record.id, 'Administrativo', 'Departamento administrativo do clube')
        ON CONFLICT DO NOTHING;
    END LOOP;
END $$;

-- Insert default job functions for Medical department
INSERT INTO job_functions (club_id, department_type_id, name, description)
VALUES
  (1, (SELECT id FROM department_types WHERE name = 'Médico' AND club_id = 1), 'Médico', 'Médico do clube'),
  (1, (SELECT id FROM department_types WHERE name = 'Médico' AND club_id = 1), 'Enfermeiro(a)', 'Enfermeiro(a) do clube'),
  (1, (SELECT id FROM department_types WHERE name = 'Médico' AND club_id = 1), 'Auxiliar de Enfermagem', 'Auxiliar de enfermagem do clube'),
  (1, (SELECT id FROM department_types WHERE name = 'Médico' AND club_id = 1), 'Fisioterapeuta', 'Fisioterapeuta do clube'),
  (1, (SELECT id FROM department_types WHERE name = 'Médico' AND club_id = 1), 'Dentista', 'Dentista do clube')
ON CONFLICT DO NOTHING;

-- Insert default job functions for Maintenance department
INSERT INTO job_functions (club_id, department_type_id, name, description)
VALUES
  (1, (SELECT id FROM department_types WHERE name = 'Manutenção' AND club_id = 1), 'Eletricista', 'Eletricista do clube'),
  (1, (SELECT id FROM department_types WHERE name = 'Manutenção' AND club_id = 1), 'Jardineiro', 'Jardineiro do clube'),
  (1, (SELECT id FROM department_types WHERE name = 'Manutenção' AND club_id = 1), 'Zelador', 'Zelador do clube')
ON CONFLICT DO NOTHING;

-- Create RLS policies
ALTER TABLE department_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_functions ENABLE ROW LEVEL SECURITY;

-- Policy for department_types: users can only see department types from their club
CREATE POLICY department_types_club_isolation ON department_types
    USING (club_id IN (
        SELECT club_id FROM club_members WHERE user_id = auth.uid()
    ));

-- Policy for job_functions: users can only see job functions from their club
CREATE POLICY job_functions_club_isolation ON job_functions
    USING (club_id IN (
        SELECT club_id FROM club_members WHERE user_id = auth.uid()
    ));
