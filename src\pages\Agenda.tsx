import { useState, useEffect } from "react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { CalendarDays, List, Plus, Users } from "lucide-react";
import { AgendaEventList } from "@/components/agenda/AgendaEventList";
import { AgendaEventDialog } from "@/components/agenda/AgendaEventDialog";
import { AgendaDayView } from "@/components/agenda/AgendaDayView";
import { useAgendaStore } from "@/store/useAgendaStore";
import { useCurrentClubId } from "@/context/ClubContext";

export default function Agenda() {
  const [date, setDate] = useState<Date>(new Date());
  const { agenda: events, loading, error, fetchAgenda, addAgendaEvent, updateAgendaEvent, deleteAgendaEvent } = useAgendaStore();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [view, setView] = useState<"calendar" | "list">("calendar");
  
  const clubId = useCurrentClubId();

  useEffect(() => {
    fetchAgenda(clubId);
  }, [fetchAgenda, clubId]);
  
  // Filtrar eventos para a data selecionada
  const filteredEvents = events.filter(
    (event) => {
      const eventDate = typeof event.date === 'string' ? new Date(event.date) : event.date;
      return (
        eventDate.getDate() === date.getDate() &&
        eventDate.getMonth() === date.getMonth() &&
        eventDate.getFullYear() === date.getFullYear()
      );
    }
  );
  
  // Transformar eventos para o formato exigido, sempre incluindo club_id
  const filteredEventsAsEvent = filteredEvents.map(event => ({
    ...event,
    club_id: event.club_id, // Garante que club_id está presente
    date: typeof event.date === 'string' ? new Date(event.date) : event.date,
    endTime: typeof event.endTime === 'string' ? new Date(event.endTime) : event.endTime,
  }));
  
  // Função para determinar os dias que têm eventos
  const getEventDates = () => {
    return events.reduce((dates: Record<string, boolean>, event) => {
      const eventDate = typeof event.date === 'string' ? new Date(event.date) : event.date;
      const dateKey = format(eventDate, 'yyyy-MM-dd');
      dates[dateKey] = true;
      return dates;
    }, {});
  };
  
  const eventDates = getEventDates();
  
  // Formatar o mês para exibição
  const formattedMonth = format(date, 'MMMM yyyy', { locale: ptBR });

  return (
    <div className="container mx-auto p-4 space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-3xl font-bold text-team-blue">Agenda</h1>
          <p className="text-muted-foreground">Gerencie eventos, treinos e compromissos do time</p>
        </div>
        
        <div className="flex items-center gap-4">
          <Tabs value={view} onValueChange={(v) => setView(v as "calendar" | "list")} className="hidden sm:flex">
            <TabsList>
              <TabsTrigger value="calendar" className="flex items-center gap-1.5">
                <CalendarDays className="h-4 w-4" />
                <span className="hidden sm:inline-block">Calendário</span>
              </TabsTrigger>
              <TabsTrigger value="list" className="flex items-center gap-1.5">
                <List className="h-4 w-4" />
                <span className="hidden sm:inline-block">Lista</span>
              </TabsTrigger>
            </TabsList>
          </Tabs>
          
          <Button onClick={() => setIsDialogOpen(true)} className="flex items-center gap-1.5">
            <Plus className="h-4 w-4" />
            <span className="hidden sm:inline-block">Novo Evento</span>
          </Button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="capitalize">{formattedMonth}</span>
            </CardTitle>
            <CardDescription>Selecione uma data para ver os eventos</CardDescription>
          </CardHeader>
          <CardContent>
            <Calendar
              mode="single"
              selected={date}
              onSelect={(newDate) => newDate && setDate(newDate)}
              className="border rounded-md p-3"
              modifiers={{
                eventDay: (day) => {
                  const dateKey = format(day, 'yyyy-MM-dd');
                  return eventDates[dateKey] || false;
                }
              }}
              modifiersClassNames={{
                eventDay: "relative after:absolute after:bottom-0 after:left-1/2 after:h-1 after:w-1 after:-translate-x-1/2 after:rounded-full after:bg-primary"
              }}
            />
          </CardContent>
        </Card>
        
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Eventos de {format(date, 'dd/MM/yyyy')}</span>
              <Badge variant="outline" className="font-normal">
                {filteredEvents.length} evento{filteredEvents.length !== 1 ? 's' : ''}
              </Badge>
            </CardTitle>
            <CardDescription>
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-muted-foreground" />
                <span>Planeje compromissos para a equipe</span>
              </div>
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex flex-col items-center justify-center h-60 text-center">
                <div className="text-muted-foreground mb-2">
                  <CalendarDays className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>Carregando eventos...</p>
                </div>
              </div>
            ) : (
              <>
                {view === "calendar" ? (
                  <AgendaDayView events={filteredEventsAsEvent} />
                ) : (
                  <ScrollArea className="h-[500px] pr-4">
                    <AgendaEventList events={filteredEventsAsEvent} />
                  </ScrollArea>
                )}
                
                {filteredEvents.length === 0 && (
                  <div className="flex flex-col items-center justify-center h-60 text-center">
                    <div className="text-muted-foreground mb-2">
                      <CalendarDays className="h-12 w-12 mx-auto mb-2 opacity-50" />
                      <p>Não há eventos programados para esta data</p>
                    </div>
                    <Button 
                      variant="outline" 
                      onClick={() => setIsDialogOpen(true)}
                      className="mt-4"
                    >
                      Adicionar evento
                    </Button>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>
      </div>
      
      <AgendaEventDialog open={isDialogOpen} onOpenChange={setIsDialogOpen} clubId={clubId} />
    </div>
  );
}
