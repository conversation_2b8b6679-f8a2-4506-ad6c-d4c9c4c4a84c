-- Tabela para registrar adiantamentos de salário (vales)
CREATE TABLE IF NOT EXISTS salary_advances (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL REFERENCES club_info(id),
  person_id INTEGER NOT NULL, -- ID do jogador ou colaborador
  person_type TEXT NOT NULL, -- 'player' ou 'collaborator'
  amount NUMERIC(10, 2) NOT NULL,
  description TEXT,
  advance_date DATE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  month INTEGER NOT NULL, -- Mês do adiantamento (1-12)
  year INTEGER NOT NULL, -- Ano do adiantamento
  status TEXT NOT NULL DEFAULT 'active', -- 'active' ou 'cancelled'
  payment_method TEXT,
  receipt_url TEXT
);

-- Índices para melhorar a performance
CREATE INDEX IF NOT EXISTS idx_salary_advances_club_id ON salary_advances(club_id);
CREATE INDEX IF NOT EXISTS idx_salary_advances_person ON salary_advances(person_id, person_type);
CREATE INDEX IF NOT EXISTS idx_salary_advances_date ON salary_advances(year, month);

-- Políticas de segurança
ALTER TABLE salary_advances ENABLE ROW LEVEL SECURITY;

-- Política para leitura
CREATE POLICY salary_advances_select_policy ON salary_advances
  FOR SELECT
  USING (
    club_id IN (
      SELECT club_id FROM club_members WHERE user_id = auth.uid()
    )
  );

-- Política para inserção
CREATE POLICY salary_advances_insert_policy ON salary_advances
  FOR INSERT
  WITH CHECK (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() 
      AND (
        role IN ('admin', 'president') 
        OR permissions->>'financial.edit' = 'true'
      )
    )
  );

-- Política para atualização
CREATE POLICY salary_advances_update_policy ON salary_advances
  FOR UPDATE
  USING (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() 
      AND (
        role IN ('admin', 'president') 
        OR permissions->>'financial.edit' = 'true'
      )
    )
  );

-- Política para exclusão
CREATE POLICY salary_advances_delete_policy ON salary_advances
  FOR DELETE
  USING (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() 
      AND (
        role IN ('admin', 'president') 
        OR permissions->>'financial.edit' = 'true'
      )
    )
  );
