import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ListChecks } from "lucide-react";
import type { Exercise } from "@/api/api";

interface AddExerciseToTrainingDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  exercises: Exercise[];
  onAdd: (exercise: Exercise, notes?: string) => Promise<void>;
}

export function AddExerciseToTrainingDialog({ open, onOpenChange, exercises, onAdd }: AddExerciseToTrainingDialogProps) {
  const [search, setSearch] = useState("");
  const [selected, setSelected] = useState<Exercise | null>(null);
  const [notes, setNotes] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const filtered = exercises.filter(ex => ex.name.toLowerCase().includes(search.toLowerCase()));

  const handleAdd = async () => {
    if (!selected) return;
    setLoading(true);
    setError(null);
    try {
      await onAdd(selected, notes);
      setSelected(null);
      setNotes("");
      onOpenChange(false);
    } catch (e: any) {
      setError(e.message || "Erro ao adicionar exercício");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Adicionar Exercício ao Treino</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-3">
          <Input
            placeholder="Buscar exercício..."
            value={search}
            onChange={e => setSearch(e.target.value)}
            className="mb-2"
          />
          <div className="max-h-48 overflow-y-auto divide-y rounded border bg-muted">
            {filtered.length === 0 ? (
              <div className="p-4 text-center text-muted-foreground">Nenhum exercício encontrado.</div>
            ) : (
              filtered.map(ex => (
                <div
                  key={ex.id}
                  className={`p-3 cursor-pointer hover:bg-muted/60 ${selected?.id === ex.id ? 'bg-muted' : ''}`}
                  onClick={() => setSelected(ex)}
                >
                  <div className="font-medium text-sm">{ex.name}</div>
                  <div className="text-xs text-muted-foreground">{ex.category} - {ex.difficulty}</div>
                </div>
              ))
            )}
          </div>
          {selected && (
            <div className="mt-2">
              <div className="font-medium mb-1">Notas para este exercício</div>
              <Input
                placeholder="Ex: 3 séries de 10 repetições"
                value={notes}
                onChange={e => setNotes(e.target.value)}
              />
            </div>
          )}
          {error && <div className="text-red-500 text-sm text-center">{error}</div>}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={loading}>
            Cancelar
          </Button>
          <Button onClick={handleAdd} disabled={!selected || loading} isLoading={loading}>
            Adicionar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
