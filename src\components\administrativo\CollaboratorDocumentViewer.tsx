import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/use-toast";
import { useUser } from "@/context/UserContext";
import { supabase } from "@/integrations/supabase/client";
import { Check, X, AlertCircle } from "lucide-react";
import { COLLABORATOR_DOCUMENT_LABELS } from "@/api/api";
import { formatDate } from "@/lib/utils";

// Tipo para documentos de colaborador
interface CollaboratorDocument {
  id: number;
  club_id: number;
  collaborator_id: number;
  document_type: string;
  file_url: string;
  status: string;
  uploaded_at: string;
  verified_at?: string;
  verified_by?: string;
  rejection_reason?: string;
  verifier_name?: string;
  collaborator_name?: string;
}

interface CollaboratorDocumentViewerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  collaboratorId: number;
  clubId: number;
  canVerify?: boolean;
}

export function CollaboratorDocumentViewer({
  open,
  onOpenChange,
  collaboratorId,
  clubId,
  canVerify = false,
}: CollaboratorDocumentViewerProps) {
  const { user } = useUser();
  const [documents, setDocuments] = useState<CollaboratorDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedDocument, setSelectedDocument] = useState<CollaboratorDocument | null>(null);
  const [verifying, setVerifying] = useState(false);
  const [rejectionReason, setRejectionReason] = useState("");
  const [showRejectionForm, setShowRejectionForm] = useState(false);

  // Carregar documentos quando o diálogo for aberto
  useEffect(() => {
    if (open && collaboratorId) {
      fetchDocuments();
    }
  }, [open, collaboratorId]);

  // Função para buscar documentos
  const fetchDocuments = async () => {
    try {
      setLoading(true);

      // Buscar documentos diretamente do Supabase usando SQL
      const { data, error } = await supabase.rpc('get_collaborator_documents', {
        p_club_id: clubId,
        p_collaborator_id: collaboratorId
      });

      if (error) {
        throw new Error(`Erro ao buscar documentos: ${error.message}`);
      }

      // Converter os dados para o formato esperado
      const docs = Array.isArray(data) ? data.map(doc => ({
        ...doc,
        verifier_name: null // Simplificando para evitar problemas de tipagem
      })) : [];

      setDocuments(docs as CollaboratorDocument[]);
    } catch (err: any) {
      console.error("Erro ao buscar documentos:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao buscar documentos",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Função para verificar um documento
  const handleVerifyDocument = async (status: "verified" | "rejected") => {
    if (!selectedDocument || !user) return;

    // Se for rejeição, mostrar formulário para informar o motivo
    if (status === "rejected" && !showRejectionForm) {
      setShowRejectionForm(true);
      return;
    }

    // Se for rejeição e o motivo não foi informado, exigir
    if (status === "rejected" && !rejectionReason.trim()) {
      toast({
        title: "Atenção",
        description: "Por favor, informe o motivo da rejeição para que o colaborador possa corrigir o documento.",
        variant: "destructive",
      });
      return;
    }

    try {
      setVerifying(true);

      // Verificar o documento usando a função RPC
      const { error } = await supabase.rpc('verify_collaborator_document', {
        p_document_id: selectedDocument.id,
        p_verified_by: user.id,
        p_status: status,
        p_rejection_reason: status === "rejected" ? rejectionReason : null
      });

      if (error) {
        throw new Error(`Erro ao verificar documento: ${error.message}`);
      }

      // Atualizar lista de documentos
      const updatedDocuments = documents.map((doc) =>
        doc.id === selectedDocument.id
          ? {
              ...doc,
              status,
              verified_at: new Date().toISOString(),
              verified_by: user.id,
              rejection_reason: status === "rejected" ? rejectionReason : null
            }
          : doc
      );

      setDocuments(updatedDocuments);

      toast({
        title: "Sucesso",
        description: `Documento ${status === "verified" ? "verificado" : "rejeitado"} com sucesso`,
      });

      // Limpar o formulário e fechar
      setRejectionReason("");
      setShowRejectionForm(false);

      // Fechar visualização do documento
      setSelectedDocument(null);
    } catch (err: any) {
      console.error("Erro ao verificar documento:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao verificar documento",
        variant: "destructive",
      });
    } finally {
      setVerifying(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Documentos do Colaborador</DialogTitle>
        </DialogHeader>

        {loading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        ) : documents.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            Nenhum documento encontrado
          </div>
        ) : selectedDocument ? (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">
                {COLLABORATOR_DOCUMENT_LABELS[selectedDocument.document_type] || selectedDocument.document_type}
              </h3>
              <div className="flex space-x-2">
                {canVerify && selectedDocument.status === "pending" && !showRejectionForm && (
                  <>
                    <Button
                      variant="outline"
                      className="bg-green-50 text-green-700 border-green-200 hover:bg-green-100 hover:text-green-800"
                      onClick={() => handleVerifyDocument("verified")}
                      disabled={verifying}
                    >
                      <Check className="h-4 w-4 mr-1" />
                      Aprovar
                    </Button>
                    <Button
                      variant="outline"
                      className="bg-red-50 text-red-700 border-red-200 hover:bg-red-100 hover:text-red-800"
                      onClick={() => handleVerifyDocument("rejected")}
                      disabled={verifying}
                    >
                      <X className="h-4 w-4 mr-1" />
                      Rejeitar
                    </Button>
                  </>
                )}

                {canVerify && selectedDocument.status === "pending" && showRejectionForm && (
                  <>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setShowRejectionForm(false);
                        setRejectionReason("");
                      }}
                      disabled={verifying}
                    >
                      Cancelar
                    </Button>
                    <Button
                      variant="outline"
                      className="bg-red-50 text-red-700 border-red-200 hover:bg-red-100 hover:text-red-800"
                      onClick={() => handleVerifyDocument("rejected")}
                      disabled={verifying}
                    >
                      <X className="h-4 w-4 mr-1" />
                      Confirmar Rejeição
                    </Button>
                  </>
                )}
                <Button
                  variant="outline"
                  onClick={() => setSelectedDocument(null)}
                >
                  Voltar
                </Button>
              </div>
            </div>

            {showRejectionForm && (
              <div className="space-y-2 border rounded-md p-4 bg-gray-50">
                <Label htmlFor="rejectionReason">Motivo da Rejeição</Label>
                <Input
                  id="rejectionReason"
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  placeholder="Informe o motivo da rejeição"
                />
                <p className="text-xs text-muted-foreground">
                  Este motivo será exibido para o colaborador para que ele possa corrigir o documento.
                </p>
              </div>
            )}

            <div className="border rounded-md p-2 overflow-hidden" style={{ maxHeight: "60vh" }}>
              {selectedDocument.file_url.endsWith(".pdf") ? (
                <iframe
                  src={`https://docs.google.com/viewer?url=${encodeURIComponent(selectedDocument.file_url)}&embedded=true`}
                  className="w-full h-[60vh] border-0"
                  title="Documento"
                  allowFullScreen
                />
              ) : (
                <div className="flex justify-center" style={{ maxHeight: "58vh", overflow: "hidden" }}>
                  <img
                    src={selectedDocument.file_url}
                    alt="Documento"
                    className="max-h-[58vh] object-contain"
                    onClick={(e) => e.preventDefault()}
                    style={{ pointerEvents: "none", display: "block" }}
                  />
                </div>
              )}
            </div>

            <div className="text-sm text-muted-foreground">
              <p>Enviado em: {formatDate(selectedDocument.uploaded_at)}</p>
              {selectedDocument.verified_at && (
                <p>
                  {selectedDocument.status === "verified" ? "Verificado" : "Rejeitado"} em:{" "}
                  {formatDate(selectedDocument.verified_at)}
                </p>
              )}
              {selectedDocument.rejection_reason && (
                <div className="mt-2 p-2 bg-red-50 text-red-700 rounded-md">
                  <p className="font-medium">Motivo da rejeição:</p>
                  <p>{selectedDocument.rejection_reason}</p>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {documents.map((document) => (
              <div
                key={document.id}
                className="border rounded-md p-4 cursor-pointer hover:bg-gray-50"
                onClick={() => setSelectedDocument(document)}
              >
                <div className="flex justify-between items-start">
                  <h3 className="font-medium">
                    {COLLABORATOR_DOCUMENT_LABELS[document.document_type] || document.document_type}
                  </h3>
                  <div className="flex items-center">
                    {document.status === "pending" && (
                      <div className="flex items-center bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        Pendente
                      </div>
                    )}
                    {document.status === "verified" && (
                      <div className="flex items-center bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
                        <Check className="h-3 w-3 mr-1" />
                        Verificado
                      </div>
                    )}
                    {document.status === "rejected" && (
                      <div className="flex items-center bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs">
                        <X className="h-3 w-3 mr-1" />
                        Rejeitado
                      </div>
                    )}
                  </div>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  Enviado em: {formatDate(document.uploaded_at)}
                </p>
              </div>
            ))}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
