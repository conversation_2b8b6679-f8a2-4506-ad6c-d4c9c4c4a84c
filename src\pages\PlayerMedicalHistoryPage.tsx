import { useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { usePlayerProfileStore } from "@/store/usePlayerProfileStore";
import { useCurrentClubId } from "@/context/ClubContext";
import { PlayerMedicalHistory } from "@/components/player/PlayerMedicalHistory";
import { ChevronLeft } from "lucide-react";

export default function PlayerMedicalHistoryPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const clubId = useCurrentClubId();
  const { player, loading, error, fetchPlayerProfile } = usePlayerProfileStore();

  useEffect(() => {
    if (id) {
      fetchPlayerProfile(clubId, id);
    }
  }, [clubId, id, fetchPlayerProfile]);

  const handleBack = () => {
    navigate(`/jogador/${id}`);
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center py-8">Carregando histórico médico do jogador...</div>
      </div>
    );
  }

  if (error || !player) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center py-8 text-red-500">
          {error || "Erro ao carregar dados do jogador"}
        </div>
        <div className="flex justify-center">
          <Button onClick={handleBack} variant="outline">
            <ChevronLeft className="h-4 w-4 mr-2" />
            Voltar ao perfil
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center gap-4">
        <Button onClick={handleBack} variant="outline">
          <ChevronLeft className="h-4 w-4 mr-2" />
          Voltar ao perfil
        </Button>
        <h1 className="text-3xl font-bold">Histórico Médico: {player.name}</h1>
      </div>

      <Card>
        <CardContent className="pt-6">
          <PlayerMedicalHistory player={player} />
        </CardContent>
      </Card>
    </div>
  );
}
