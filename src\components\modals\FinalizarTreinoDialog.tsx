import { <PERSON><PERSON>, Di<PERSON><PERSON>ontent, <PERSON><PERSON>Header, Di<PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useState } from "react";

interface FinalizarTreinoDialogProps {
  open: boolean;
  onClose: () => void;
  onFinalize: (summary: string, description: string, clubId: number) => void;
  clubId: number;
}

export function FinalizarTreinoDialog({ open, onClose, onFinalize, clubId }: FinalizarTreinoDialogProps) {
  const [summary, setSummary] = useState("");
  const [description, setDescription] = useState("");
  const [error, setError] = useState<string | null>(null);

  const handleFinalize = () => {
    if (!summary.trim()) {
      setError("O resumo é obrigatório.");
      return;
    }
    setError(null);
    onFinalize(summary, description, clubId);
    setSummary("");
    setDescription("");
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Finalizar Treino</DialogTitle>
        </DialogHeader>
        <div className="space-y-2">
          <label>Resumo do Treino <span className="text-red-500">*</span></label>
          <Input placeholder="Resumo do treino" value={summary} onChange={e => setSummary(e.target.value)} />
          <label>Descrição Detalhada</label>
          <Textarea placeholder="Descrição detalhada do treino (opcional)" value={description} onChange={e => setDescription(e.target.value)} />
          {error && <div className="text-red-500 text-sm">{error}</div>}
        </div>
        <DialogFooter>
          <Button variant="secondary" onClick={onClose}>Cancelar</Button>
          <Button onClick={handleFinalize}>Finalizar</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
