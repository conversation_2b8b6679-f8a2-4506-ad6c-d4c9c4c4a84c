import { useSeasonStore } from "@/store/useSeasonStore";
import { useCurrentClubId } from "@/context/ClubContext";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { getSeasonStats } from "@/api/seasonApi";
import { BarChart2, Trophy, Users, Calendar, RefreshCw } from "lucide-react";

export default function EstatisticasTemporada() {
  // Adicione um fallback para nunca chamar o hook fora do provider
  let clubId: number | undefined = undefined;
  try {
    clubId = useCurrentClubId();
  } catch (e) {
    // Se não houver contexto, não renderize nada (resolve crash do app)
    return null;
  }
  const { seasons, activeSeason, fetchSeasons } = useSeasonStore();
  const navigate = useNavigate();

  useEffect(() => {
    if (clubId && seasons.length === 0) fetchSeasons(clubId);
  }, [clubId, seasons.length, fetchSeasons]);

  const [stats, setStats] = useState<any | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!activeSeason || !clubId) return;
    setLoading(true);
    setError(null);
    getSeasonStats(activeSeason.id, clubId)
      .then(setStats)
      .catch(e => setError(e.message))
      .finally(() => setLoading(false));
  }, [activeSeason, clubId]);

  if (!activeSeason) {
    return (
      <div className="p-8 text-center">
        <p className="text-lg">Selecione uma temporada para ver as estatísticas.</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="p-8 text-center">
        <RefreshCw className="animate-spin mx-auto mb-2" />
        <p>Carregando estatísticas...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8 text-center text-red-600">
        <p>Erro ao carregar estatísticas: {error}</p>
      </div>
    );
  }

  if (!stats) return null;

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Estatísticas da Temporada</h1>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded shadow p-4 flex flex-col items-center">
          <Calendar className="mb-2 text-blue-600" />
          <span className="text-2xl font-bold">{stats.jogos}</span>
          <span className="text-gray-500 text-sm">Jogos</span>
        </div>
        <div className="bg-white rounded shadow p-4 flex flex-col items-center">
          <Trophy className="mb-2 text-green-600" />
          <span className="text-2xl font-bold">{stats.vitorias}</span>
          <span className="text-gray-500 text-sm">Vitórias</span>
        </div>
        <div className="bg-white rounded shadow p-4 flex flex-col items-center">
          <Trophy className="mb-2 text-gray-400 rotate-180" />
          <span className="text-2xl font-bold">{stats.derrotas}</span>
          <span className="text-gray-500 text-sm">Derrotas</span>
        </div>
        <div className="bg-white rounded shadow p-4 flex flex-col items-center">
          <Trophy className="mb-2 text-yellow-400" />
          <span className="text-2xl font-bold">{stats.empates}</span>
          <span className="text-gray-500 text-sm">Empates</span>
        </div>
        <div className="bg-white rounded shadow p-4 flex flex-col items-center">
          <Users className="mb-2 text-indigo-600" />
          <span className="text-2xl font-bold">{stats.jogadores}</span>
          <span className="text-gray-500 text-sm">Jogadores</span>
        </div>
        <div className="bg-white rounded shadow p-4 flex flex-col items-center">
          <BarChart2 className="mb-2 text-orange-600" />
          <span className="text-2xl font-bold">{stats.lesoes}</span>
          <span className="text-gray-500 text-sm">Lesões</span>
        </div>
        <div className="bg-white rounded shadow p-4 flex flex-col items-center">
          <BarChart2 className="mb-2 text-cyan-600" />
          <span className="text-2xl font-bold">{stats.treinos}</span>
          <span className="text-gray-500 text-sm">Treinos</span>
        </div>
        <div className="bg-white rounded shadow p-4 flex flex-col items-center">
          <BarChart2 className="mb-2 text-lime-600" />
          <span className="text-2xl font-bold">{stats.golsPro}</span>
          <span className="text-gray-500 text-sm">Gols Pró</span>
        </div>
        <div className="bg-white rounded shadow p-4 flex flex-col items-center">
          <BarChart2 className="mb-2 text-red-600" />
          <span className="text-2xl font-bold">{stats.golsContra}</span>
          <span className="text-gray-500 text-sm">Gols Contra</span>
        </div>
      </div>
      {/* Futuramente: gráficos, tabelas detalhadas, filtros por jogador, etc. */}
    </div>
  );
}
