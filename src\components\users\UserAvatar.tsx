import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { getFirstName } from "@/api/external";

interface User {
  id: string;
  name: string;
  profile_image?: string;
}

interface UserAvatarProps {
  user: User;
  size?: "sm" | "md" | "lg";
}

export function UserAvatar({ user, size = "md" }: UserAvatarProps) {
  // Obter as iniciais do nome do usuário
  const getInitials = (name: string) => {
    if (!name) return "U";

    const firstName = getFirstName(name);
    if (firstName.length <= 2) return firstName.toUpperCase();

    return firstName.charAt(0).toUpperCase();
  };

  // Obter a classe de tamanho
  const getSizeClass = () => {
    switch (size) {
      case "sm":
        return "h-8 w-8 text-xs";
      case "lg":
        return "h-12 w-12 text-lg";
      default:
        return "h-10 w-10 text-sm";
    }
  };

  // Gerar uma cor baseada no ID do usuário
  const getColorClass = () => {
    if (!user.id) return "bg-gray-200 text-gray-700";

    // Usar o último caractere do ID para determinar a cor
    const lastChar = user.id.charAt(user.id.length - 1);
    const colorIndex = parseInt(lastChar, 16) % 8;

    const colors = [
      "bg-red-100 text-red-800",
      "bg-primary/10 text-primary",
      "bg-green-100 text-green-800",
      "bg-yellow-100 text-yellow-800",
      "bg-purple-100 text-purple-800",
      "bg-pink-100 text-pink-800",
      "bg-indigo-100 text-indigo-800",
      "bg-teal-100 text-teal-800",
    ];

    return colors[colorIndex];
  };

  return (
    <Avatar className={getSizeClass()}>
      <AvatarImage src={user.profile_image} alt={user.name} />
      <AvatarFallback className={getColorClass()}>
        {getInitials(user.name)}
      </AvatarFallback>
    </Avatar>
  );
}
