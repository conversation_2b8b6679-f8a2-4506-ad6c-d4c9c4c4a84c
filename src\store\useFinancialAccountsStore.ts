import { create } from "zustand";
import { FinancialAccount, getFinancialAccounts, createFinancialAccount, updateFinancialAccount, deleteFinancialAccount, uploadAccountReceipt } from "../api/api";

interface FinancialAccountsState {
  accounts: FinancialAccount[];
  loading: boolean;
  error: string | null;
  fetchAccounts: (clubId: number) => Promise<void>;
  addAccount: (clubId: number, account: Omit<FinancialAccount, "id" | "club_id" | "created_at" | "updated_at">) => Promise<void>;
  updateAccount: (clubId: number, id: number, account: Partial<FinancialAccount>) => Promise<void>;
  deleteAccount: (clubId: number, id: number) => Promise<void>;
  uploadReceipt: (clubId: number, id: number, file: File) => Promise<void>;
  markAsPaid: (clubId: number, id: number) => Promise<void>;
}

export const useFinancialAccountsStore = create<FinancialAccountsState>((set) => ({
  accounts: [],
  loading: false,
  error: null,

  fetchAccounts: async (clubId: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const accounts = await getFinancialAccounts(clubId);
      console.log("Contas carregadas no store:", accounts);
      set({ accounts, loading: false });
    } catch (err: unknown) {
      console.error("Erro ao buscar contas:", err);
      set({ error: err instanceof Error ? err.message : "Erro ao buscar contas", loading: false });
    }
  },

  addAccount: async (clubId: number, account: Omit<FinancialAccount, "id" | "club_id" | "created_at" | "updated_at">): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const newAccount = await createFinancialAccount(clubId, account);
      set((state) => ({ accounts: [...state.accounts, newAccount], loading: false }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao adicionar conta", loading: false });
    }
  },

  updateAccount: async (clubId: number, id: number, account: Partial<FinancialAccount>): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const updated = await updateFinancialAccount(clubId, id, account);
      if (updated) {
        set((state) => ({ accounts: state.accounts.map(a => a.id === id ? updated : a), loading: false }));
      } else {
        set({ error: "Conta não encontrada", loading: false });
      }
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao atualizar conta", loading: false });
    }
  },

  deleteAccount: async (clubId: number, id: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const ok = await deleteFinancialAccount(clubId, id);
      if (ok) {
        set((state) => ({ accounts: state.accounts.filter(a => a.id !== id), loading: false }));
      } else {
        set({ error: "Conta não encontrada", loading: false });
      }
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao deletar conta", loading: false });
    }
  },

  uploadReceipt: async (clubId: number, id: number, file: File): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const updated = await uploadAccountReceipt(clubId, id, file);
      if (updated) {
        set((state) => ({ accounts: state.accounts.map(a => a.id === id ? updated : a), loading: false }));
      } else {
        set({ error: "Conta não encontrada", loading: false });
      }
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao fazer upload do comprovante", loading: false });
    }
  },

  markAsPaid: async (clubId: number, id: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      // Find the account to get its type
      const account = set.getState().accounts.find(a => a.id === id);
      if (!account) {
        set({ error: "Conta não encontrada", loading: false });
        return;
      }

      // Set the appropriate status based on the account type
      const status = account.type === "a_pagar" ? "pago" : "recebido";
      
      const updated = await updateFinancialAccount(clubId, id, { status });
      if (updated) {
        set((state) => ({ accounts: state.accounts.map(a => a.id === id ? updated : a), loading: false }));
      } else {
        set({ error: "Conta não encontrada", loading: false });
      }
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao marcar como pago/recebido", loading: false });
    }
  },
}));
