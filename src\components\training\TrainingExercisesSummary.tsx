import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, Too<PERSON><PERSON><PERSON>ontent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { ListChecks } from "lucide-react";
import { getTrainingExercises } from "@/api";
import type { TrainingExercise } from "@/api/api";

interface TrainingExercisesSummaryProps {
  trainingId: number;
}

export function TrainingExercisesSummary({ trainingId }: TrainingExercisesSummaryProps) {
  const [exercises, setExercises] = useState<TrainingExercise[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    setError(null);
    getTrainingExercises(trainingId)
      .then(setExercises)
      .catch((e) => setError(e.message))
      .finally(() => setLoading(false));
  }, [trainingId]);

  if (loading) return <span className="text-xs text-muted-foreground">...</span>;
  if (error) return <span className="text-xs text-red-500">Erro</span>;
  if (exercises.length === 0) return <span className="text-xs text-muted-foreground">Nenhum exercício</span>;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex gap-1 flex-wrap">
            {exercises.slice(0, 2).map(ex => (
              <Badge key={ex.id} variant="secondary" className="px-2 py-0.5 text-xs">
                {ex.exercises.name}
              </Badge>
            ))}
            {exercises.length > 2 && (
              <Badge variant="outline" className="px-2 py-0.5 text-xs">+{exercises.length - 2}</Badge>
            )}
          </div>
        </TooltipTrigger>
        <TooltipContent side="bottom">
          <div className="min-w-[180px]">
            <div className="font-medium mb-1 flex items-center gap-1">
              <ListChecks className="w-3 h-3" /> Exercícios do treino
            </div>
            <ul className="text-xs text-muted-foreground list-disc pl-4">
              {exercises.map(ex => (
                <li key={ex.id}>{ex.exercises.name}{ex.notes ? ` (${ex.notes})` : ''}</li>
              ))}
            </ul>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
