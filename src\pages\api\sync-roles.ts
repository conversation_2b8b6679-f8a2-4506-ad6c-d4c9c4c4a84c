import type { NextApiRequest, NextApiResponse } from 'next';
import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Criar cliente Supabase com contexto do servidor
  const supabase = createServerSupabaseClient({ req, res });

  // Verificar se o usuário está autenticado
  const { data: { session } } = await supabase.auth.getSession();
  if (!session) {
    return res.status(401).json({ error: 'Não autorizado' });
  }

  // Verificar se o método é POST
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Método não permitido' });
  }

  try {
    const { clubId } = req.body;

    if (!clubId) {
      return res.status(400).json({ error: 'ID do clube é obrigatório' });
    }

    // Verificar se o usuário é admin ou presidente do clube
    const { data: member, error: memberError } = await supabase
      .from('club_members')
      .select('role')
      .eq('club_id', clubId)
      .eq('user_id', session.user.id)
      .single();

    if (memberError || !member || !['admin', 'president'].includes(member.role)) {
      return res.status(403).json({ error: 'Permissão negada' });
    }

    // Buscar todos os colaboradores que têm user_id
    const { data: collaborators, error: collaboratorsError } = await supabase
      .from('collaborators')
      .select('id, user_id, role, full_name')
      .eq('club_id', clubId)
      .not('user_id', 'is', null);

    if (collaboratorsError) {
      return res.status(500).json({ error: `Erro ao buscar colaboradores: ${collaboratorsError.message}` });
    }

    if (!collaborators || collaborators.length === 0) {
      return res.status(200).json({ message: 'Nenhum colaborador com user_id encontrado', updated: 0 });
    }

    // Atualizar o role de cada usuário
    let updatedCount = 0;
    let errors = 0;
    const updates = [];

    for (const collaborator of collaborators) {
      if (!collaborator.user_id || !collaborator.role) continue;

      try {
        const { error: updateError } = await supabase
          .from('users')
          .update({ role: collaborator.role })
          .eq('id', collaborator.user_id);

        if (updateError) {
          console.error(`Erro ao atualizar role do usuário ${collaborator.user_id}:`, updateError);
          errors++;
        } else {
          console.log(`Role do usuário ${collaborator.user_id} atualizado para ${collaborator.role}`);
          updatedCount++;
          updates.push({
            id: collaborator.id,
            name: collaborator.full_name,
            role: collaborator.role
          });
        }
      } catch (error) {
        console.error(`Erro ao atualizar usuário ${collaborator.user_id}:`, error);
        errors++;
      }
    }

    return res.status(200).json({
      message: `${updatedCount} usuários atualizados com sucesso`,
      updated: updatedCount,
      errors,
      updates
    });
  } catch (error) {
    console.error('Erro ao sincronizar roles:', error);
    const errorMessage = error instanceof Error ? error.message : 'Erro ao sincronizar roles';
    return res.status(500).json({ error: errorMessage });
  }
}
