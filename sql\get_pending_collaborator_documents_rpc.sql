-- C<PERSON>r função RPC para buscar documentos pendentes de colaboradores
CREATE OR REPLACE FUNCTION get_pending_collaborator_documents(
  p_club_id INTEGER
) RETURNS TABLE (
  id INTEGER,
  club_id INTEGER,
  collaborator_id INTEGER,
  document_type TEXT,
  file_url TEXT,
  status TEXT,
  uploaded_at TIMESTAMPTZ,
  verified_at TIMESTAMPTZ,
  verified_by UUI<PERSON>,
  rejection_reason TEXT,
  collaborator_name TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    cd.id,
    cd.club_id,
    cd.collaborator_id,
    cd.document_type,
    cd.file_url,
    cd.status,
    cd.uploaded_at,
    cd.verified_at,
    cd.verified_by,
    cd.rejection_reason,
    c.full_name AS collaborator_name
  FROM collaborator_documents cd
  JOIN collaborators c ON cd.collaborator_id = c.id
  WHERE cd.club_id = p_club_id
  AND cd.status = 'pending'
  ORDER BY cd.uploaded_at;
END;
$$ LANGUAGE plpgsql;
