import { jsPDF } from "jspdf";
import html2canvas from "html2canvas";
import { Player, ClubInfo } from "@/api/api";

// Ordem das posições para o relatório
const POSITION_ORDER = [
  "Goleiro",
  "Zague<PERSON>",
  "Lateral",
  "Volante",
  "Meio-campista",
  "Extremo",
  "Atacante",
  "Outro"
];

/**
 * Gera um relatório de jogadores por categoria em PDF
 * @param players Lista de jogadores
 * @param categoryName Nome da categoria
 * @param clubInfo Informações do clube
 * @param filename Nome do arquivo PDF
 */
export async function generateCategoryReport(
  players: Player[],
  categoryName: string,
  clubInfo: ClubInfo,
  filename: string = 'relatorio-categoria.pdf'
): Promise<void> {
  // Filtrar jogadores ativos (não inativos e não emprestados)
  const activePlayers = players.filter(player =>
    player.status !== "inativo" && player.status !== "emprestado"
  );

  // Ordenar jogadores por posição conforme a ordem definida
  const sortedPlayers = [...activePlayers].sort((a, b) => {
    const posA = POSITION_ORDER.indexOf(a.position);
    const posB = POSITION_ORDER.indexOf(b.position);

    // Se ambas as posições estão na lista, ordenar conforme a lista
    if (posA !== -1 && posB !== -1) {
      return posA - posB;
    }

    // Se apenas uma posição está na lista, ela vem primeiro
    if (posA !== -1) return -1;
    if (posB !== -1) return 1;

    // Se nenhuma posição está na lista, ordenar alfabeticamente
    return a.position.localeCompare(b.position);
  });

  // Agrupar jogadores por posição
  const playersByPosition: Record<string, Player[]> = {};

  sortedPlayers.forEach(player => {
    if (!playersByPosition[player.position]) {
      playersByPosition[player.position] = [];
    }
    playersByPosition[player.position].push(player);
  });

  // Criar um elemento temporário para renderizar o relatório
  const reportElement = document.createElement('div');
  reportElement.style.width = '210mm'; // Largura A4
  reportElement.style.padding = '10mm';
  reportElement.style.backgroundColor = 'white';
  reportElement.style.fontFamily = 'Arial, sans-serif';

  // Cabeçalho com informações do clube
  const header = document.createElement('div');
  header.style.display = 'flex';
  header.style.alignItems = 'center';
  header.style.marginBottom = '20px';

  // Logo do clube
  const logoDiv = document.createElement('div');
  logoDiv.style.width = '80px';
  logoDiv.style.height = '80px';
  logoDiv.style.marginRight = '20px';

  if (clubInfo.logo_url) {
    const logo = document.createElement('img');
    logo.src = clubInfo.logo_url;
    logo.style.width = '100%';
    logo.style.height = '100%';
    logo.style.objectFit = 'contain';
    logoDiv.appendChild(logo);
  } else {
    logoDiv.style.backgroundColor = '#f3f4f6';
    logoDiv.style.display = 'flex';
    logoDiv.style.alignItems = 'center';
    logoDiv.style.justifyContent = 'center';
    logoDiv.textContent = clubInfo.name?.substring(0, 2) || 'CL';
  }

  // Informações do clube
  const clubInfoDiv = document.createElement('div');

  const clubName = document.createElement('h1');
  clubName.textContent = clubInfo.name || 'Nome do Clube';
  clubName.style.margin = '0 0 5px 0';
  clubName.style.fontSize = '40px'; // Aumentado para 40px

  const clubAddress = document.createElement('p');
  clubAddress.textContent = clubInfo.address ? `${clubInfo.address}, ${clubInfo.zip_code || ''}` : '';
  clubAddress.style.margin = '0 0 3px 0';
  clubAddress.style.fontSize = '14px';

  const clubPhone = document.createElement('p');
  clubPhone.textContent = clubInfo.phone || '';
  clubPhone.style.margin = '0';
  clubPhone.style.fontSize = '14px';

  clubInfoDiv.appendChild(clubName);
  clubInfoDiv.appendChild(clubAddress);
  clubInfoDiv.appendChild(clubPhone);

  header.appendChild(logoDiv);
  header.appendChild(clubInfoDiv);
  reportElement.appendChild(header);

  // Título do relatório
  const title = document.createElement('h2');
  title.textContent = `Relatório de Jogadores - Categoria ${categoryName}`;
  title.style.textAlign = 'center';
  title.style.margin = '20px 0';
  reportElement.appendChild(title);

  // Data do relatório
  const date = document.createElement('p');
  date.textContent = `Data: ${new Date().toLocaleDateString()}`;
  date.style.textAlign = 'right';
  date.style.margin = '10px 0 20px';
  reportElement.appendChild(date);

  // Verificar se há jogadores
  if (Object.keys(playersByPosition).length === 0) {
    const noPlayers = document.createElement('p');
    noPlayers.textContent = 'Nenhum jogador encontrado nesta categoria.';
    noPlayers.style.textAlign = 'center';
    noPlayers.style.margin = '30px 0';
    noPlayers.style.fontStyle = 'italic';
    reportElement.appendChild(noPlayers);
  } else {
    // Iterar pelas posições na ordem definida
    POSITION_ORDER.forEach(position => {
      const playersInPosition = playersByPosition[position];

      if (playersInPosition && playersInPosition.length > 0) {
        // Título da posição
        const positionTitle = document.createElement('h3');
        positionTitle.textContent = position;
        positionTitle.style.marginTop = '30px';
        positionTitle.style.marginBottom = '10px';
        positionTitle.style.borderBottom = '1px solid #e5e7eb';
        positionTitle.style.paddingBottom = '5px';
        reportElement.appendChild(positionTitle);

        // Tabela de jogadores desta posição
        const table = document.createElement('table');
        table.style.width = '100%';
        table.style.borderCollapse = 'collapse';

        // Cabeçalho da tabela
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');

        const headers = ['Nome', 'Apelido', 'Data Nasc.', 'Peso', 'Altura', 'Status'];

        headers.forEach(headerText => {
          const th = document.createElement('th');
          th.textContent = headerText;
          th.style.padding = '8px';
          th.style.textAlign = 'left';
          th.style.borderBottom = '1px solid #e5e7eb';
          th.style.backgroundColor = '#f9fafb';
          headerRow.appendChild(th);
        });

        thead.appendChild(headerRow);
        table.appendChild(thead);

        // Corpo da tabela
        const tbody = document.createElement('tbody');

        playersInPosition.forEach(player => {
          const row = document.createElement('tr');

          // Nome
          const tdName = document.createElement('td');
          tdName.textContent = player.name;
          tdName.style.padding = '8px';
          tdName.style.borderBottom = '1px solid #e5e7eb';
          row.appendChild(tdName);

          // Apelido
          const tdNickname = document.createElement('td');
          tdNickname.textContent = player.nickname || '-';
          tdNickname.style.padding = '8px';
          tdNickname.style.borderBottom = '1px solid #e5e7eb';
          row.appendChild(tdNickname);

          // Data de Nascimento
          const tdBirthdate = document.createElement('td');
          tdBirthdate.textContent = player.birthdate
            ? new Date(player.birthdate).toLocaleDateString()
            : '-';
          tdBirthdate.style.padding = '8px';
          tdBirthdate.style.borderBottom = '1px solid #e5e7eb';
          row.appendChild(tdBirthdate);

          // Peso
          const tdWeight = document.createElement('td');
          tdWeight.textContent = player.weight ? `${player.weight} kg` : '-';
          tdWeight.style.padding = '8px';
          tdWeight.style.borderBottom = '1px solid #e5e7eb';
          row.appendChild(tdWeight);

          // Altura
          const tdHeight = document.createElement('td');
          tdHeight.textContent = player.height ? `${player.height} cm` : '-';
          tdHeight.style.padding = '8px';
          tdHeight.style.borderBottom = '1px solid #e5e7eb';
          row.appendChild(tdHeight);

          // Status
          const tdStatus = document.createElement('td');
          tdStatus.textContent = formatStatus(player.status);
          tdStatus.style.padding = '8px';
          tdStatus.style.borderBottom = '1px solid #e5e7eb';
          row.appendChild(tdStatus);

          tbody.appendChild(row);
        });

        table.appendChild(tbody);
        reportElement.appendChild(table);
      }
    });

    // Adicionar outras posições que não estão na lista predefinida
    Object.keys(playersByPosition).forEach(position => {
      if (!POSITION_ORDER.includes(position)) {
        const playersInPosition = playersByPosition[position];

        // Título da posição
        const positionTitle = document.createElement('h3');
        positionTitle.textContent = position;
        positionTitle.style.marginTop = '30px';
        positionTitle.style.marginBottom = '10px';
        positionTitle.style.borderBottom = '1px solid #e5e7eb';
        positionTitle.style.paddingBottom = '5px';
        reportElement.appendChild(positionTitle);

        // Tabela de jogadores desta posição
        const table = document.createElement('table');
        table.style.width = '100%';
        table.style.borderCollapse = 'collapse';

        // Cabeçalho da tabela
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');

        const headers = ['Nome', 'Apelido', 'Data Nasc.', 'Peso', 'Altura', 'Status'];

        headers.forEach(headerText => {
          const th = document.createElement('th');
          th.textContent = headerText;
          th.style.padding = '8px';
          th.style.textAlign = 'left';
          th.style.borderBottom = '1px solid #e5e7eb';
          th.style.backgroundColor = '#f9fafb';
          headerRow.appendChild(th);
        });

        thead.appendChild(headerRow);
        table.appendChild(thead);

        // Corpo da tabela
        const tbody = document.createElement('tbody');

        playersInPosition.forEach(player => {
          const row = document.createElement('tr');

          // Nome
          const tdName = document.createElement('td');
          tdName.textContent = player.name;
          tdName.style.padding = '8px';
          tdName.style.borderBottom = '1px solid #e5e7eb';
          row.appendChild(tdName);

          // Apelido
          const tdNickname = document.createElement('td');
          tdNickname.textContent = player.nickname || '-';
          tdNickname.style.padding = '8px';
          tdNickname.style.borderBottom = '1px solid #e5e7eb';
          row.appendChild(tdNickname);

          // Data de Nascimento
          const tdBirthdate = document.createElement('td');
          tdBirthdate.textContent = player.birthdate
            ? new Date(player.birthdate).toLocaleDateString()
            : '-';
          tdBirthdate.style.padding = '8px';
          tdBirthdate.style.borderBottom = '1px solid #e5e7eb';
          row.appendChild(tdBirthdate);

          // Peso
          const tdWeight = document.createElement('td');
          tdWeight.textContent = player.weight ? `${player.weight} kg` : '-';
          tdWeight.style.padding = '8px';
          tdWeight.style.borderBottom = '1px solid #e5e7eb';
          row.appendChild(tdWeight);

          // Altura
          const tdHeight = document.createElement('td');
          tdHeight.textContent = player.height ? `${player.height} cm` : '-';
          tdHeight.style.padding = '8px';
          tdHeight.style.borderBottom = '1px solid #e5e7eb';
          row.appendChild(tdHeight);

          // Status
          const tdStatus = document.createElement('td');
          tdStatus.textContent = formatStatus(player.status);
          tdStatus.style.padding = '8px';
          tdStatus.style.borderBottom = '1px solid #e5e7eb';
          row.appendChild(tdStatus);

          tbody.appendChild(row);
        });

        table.appendChild(tbody);
        reportElement.appendChild(table);
      }
    });
  }

  // Criar o cabeçalho como um elemento separado para repetição em cada página
  const headerElement = document.createElement('div');
  headerElement.style.width = '210mm'; // Largura A4
  headerElement.style.padding = '10mm';
  headerElement.style.backgroundColor = 'white';
  headerElement.style.fontFamily = 'Arial, sans-serif';

  // Clonar o cabeçalho para o elemento de cabeçalho separado
  const headerClone = header.cloneNode(true);
  headerElement.appendChild(headerClone);

  // Adicionar os elementos ao documento temporariamente
  document.body.appendChild(reportElement);
  document.body.appendChild(headerElement);

  // Importar a função generatePDF do módulo pdfGenerator
  const { generatePDF } = await import('./pdfGenerator');

  // Gerar o PDF com suporte a múltiplas páginas e cabeçalho repetido
  try {
    await generatePDF(reportElement, filename, { orientation: 'portrait' }, headerElement);
  } finally {
    // Remover os elementos temporários
    document.body.removeChild(reportElement);
    document.body.removeChild(headerElement);
  }
}

/**
 * Formata o status do jogador para exibição
 * @param status Status do jogador
 * @returns Status formatado
 */
function formatStatus(status: string): string {
  switch (status) {
    case "disponivel":
      return "Disponível";
    case "lesionado":
      return "Lesionado";
    case "suspenso":
      return "Suspenso";
    case "em recuperacao":
    case "em recuperação":
      return "Em recuperação";
    case "inativo":
      return "Inativo";
    case "emprestado":
      return "Emprestado";
    case "em avaliacao":
      return "Em Avaliação";
    default:
      return status;
  }
}
