import { create } from "zustand";
import type { Database } from "@/integrations/supabase/types";
type RehabSession = Database['public']['Tables']['rehab_sessions']['Row'];
import { getRehabSessions, createRehabSession, updateRehabSession, deleteRehabSession, archiveRehabSession } from "@/api/rehabSessions";

interface RehabSessionsState {
  rehabSessions: RehabSession[];
  loading: boolean;
  error: string | null;
  fetchRehabSessions: (clubId: number, options?: { includeArchived?: boolean }) => Promise<void>;
  addRehabSession: (clubId: number, session: Omit<RehabSession, "id">) => Promise<void>;
  updateRehabSession: (clubId: number, id: number, session: Partial<RehabSession>) => Promise<void>;
  deleteRehabSession: (clubId: number, id: number) => Promise<void>;
  archiveRehabSession: (clubId: number, id: number, archived: boolean) => Promise<void>;
}

export const useRehabSessionsStore = create<RehabSessionsState>((set) => ({
  rehabSessions: [],
  loading: false,
  error: null,
  fetchRehabSessions: async (clubId, options) => {
    set({ loading: true, error: null });
    try {
      const sessions = await getRehabSessions(clubId, options);
      set({ rehabSessions: sessions, loading: false });
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao buscar sessões", loading: false });
    }
  },
  addRehabSession: async (clubId, session) => {
    set({ loading: true, error: null });
    try {
      const newSession = await createRehabSession(clubId, session);
      set((state) => ({ rehabSessions: [...state.rehabSessions, newSession], loading: false }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao adicionar sessão", loading: false });
    }
  },
  updateRehabSession: async (clubId, id, session) => {
    set({ loading: true, error: null });
    try {
      const updated = await updateRehabSession(clubId, id, session);
      set((state) => ({ rehabSessions: state.rehabSessions.map(s => s.id === id ? updated : s), loading: false }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao atualizar sessão", loading: false });
    }
  },
  deleteRehabSession: async (clubId, id) => {
    set({ loading: true, error: null });
    try {
      const ok = await deleteRehabSession(clubId, id);
      if (ok) {
        set((state) => ({ rehabSessions: state.rehabSessions.filter(s => s.id !== id), loading: false }));
      } else {
        set({ error: "Sessão não encontrada", loading: false });
      }
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao deletar sessão", loading: false });
    }
  },

  archiveRehabSession: async (clubId, id, archived) => {
    set({ loading: true, error: null });
    try {
      const updated = await archiveRehabSession(clubId, id, archived);
      set((state) => ({
        rehabSessions: state.rehabSessions.map(s => s.id === id ? updated : s),
        loading: false
      }));
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : `Erro ao ${archived ? 'arquivar' : 'desarquivar'} sessão`,
        loading: false
      });
    }
  },
}));
