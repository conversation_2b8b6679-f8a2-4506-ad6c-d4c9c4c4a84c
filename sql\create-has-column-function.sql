-- C<PERSON>r função has_column para verificar se uma coluna existe em uma tabela
CREATE OR REPLACE FUNCTION has_column(
  table_name text,
  column_name text
) RETURNS boolean AS $$
DECLARE
  result boolean;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = $1
    AND column_name = $2
  ) INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
