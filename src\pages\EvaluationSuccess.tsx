import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle } from "lucide-react";

export default function EvaluationSuccess() {
  return (
    <div className="container max-w-md py-10">
      <Card>
        <CardHeader>
          <div className="flex justify-center mb-4">
            <CheckCircle className="h-16 w-16 text-green-500" />
          </div>
          <CardTitle className="text-center">Cadastro Realizado com Sucesso!</CardTitle>
          <CardDescription className="text-center">
            Seus dados foram enviados para pré cadastro.
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <p className="mb-4">
            Agradecemos pelo seu interesse em participar do nosso processo de pré cadastro.
          </p>
          <div className="bg-blue-50 p-4 rounded-md mb-4 text-left">
            <h3 className="font-medium text-blue-800 mb-2">Próximos passos:</h3>
            <ol className="list-decimal pl-5 space-y-2 text-blue-800">
              <li>Nossa equipe irá analisar seus documentos</li>
              <li>Você receberá um email informando se os documentos foram aprovados ou se precisam de correções</li>
              <li>Após a aprovação dos documentos, enviaremos o agendamento do seu pré cadastro</li>
              <li>Compareça ao pré cadastro na data e local informados</li>
              <li>Após o pré cadastro, você será notificado sobre o resultado</li>
            </ol>
          </div>
          <p className="mb-4">
            Fique atento ao seu email, pois todas as comunicações serão enviadas para o endereço que você cadastrou.
          </p>
          <p>
            Em caso de dúvidas, entre em contato com o clube.
          </p>
        </CardContent>
        <CardFooter className="flex justify-center">
          <Button onClick={() => window.location.href = "/"}>
            Voltar para a Página Inicial
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
