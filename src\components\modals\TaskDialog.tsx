
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import { useTasksStore } from "@/store/useTasksStore";
import type { AgendaEvent } from "@/api/api";

interface TaskDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  task?: Omit<AgendaEvent, "date" | "endTime"> & { date: Date; endTime: Date };
  clubId: number;
}

export function TaskDialog({ open, onOpenChange, task, clubId }: TaskDialogProps) {
  const [title, setTitle] = useState(task?.title || "");
  const [description, setDescription] = useState(task?.description || "");
  const [error, setError] = useState("");
  const { addTask, loading } = useTasksStore();

  const handleSave = async () => {
    if (!title.trim()) {
      setError("O título da tarefa é obrigatório.");
      return;
    }
    setError("");
    await addTask(clubId, {
      title,
      description,
      status: "pendente",
      club_id: clubId,
    });
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Adicionar/Editar Tarefa</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <Input placeholder="Título*" value={title} onChange={e => setTitle(e.target.value)} />
          <Input placeholder="Descrição" value={description} onChange={e => setDescription(e.target.value)} />
          {error && <div className="text-red-500 text-sm mt-1">{error}</div>}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancelar</Button>
          <Button onClick={handleSave} disabled={!title.trim() || loading}>Salvar</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
