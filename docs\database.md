# Documentação do Banco de Dados

Esta seção detalha o modelo de dados, tabelas, campos e relacionamentos principais do projeto, conforme o Supabase real.

## Sumá<PERSON> de Tabelas
- [players](#players)
- [matches](#matches)
- [gols](#gols)
- [training_goals](#training_goals)
- [seasons](#seasons)
- [exercises](#exercises)
- [club_members](#club_members)
- [staff](#staff)
- [contracts](#contracts)
- [salaries](#salaries)
- [agenda](#agenda)
- [medical_records](#medical_records)
- [auth.users](#authusers)

---

## <a name="players"></a>players
**Responsabilidade:** Cadastro de jogadores do clube.

| Coluna     | Tipo    | Default | Null | Descrição |
|------------|---------|---------|------|-----------|
| id         | uuid    |         | Não  | Identificador único do jogador |
| name       | text    |         | Não  | Nome do jogador |
| status     | text    | 'disponivel' | Não | Status do jogador (disponivel, lesionado, suspenso) |
| club_id    | uuid    |         | Não  | Clube ao qual o jogador pertence |
| ...        | ...     |         |      | Outras colunas relevantes |

**Relacionamentos:**
- club_id → clubs(id)
- Usado em: matches, gols, medical_records, etc.

---

## <a name="matches"></a>matches
**Responsabilidade:** Cadastro de partidas do clube.

| Coluna        | Tipo    | Default | Null | Descrição |
|---------------|---------|---------|------|-----------|
| id            | uuid    |         | Não  | Identificador único da partida |
| club_id       | uuid    |         | Não  | Clube mandante |
| date          | date    |         | Não  | Data da partida |
| opponent      | text    |         | Não  | Adversário |
| score_home    | int     | 0       | Não  | Gols do clube mandante |
| score_away    | int     | 0       | Não  | Gols do adversário |
| season_id     | uuid    |         | Não  | Temporada |
| escalacao     | text[]  |         | Não  | Array de IDs dos jogadores escalados |
| formation     | text    | '4-4-2' | Não  | Formação tática |
| ida_volta     | boolean | false   | Não  | Indica se é partida de ida/volta |
| ...           | ...     |         |      | Outras colunas relevantes |

**Relacionamentos:**
- club_id → clubs(id)
- season_id → seasons(id)
- Usado em: gols, training_exercises, etc.

---

## <a name="gols"></a>gols
**Responsabilidade:** Registro dos gols feitos em cada partida.

| Coluna    | Tipo    | Default | Null | Descrição |
|-----------|---------|---------|------|-----------|
| id        | uuid    |         | Não  | Identificador único do gol |
| match_id  | uuid    |         | Não  | Partida em que o gol foi marcado |
| player_id | uuid    |         | Não  | Jogador que marcou |
| minute    | int     |         | Sim  | Minuto do gol |

**Relacionamentos:**
- match_id → matches(id)
- player_id → players(id)

---

## <a name="training_goals"></a>training_goals
**Responsabilidade:** Objetivos semanais de treinamento.

| Coluna        | Tipo      | Default | Null | Descrição |
|---------------|-----------|---------|------|-----------|
| id            | uuid      |         | Não  | Identificador único do objetivo |
| club_id       | uuid      |         | Não  | Clube vinculado |
| name          | text      |         | Não  | Nome do objetivo |
| type          | text      |         | Não  | Tipo (tático, técnico, físico) |
| current_value | integer   | 0       | Sim  | Progresso atual |
| target_value  | integer   |         | Não  | Meta do objetivo |
| description   | text      |         | Sim  | Descrição detalhada |
| created_at    | timestamptz | now() | Sim  | Data de criação |

**Relacionamentos:**
- club_id → clubs(id)

---

## <a name="seasons"></a>seasons
**Responsabilidade:** Cadastro de temporadas do clube.

| Coluna     | Tipo    | Default | Null | Descrição |
|------------|---------|---------|------|-----------|
| id         | uuid    |         | Não  | Identificador único da temporada |
| club_id    | uuid    |         | Não  | Clube vinculado |
| name       | text    |         | Não  | Nome da temporada |
| start_date | date    |         | Não  | Data de início |
| end_date   | date    |         | Não  | Data de término |

**Relacionamentos:**
- club_id → clubs(id)

---

## <a name="exercises"></a>exercises
**Responsabilidade:** Banco de exercícios disponíveis para treinamentos.

| Coluna      | Tipo    | Default | Null | Descrição |
|-------------|---------|---------|------|-----------|
| id          | uuid    |         | Não  | Identificador único do exercício |
| club_id     | uuid    |         | Não  | Clube vinculado |
| name        | text    |         | Não  | Nome do exercício |
| category    | text    |         | Não  | Categoria do exercício |
| difficulty  | text    |         | Não  | Grau de dificuldade |
| description | text    |         | Sim  | Descrição detalhada |

**Relacionamentos:**
- club_id → clubs(id)

---

## <a name="club_members"></a>club_members
**Responsabilidade:** Associação de usuários a clubes.

| Coluna   | Tipo    | Default | Null | Descrição |
|----------|---------|---------|------|-----------|
| id       | uuid    |         | Não  | Identificador único da associação |
| club_id  | uuid    |         | Não  | Clube |
| user_id  | uuid    |         | Não  | Usuário (auth.users) |

**Relacionamentos:**
- club_id → clubs(id)
- user_id → auth.users(id)

---

## <a name="authusers"></a>auth.users
**Responsabilidade:** Usuários autenticados do sistema (Supabase Auth).

| Coluna     | Tipo    | Default | Null | Descrição |
|------------|---------|---------|------|-----------|
| id         | uuid    |         | Não  | Identificador único do usuário |
| ...        | ...     |         |      | Outras colunas de autenticação |

**Relacionamentos:**
- Usado em: club_members, permissões, etc.

---

## Observações Gerais
- Todas as tabelas possuem controle de RLS (Row Level Security) ativado.
- Migrações são versionadas via Supabase/MCP.
- Índices e constraints seguem padrões de integridade referencial.

> Para detalhes completos de cada coluna, consulte o Supabase Studio ou a migration SQL.
