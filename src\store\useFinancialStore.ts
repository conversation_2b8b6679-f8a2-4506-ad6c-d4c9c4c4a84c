import { create } from "zustand";
import { FinancialTransaction } from "../api/api";
import { getFinancialTransactions, createFinancialTransaction, updateFinancialTransaction, deleteFinancialTransaction, markTransactionAsPaid } from "../api/api";

interface FinancialState {
  transactions: FinancialTransaction[];
  loading: boolean;
  error: string | null;
  fetchTransactions: (clubId: number) => Promise<void>;
  addTransaction: (clubId: number, tx: Omit<FinancialTransaction, "id">) => Promise<void>;
  updateTransaction: (clubId: number, id: number, tx: Partial<FinancialTransaction>) => Promise<void>;
  deleteTransaction: (clubId: number, id: number) => Promise<void>;
  markAsPaid: (clubId: number, id: number) => Promise<void>;
}

export const useFinancialStore = create<FinancialState>((set) => ({
  transactions: [],
  loading: false,
  error: null,

  fetchTransactions: async (clubId: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const transactions = await getFinancialTransactions(clubId);
      console.log("Transações carregadas no store:", transactions);
      set({ transactions, loading: false });
    } catch (err: unknown) {
      console.error("Erro ao buscar transações:", err);
      set({ error: err instanceof Error ? err.message : "Erro ao buscar transações", loading: false });
    }
  },

  addTransaction: async (clubId: number, transaction: Omit<FinancialTransaction, "id">): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const newTransaction = await createFinancialTransaction(clubId, transaction);
      set((state) => ({ transactions: [...state.transactions, newTransaction], loading: false }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao adicionar transação", loading: false });
    }
  },

  updateTransaction: async (clubId: number, id: number, transaction: Partial<FinancialTransaction>): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const updated = await updateFinancialTransaction(clubId, id, transaction);
      if (updated) {
        set((state) => ({ transactions: state.transactions.map(t => t.id === id ? updated : t), loading: false }));
      } else {
        set({ error: "Transação não encontrada", loading: false });
      }
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao atualizar transação", loading: false });
    }
  },

  deleteTransaction: async (clubId: number, id: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const ok = await deleteFinancialTransaction(clubId, id);
      if (ok) {
        set((state) => ({ transactions: state.transactions.filter(t => t.id !== id), loading: false }));
      } else {
        set({ error: "Transação não encontrada", loading: false });
      }
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao deletar transação", loading: false });
    }
  },

  markAsPaid: async (clubId: number, id: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const updated = await markTransactionAsPaid(clubId, id);
      if (updated) {
        set((state) => ({ transactions: state.transactions.map(t => t.id === id ? updated : t), loading: false }));
      } else {
        set({ error: "Transação não encontrada", loading: false });
      }
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao marcar transação como paga", loading: false });
    }
  },
}));
