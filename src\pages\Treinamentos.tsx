import { useState, useEffect } from 'react';
import {
  Calendar,
  ClipboardCheck,
  PlaySquare,
  Flame,
  TrendingUp,
  Users,
  Clock,
  BarChart2,
  ListChecks,
  Filter,
  Plus,
  Search,
  ChevronRight,
  Download,
  Pencil,
  Trash2,
  Minus,
  Edit,
  Tag
} from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Progress } from '@/components/ui/progress';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { NovoTreinoDialog } from '@/components/modals/NovoTreinoDialog';
import { FinalizarTreinoDialog } from '@/components/modals/FinalizarTreinoDialog';
import { AddExerciseToTrainingDialog } from '@/components/modals/AddExerciseToTrainingDialog';
import { TrainingDetailsDialog } from '@/components/modals/TrainingDetailsDialog';
import { TrainingEditDialog } from '@/components/modals/TrainingEditDialog';
import { TrainingExercisesSummary } from '@/components/training/TrainingExercisesSummary';
import { NovoExercicioDialog } from '@/components/modals/NovoExercicioDialog';
import { NovoObjetivoDialog } from '@/components/modals/NovoObjetivoDialog';
import { TrainingReportDialog } from '@/components/modals/TrainingReportDialog';
import { generateTrainingReport, type TrainingReportOptions, type TrainingReportData } from '@/utils/trainingReportGenerator';
import { useTrainingsStore } from "@/store/useTrainingsStore";
import { useCurrentClubId } from "@/context/ClubContext";
import { endOfWeek, startOfWeek, isWithinInterval, parseISO } from 'date-fns';
import { getPhysicalProgress, getTrainingGoals, getExercises, addExerciseToTraining, createExercise, createTrainingGoal, updateTrainingGoal, deleteTrainingGoal } from "@/api";
import { useClubInfoStore } from '@/store/useClubInfoStore';
import { useToast } from '@/components/ui/use-toast';
import type { PhysicalProgress, TrainingGoal, Exercise, Training } from "@/api/api";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

// Componente principal
export default function Treinamentos() {
  const [activeTab, setActiveTab] = useState("agenda");
  const [novoTreinoDialogOpen, setNovoTreinoDialogOpen] = useState(false);
  const [finalizarDialogOpen, setFinalizarDialogOpen] = useState(false);
  const [treinoParaFinalizar, setTreinoParaFinalizar] = useState<number | null>(null);
  // Zustand store
  const {
    trainings,
    completedTrainings,
    loading,
    completedLoading,
    error,
    fetchTrainings,
    fetchCompletedTrainings,
    addTraining,
    updateTraining,
    deleteTraining,
    finalizeTraining,
    syncTrainings
  } = useTrainingsStore();
  const clubId = useCurrentClubId();
  const { clubInfo, fetchClubInfo } = useClubInfoStore();
  const { toast } = useToast();

  // Estado para filtros de treinos concluídos
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [selectedMonth, setSelectedMonth] = useState<number | undefined>(undefined);

  useEffect(() => {
    fetchTrainings(clubId);
  }, [fetchTrainings, clubId]);

  // Buscar treinos concluídos quando a tab for ativada ou filtros mudarem
  useEffect(() => {
    if (activeTab === "concluidos") {
      fetchCompletedTrainings(clubId, selectedYear, selectedMonth);
    }
  }, [activeTab, selectedYear, selectedMonth, fetchCompletedTrainings, clubId]);

  // State for physical progress, training goals, and exercises
  const [physicalProgress, setPhysicalProgress] = useState<PhysicalProgress[]>([]);
  const [physicalLoading, setPhysicalLoading] = useState(true);
  const [physicalError, setPhysicalError] = useState<string | null>(null);
  const [trainingGoals, setTrainingGoals] = useState<TrainingGoal[]>([]);
  const [goalsLoading, setGoalsLoading] = useState(true);
  const [goalsError, setGoalsError] = useState<string | null>(null);
  // Exercícios
  const [exercises, setExercises] = useState<Exercise[]>([]);
  const [exercisesLoading, setExercisesLoading] = useState(true);
  const [exercisesError, setExercisesError] = useState<string | null>(null);
  const [exerciseSearch, setExerciseSearch] = useState("");
  const [selectedExercise, setSelectedExercise] = useState<Exercise | null>(null);

  useEffect(() => {
    if (!clubId) return;
    setExercisesLoading(true);
    setExercisesError(null);
    getExercises(clubId)
      .then(setExercises)
      .catch((e) => setExercisesError(e.message))
      .finally(() => setExercisesLoading(false));
  }, [clubId]);

  useEffect(() => {
    if (!clubId) return;
    setPhysicalLoading(true);
    setPhysicalError(null);
    getPhysicalProgress(clubId)
      .then(setPhysicalProgress)
      .catch((e) => setPhysicalError(e.message))
      .finally(() => setPhysicalLoading(false));
  }, [clubId]);

  useEffect(() => {
    if (!clubId) return;
    setGoalsLoading(true);
    setGoalsError(null);
    getTrainingGoals(clubId)
      .then(setTrainingGoals)
      .catch((e) => setGoalsError(e.message))
      .finally(() => setGoalsLoading(false));
  }, [clubId]);

  // Buscar informações do clube
  useEffect(() => {
    if (clubId) {
      fetchClubInfo(clubId);
    }
  }, [clubId, fetchClubInfo]);

  // State for Add Exercise modal
  const [addExerciseDialogOpen, setAddExerciseDialogOpen] = useState(false);
  const [selectedTrainingId, setSelectedTrainingId] = useState<number | null>(null);
  const [addExerciseLoading, setAddExerciseLoading] = useState(false);

  // State para modal de detalhes do treino
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [detailsTraining, setDetailsTraining] = useState<Training | null>(null);

  // State para modal de edição de treino
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [trainingToEdit, setTrainingToEdit] = useState<Training | null>(null);

  // State para modal de novo exercício
  const [novoExercicioDialogOpen, setNovoExercicioDialogOpen] = useState(false);
  const [novoExercicioLoading, setNovoExercicioLoading] = useState(false);

  // State para modal de objetivo
  const [novoObjetivoDialogOpen, setNovoObjetivoDialogOpen] = useState(false);
  const [novoObjetivoLoading, setNovoObjetivoLoading] = useState(false);

  // State para edição de objetivo
  const [editObjetivoDialogOpen, setEditObjetivoDialogOpen] = useState(false);
  const [objetivoParaEditar, setObjetivoParaEditar] = useState<TrainingGoal | null>(null);

  // Estados para o relatório
  const [reportDialogOpen, setReportDialogOpen] = useState(false);
  const [reportLoading, setReportLoading] = useState(false);
  const [objetivoLoading, setObjetivoLoading] = useState(false);

  // Handler para adicionar exercício ao treino
  async function handleAddExerciseToTraining(exercise: Exercise, notes?: string) {
    if (!selectedTrainingId) return;
    setAddExerciseLoading(true);
    try {
      await addExerciseToTraining(selectedTrainingId, exercise.id, undefined, notes);
      // TODO: Atualizar lista de exercícios do treino, se exibida
      setAddExerciseDialogOpen(false);
      setSelectedTrainingId(null);
    } catch (e) {
      // O modal já exibe erro
    } finally {
      setAddExerciseLoading(false);
    }
  }

  // Ajusta o tipo do parâmetro para aceitar apenas os campos válidos para a tabela exercises
  async function handleCreateExercise(exercise: { name: string; description?: string; category: string; difficulty: string }) {
    if (!clubId) return;
    setNovoExercicioLoading(true);
    try {
      await createExercise(clubId, exercise);
      setNovoExercicioDialogOpen(false);
      setExercises(await getExercises(clubId));
    } catch (e) {
      // O modal já exibe erro
    } finally {
      setNovoExercicioLoading(false);
    }
  }

  // Handler para salvar objetivo
  const handleSalvarObjetivo = async (goal) => {
    setNovoObjetivoLoading(true);
    try {
      await createTrainingGoal(clubId, goal);
      // Refaz a busca após salvar
      const goals = await getTrainingGoals(clubId);
      setTrainingGoals(goals);
    } catch (e) {
      // Você pode exibir um toast de erro aqui
    } finally {
      setNovoObjetivoLoading(false);
    }
  };

  // Handler para abrir modal de edição
  const handleEditarObjetivo = (goal: TrainingGoal) => {
    setObjetivoParaEditar(goal);
    setEditObjetivoDialogOpen(true);
  };

  // Handler para salvar edição
  const handleSalvarEdicaoObjetivo = async (goalEdit: any) => {
    if (!objetivoParaEditar) return;
    setObjetivoLoading(true);
    try {
      await updateTrainingGoal(clubId, objetivoParaEditar.id, goalEdit);
      const goals = await getTrainingGoals(clubId);
      setTrainingGoals(goals);
      setEditObjetivoDialogOpen(false);
      setObjetivoParaEditar(null);
    } catch (e) {
      // Exibir toast de erro se quiser
    } finally {
      setObjetivoLoading(false);
    }
  };

  // Handler para excluir objetivo
  const handleExcluirObjetivo = async (goal: TrainingGoal) => {
    if (!window.confirm(`Deseja realmente excluir o objetivo "${goal.name}"?`)) return;
    setObjetivoLoading(true);
    try {
      await deleteTrainingGoal(clubId, goal.id);
      setTrainingGoals(await getTrainingGoals(clubId));
    } catch (e) {
      // Exibir toast de erro se quiser
    } finally {
      setObjetivoLoading(false);
    }
  };

  // Handler para atualizar progresso rápido
  const handleAtualizarProgresso = async (goal: TrainingGoal, delta: number) => {
    const novoValor = Math.max(0, Math.min(goal.target_value, goal.current_value + delta));
    if (novoValor === goal.current_value) return;
    setObjetivoLoading(true);
    try {
      await updateTrainingGoal(clubId, goal.id, { current_value: novoValor });
      setTrainingGoals(await getTrainingGoals(clubId));
    } catch (e) {
      // Exibir toast de erro se quiser
    } finally {
      setObjetivoLoading(false);
    }
  };

  // Handler para gerar relatório de treinamentos
  const handleGenerateTrainingReport = async (options: TrainingReportOptions) => {
    if (!clubInfo) {
      toast({
        title: 'Erro',
        description: 'Informações do clube não carregadas.',
        variant: 'destructive',
      });
      return;
    }

    setReportLoading(true);
    try {
      // Buscar dados necessários
      const [allTrainings, allCompletedTrainings, allExercises] = await Promise.all([
        syncTrainings(clubId).then(() => trainings),
        fetchCompletedTrainings(clubId, options.period?.year, options.period?.month).then(() => completedTrainings),
        getExercises(clubId)
      ]);

      // Calcular estatísticas
      const statistics = {
        totalTrainings: allTrainings.length + allCompletedTrainings.length,
        completedTrainings: allCompletedTrainings.length,
        upcomingTrainings: allTrainings.filter(t => t.status !== 'concluído').length,
        typeDistribution: {},
        categoryDistribution: {}
      };

      // Distribuição por tipo
      [...allTrainings, ...allCompletedTrainings].forEach(training => {
        const type = training.type || 'outros';
        statistics.typeDistribution[type] = (statistics.typeDistribution[type] || 0) + 1;
      });

      // Distribuição por categoria
      [...allTrainings, ...allCompletedTrainings].forEach(training => {
        const category = training.category_name || 'Sem categoria';
        statistics.categoryDistribution[category] = (statistics.categoryDistribution[category] || 0) + 1;
      });

      const reportData: TrainingReportData = {
        trainings: allTrainings,
        completedTrainings: allCompletedTrainings,
        goals: trainingGoals,
        exercises: allExercises,
        statistics
      };

      await generateTrainingReport(reportData, clubInfo, options);

      toast({
        title: 'Relatório gerado com sucesso',
        description: 'O relatório de treinamentos foi baixado para o seu computador.',
      });
    } catch (error) {
      console.error('Erro ao gerar relatório:', error);
      toast({
        title: 'Erro ao gerar relatório',
        description: 'Ocorreu um erro ao gerar o relatório. Tente novamente.',
        variant: 'destructive',
      });
    } finally {
      setReportLoading(false);
    }
  };

  // Função para obter a cor do badge de status
  const getStatusColor = (status: string) => {
    switch (status) {
      case "concluído":
        return "bg-green-50 text-green-700 border-green-200";
      case "em andamento":
        return "bg-primary/10 text-primary border-primary/20";
      case "agendado":
        return "bg-gray-50 text-gray-700 border-gray-200";
      default:
        return "";
    }
  };

  // Handler para abrir modal de finalização
  const handleOpenFinalizar = (id: number) => {
    setTreinoParaFinalizar(id);
    setFinalizarDialogOpen(true);
  };

  // Handler para finalizar treino
  const handleFinalizarTreino = async (summary: string, description: string, clubId: number) => {
    if (treinoParaFinalizar == null) return;
    await finalizeTraining(clubId, treinoParaFinalizar, summary, description);
    await syncTrainings(clubId);
    setFinalizarDialogOpen(false);
    setTreinoParaFinalizar(null);
  };

  // Handler para criar novo treino
  async function handleAddTraining(training: Omit<Training, "id">) {
    try {
      await addTraining(clubId, training);
      await syncTrainings(clubId);
      setNovoTreinoDialogOpen(false);
    } catch (error) {
      console.error("Erro ao criar treino:", error);
      // O erro já é tratado no store, apenas logamos aqui
    }
  }

  // Handler para atualizar treino
  async function handleUpdateTraining(id: number, updates: Partial<Training>) {
    await updateTraining(clubId, id, updates);
    await syncTrainings(clubId);
  }

  // Handler para excluir treino
  async function handleDeleteTraining(id: number) {
    await deleteTraining(clubId, id);
    await syncTrainings(clubId);
  }

  // Handler para excluir treino concluído
  async function handleDeleteCompletedTraining(id: number) {
    if (!window.confirm("Tem certeza que deseja excluir este treino concluído? Esta ação não pode ser desfeita.")) {
      return;
    }

    try {
      await deleteTraining(clubId, id);
      // Recarregar a lista de treinos concluídos
      await fetchCompletedTrainings(clubId, selectedYear, selectedMonth);
    } catch (error) {
      console.error("Erro ao excluir treino concluído:", error);
      // Aqui você pode adicionar um toast de erro se quiser
    }
  }

  // Cálculo do resumo da semana
  const today = new Date();
  const weekStart = startOfWeek(today, { weekStartsOn: 1 }); // segunda
  const weekEnd = endOfWeek(today, { weekStartsOn: 1 }); // domingo
  const trainingsThisWeek = trainings.filter(t => {
    if (!t.date) return false;
    const date = parseISO(t.date);
    return isWithinInterval(date, { start: weekStart, end: weekEnd });
  });
  const completedTrainingsThisWeek = trainingsThisWeek.filter(t => t.status === "concluído");
  const totalTrainings = trainingsThisWeek.length;
  const totalCompleted = completedTrainingsThisWeek.length;
  const totalParticipants = trainingsThisWeek.reduce((sum, t) => sum + (t.participants || 0), 0);
  const avgParticipants = totalTrainings > 0 ? Math.round(totalParticipants / totalTrainings) : 0;
  // Se houver total de jogadores disponível, pode calcular % de presença média
  // const totalPlayers = ...
  // const avgAttendance = totalPlayers ? Math.round((avgParticipants / totalPlayers) * 100) : null;
  // Foco da semana: tipo mais frequente
  const typeCount: Record<string, number> = {};
  completedTrainingsThisWeek.forEach(t => {
    if (t.type) typeCount[t.type] = (typeCount[t.type] || 0) + 1;
  });
  const focusType = Object.entries(typeCount).sort((a, b) => b[1] - a[1])[0]?.[0] || null;

  // Cálculo do próximo treino agendado
  const nextTraining = trainings
    .filter(t => t.status === 'agendado' && parseISO(t.date) >= today)
    .sort((a, b) => parseISO(a.date).getTime() - parseISO(b.date).getTime())[0] || null;

  // Tipos de treino da semana (apenas concluídos)
  const typeList = Object.keys(typeCount);
  const typeColors: Record<string, string> = {
    'tático': 'bg-primary',
    'técnico': 'bg-green-500',
    'físico': 'bg-amber-500',
    // Adicione mais tipos e cores conforme necessário
  };

  return (
    <div className="space-y-6">
      {/* Cabeçalho da página */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Treinamento e Desempenho</h1>
        <p className="text-muted-foreground">
          Planejamento de treinos, banco de exercícios e monitoramento de desempenho
        </p>
      </div>

      {/* Card de resumo */}
      <Card className="bg-gradient-to-r from-secondary to-primary border-none text-white">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2">
              <h3 className="text-lg font-medium mb-2">Resumo da Semana</h3>
              <div className="flex flex-wrap gap-4 mb-4">
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>{totalTrainings} treinos programados</span>
                </div>
                <div className="flex items-center gap-1">
                  <ClipboardCheck className="h-4 w-4" />
                  <span>{totalCompleted} treinos concluídos</span>
                </div>
                <div className="flex items-center gap-1">
                  <Flame className="h-4 w-4" />
                  <span>{avgParticipants} participantes em média</span>
                </div>
              </div>
              <p className="text-sm opacity-90 mb-4">
                {focusType ? `Foco da semana: treinos do tipo ${focusType}` : 'Semana de treinamentos'}
              </p>
              <div className="flex gap-2">
                <Button className="bg-white text-team-blue hover:bg-white/90" onClick={() => setNovoTreinoDialogOpen(true)}>
                  Adicionar Novo Treino
                </Button>
                <Button
                  variant="outline"
                  className="text-black border-white/40 hover:bg-white/20 hover:border-white/60"
                  onClick={() => setReportDialogOpen(true)}
                >
                  Ver Relatório Completo
                </Button>
              </div>
            </div>

          </div>
        </CardContent>
      </Card>

      {/* Estatísticas e gráficos */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Tipos de Treinamento</CardTitle>
            <CardDescription>Distribuição dos treinos concluídos desta semana</CardDescription>
          </CardHeader>
          <CardContent className="pt-2">
            {typeList.length === 0 ? (
              <div className="text-muted-foreground text-sm">Nenhum treino concluído nesta semana.</div>
            ) : (
              typeList.map((type, idx) => {
                const value = totalCompleted > 0 ? Math.round((typeCount[type] / totalCompleted) * 100) : 0;
                return (
                  <div className={idx > 0 ? 'pt-4' : ''} key={type}>
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <div className={`h-3 w-3 rounded-full ${typeColors[type] || 'bg-gray-400'} mr-2`}></div>
                        <span className="text-sm capitalize">{type}</span>
                      </div>
                      <span className="text-sm font-medium">{typeCount[type]} treinos</span>
                    </div>
                    <Progress value={value} className="h-2 bg-muted" />
                  </div>
                );
              })
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Objetivos da Semana</CardTitle>
            <CardDescription>Metas de treinamento e progresso</CardDescription>
            <Button size="sm" className="mt-2" onClick={() => setNovoObjetivoDialogOpen(true)}>
              <Plus className="w-4 h-4 mr-1" /> Novo Objetivo
            </Button>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {goalsLoading ? (
                <div className="text-center text-muted-foreground">Carregando objetivos...</div>
              ) : goalsError ? (
                <div className="text-center text-red-500">{goalsError}</div>
              ) : trainingGoals.length === 0 ? (
                <div className="text-center text-muted-foreground">Nenhum objetivo cadastrado.</div>
              ) : (
                trainingGoals.map((goal) => (
                  <div key={goal.id} className="group border rounded p-3 hover:bg-muted/30 transition flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <div className={`h-2 w-2 rounded-full ${goal.type === 'tático' ? 'bg-team-blue' : goal.type === 'técnico' ? 'bg-green-500' : goal.type === 'físico' ? 'bg-amber-500' : 'bg-gray-400'}`}></div>
                        <span className="text-sm font-medium">{goal.name}</span>
                        <span className="text-xs text-muted-foreground">({goal.type})</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="icon" className="h-6 w-6 p-0" onClick={() => handleAtualizarProgresso(goal, -1)} disabled={objetivoLoading || goal.current_value <= 0} title="Diminuir">
                          <Minus className="w-4 h-4" />
                        </Button>
                        <Progress value={goal.current_value / goal.target_value * 100} className="h-2 flex-1" />
                        <Button variant="ghost" size="icon" className="h-6 w-6 p-0" onClick={() => handleAtualizarProgresso(goal, 1)} disabled={objetivoLoading || goal.current_value >= goal.target_value} title="Aumentar">
                          <Plus className="w-4 h-4" />
                        </Button>
                        <span className="text-xs ml-2 text-muted-foreground">{goal.current_value} / {goal.target_value} ({Math.round(goal.current_value / goal.target_value * 100)}%)</span>
                      </div>
                      {goal.description && (
                        <div className="text-xs text-muted-foreground mt-1">{goal.description}</div>
                      )}
                    </div>
                    <div className="flex gap-2 ml-4 opacity-0 group-hover:opacity-100 transition">
                      <Button variant="outline" size="icon" className="h-7 w-7" onClick={() => handleEditarObjetivo(goal)} title="Editar">
                        <Pencil className="w-4 h-4" />
                      </Button>
                      <Button variant="destructive" size="icon" className="h-7 w-7" onClick={() => handleExcluirObjetivo(goal)} title="Excluir">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>

      </div>

      {/* Tabs para diferentes seções */}
      <Tabs defaultValue="agenda" onValueChange={setActiveTab} value={activeTab}>
        <div className="flex items-center justify-between mb-4">
          <TabsList>
            <TabsTrigger value="agenda">Agenda de Treinos</TabsTrigger>
            <TabsTrigger value="concluidos">Concluídos</TabsTrigger>
            <TabsTrigger value="exercicios">Banco de Exercícios</TabsTrigger>
            {/* <TabsTrigger value="desempenho">Desempenho Individual</TabsTrigger> */}
          </TabsList>
          <div className="flex justify-end mb-4">
            <Button className="bg-team-blue hover:bg-blue-700" onClick={() => setNovoTreinoDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Novo Treino
            </Button>
          </div>
          <NovoTreinoDialog
            open={novoTreinoDialogOpen}
            onOpenChange={setNovoTreinoDialogOpen}
            clubId={clubId}
          />
          {finalizarDialogOpen && treinoParaFinalizar !== null && (
            <FinalizarTreinoDialog
              open={finalizarDialogOpen}
              onClose={() => setFinalizarDialogOpen(false)}
              onFinalize={handleFinalizarTreino}
              clubId={clubId}
            />
          )}
          <AddExerciseToTrainingDialog
            open={addExerciseDialogOpen}
            onOpenChange={(open) => {
              setAddExerciseDialogOpen(open);
              if (!open) setSelectedTrainingId(null);
            }}
            exercises={exercises}
            onAdd={handleAddExerciseToTraining}
          />
          <TrainingDetailsDialog
            open={detailsDialogOpen}
            onOpenChange={(open) => {
              setDetailsDialogOpen(open);
              if (!open) setDetailsTraining(null);
            }}
            training={detailsTraining}
          />
          <TrainingEditDialog
            open={editDialogOpen}
            onOpenChange={(open) => {
              setEditDialogOpen(open);
              if (!open) setTrainingToEdit(null);
            }}
            training={trainingToEdit}
            clubId={clubId}
          />
          <NovoExercicioDialog
            open={novoExercicioDialogOpen}
            onOpenChange={setNovoExercicioDialogOpen}
            onCreate={handleCreateExercise}
            loading={novoExercicioLoading}
          />
          <NovoObjetivoDialog
            open={novoObjetivoDialogOpen}
            onOpenChange={setNovoObjetivoDialogOpen}
            onSave={handleSalvarObjetivo}
            loading={novoObjetivoLoading}
            initialGoal={null}
          />
          <NovoObjetivoDialog
            open={editObjetivoDialogOpen}
            onOpenChange={(open) => {
              setEditObjetivoDialogOpen(open);
              if (!open) setObjetivoParaEditar(null);
            }}
            onSave={handleSalvarEdicaoObjetivo}
            loading={objetivoLoading}
            initialGoal={objetivoParaEditar}
          />
          <TrainingReportDialog
            open={reportDialogOpen}
            onOpenChange={setReportDialogOpen}
            onGenerate={handleGenerateTrainingReport}
            loading={reportLoading}
          />
        </div>

        {/* Tab: Agenda de Treinos permanece, demais tabs removidas */}
        <TabsContent value="agenda">
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Cronograma de Treinamentos</CardTitle>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="ghost" size="sm" className="h-8 gap-1">
                    <Filter className="h-4 w-4" />
                    Filtrar
                  </Button>
                  <Button variant="ghost" size="sm" className="h-8 gap-1">
                    <Download className="h-4 w-4" />
                    Exportar
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Treino</TableHead>
                    <TableHead>Tipo</TableHead>
                    <TableHead>Categoria</TableHead>
                    <TableHead>Data</TableHead>
                    <TableHead>Horário</TableHead>
                    <TableHead>Local</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Treinador</TableHead>
                    <TableHead>Exercícios</TableHead>
                    <TableHead></TableHead>
                    <TableHead></TableHead>
                    <TableHead></TableHead>
                    <TableHead></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {trainings.map((training) => (
                    <TableRow key={training.id}>
                      <TableCell className="font-medium">
                        {training.name}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className={
                          training.type === "físico" ? "border-amber-200 bg-amber-50 text-amber-700" :
                          training.type === "tático" ? "border-primary/20 bg-primary/10 text-primary" :
                          "border-green-200 bg-green-50 text-green-700"
                        }>
                          {training.type}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {training.category_name ? (
                          <Badge variant="outline" className="border-purple-200 bg-purple-50 text-purple-700">
                            <Tag className="h-3 w-3 mr-1" />
                            {training.category_name}
                          </Badge>
                        ) : (
                          <span className="text-xs text-muted-foreground">Sem categoria</span>
                        )}
                      </TableCell>
                      <TableCell>{training.date}</TableCell>
                      <TableCell>{training.time}</TableCell>
                      <TableCell>{training.location}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className={getStatusColor(training.status)}>
                            {training.status}
                          </Badge>
                          {training.status === "em andamento" && (
                            <span className="text-xs text-muted-foreground">{training.progress}%</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{training.coach}</TableCell>
                      <TableCell>
                        <TrainingExercisesSummary trainingId={training.id} />
                      </TableCell>
                      <TableCell>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setTrainingToEdit(training);
                            setEditDialogOpen(true);
                          }}
                        >
                          <Edit className="w-4 h-4 mr-1" /> Editar
                        </Button>
                      </TableCell>
                      <TableCell>
                        {training.status !== "concluído" && (
                          <Button variant="outline" size="sm" onClick={() => handleOpenFinalizar(training.id)}>
                            Finalizar
                          </Button>
                        )}
                      </TableCell>
                      <TableCell>
                        {training.status !== "concluído" && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setSelectedTrainingId(training.id);
                              setAddExerciseDialogOpen(true);
                            }}
                          >
                            <Plus className="w-4 h-4 mr-1" /> Exercício
                          </Button>
                        )}
                      </TableCell>
                      <TableCell>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setDetailsTraining(training);
                            setDetailsDialogOpen(true);
                          }}
                        >
                          <ListChecks className="w-4 h-4 mr-1" /> Detalhes
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tab: Treinos Concluídos */}
        <TabsContent value="concluidos">
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Treinos Concluídos</CardTitle>
                  <CardDescription>Histórico de treinos finalizados</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Select value={selectedYear.toString()} onValueChange={(value) => setSelectedYear(parseInt(value))}>
                    <SelectTrigger className="w-[120px]">
                      <SelectValue placeholder="Ano" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 5 }, (_, i) => {
                        const year = new Date().getFullYear() - i;
                        return (
                          <SelectItem key={year} value={year.toString()}>
                            {year}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                  <Select value={selectedMonth?.toString() || "all"} onValueChange={(value) => setSelectedMonth(value === "all" ? undefined : parseInt(value))}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="Mês" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todos os meses</SelectItem>
                      <SelectItem value="1">Janeiro</SelectItem>
                      <SelectItem value="2">Fevereiro</SelectItem>
                      <SelectItem value="3">Março</SelectItem>
                      <SelectItem value="4">Abril</SelectItem>
                      <SelectItem value="5">Maio</SelectItem>
                      <SelectItem value="6">Junho</SelectItem>
                      <SelectItem value="7">Julho</SelectItem>
                      <SelectItem value="8">Agosto</SelectItem>
                      <SelectItem value="9">Setembro</SelectItem>
                      <SelectItem value="10">Outubro</SelectItem>
                      <SelectItem value="11">Novembro</SelectItem>
                      <SelectItem value="12">Dezembro</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="ghost" size="sm" className="h-8 gap-1">
                    <Download className="h-4 w-4" />
                    Exportar
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              {completedLoading ? (
                <div className="p-8 text-center text-muted-foreground">
                  Carregando treinos concluídos...
                </div>
              ) : error ? (
                <div className="p-8 text-center text-red-500">
                  {error}
                </div>
              ) : completedTrainings.length === 0 ? (
                <div className="p-8 text-center text-muted-foreground">
                  Nenhum treino concluído encontrado para os filtros selecionados.
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Treino</TableHead>
                      <TableHead>Tipo</TableHead>
                      <TableHead>Categoria</TableHead>
                      <TableHead>Data</TableHead>
                      <TableHead>Horário</TableHead>
                      <TableHead>Local</TableHead>
                      <TableHead>Treinador</TableHead>
                      <TableHead>Participantes</TableHead>
                      <TableHead>Exercícios</TableHead>
                      <TableHead></TableHead>
                      <TableHead></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {completedTrainings.map((training) => (
                      <TableRow key={training.id}>
                        <TableCell className="font-medium">
                          {training.name}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className={
                            training.type === "físico" ? "border-amber-200 bg-amber-50 text-amber-700" :
                            training.type === "tático" ? "border-primary/20 bg-primary/10 text-primary" :
                            "border-green-200 bg-green-50 text-green-700"
                          }>
                            {training.type}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {training.category_name ? (
                            <Badge variant="outline" className="border-purple-200 bg-purple-50 text-purple-700">
                              <Tag className="h-3 w-3 mr-1" />
                              {training.category_name}
                            </Badge>
                          ) : (
                            <span className="text-xs text-muted-foreground">Sem categoria</span>
                          )}
                        </TableCell>
                        <TableCell>{training.date}</TableCell>
                        <TableCell>{training.time}</TableCell>
                        <TableCell>{training.location}</TableCell>
                        <TableCell>{training.coach}</TableCell>
                        <TableCell>{training.participants}</TableCell>
                        <TableCell>
                          <TrainingExercisesSummary trainingId={training.id} />
                        </TableCell>
                        <TableCell>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setDetailsTraining(training);
                              setDetailsDialogOpen(true);
                            }}
                          >
                            <ListChecks className="w-4 h-4 mr-1" /> Detalhes
                          </Button>
                        </TableCell>
                        <TableCell>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleDeleteCompletedTraining(training.id)}
                          >
                            <Trash2 className="w-4 h-4 mr-1" /> Excluir
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tab: Banco de Exercícios */}
        <TabsContent value="exercicios">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-1">
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between mb-2">
                    <CardTitle>Exercícios</CardTitle>
                    <Button size="sm" onClick={() => setNovoExercicioDialogOpen(true)}>
                      <Plus className="w-4 h-4 mr-1" /> Novo Exercício
                    </Button>
                  </div>
                  <div className="flex w-full items-center space-x-2">
                    <Input
                      type="search"
                      placeholder="Buscar exercícios..."
                      className="h-8 flex-1"
                      value={exerciseSearch}
                      onChange={e => setExerciseSearch(e.target.value)}
                    />
                    <Button variant="ghost" size="sm" className="px-3 h-8">
                      <Search className="h-3.5 w-3.5" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="divide-y">
                    {exercisesLoading ? (
                      <div className="p-4 text-center text-muted-foreground">Carregando exercícios...</div>
                    ) : exercisesError ? (
                      <div className="p-4 text-center text-red-500">{exercisesError}</div>
                    ) : (exercises.length === 0 ? (
                      <div className="p-4 text-center text-muted-foreground">Nenhum exercício cadastrado.</div>
                    ) : (
                      exercises
                        .filter(ex => ex.name.toLowerCase().includes(exerciseSearch.toLowerCase()))
                        .map(ex => (
                          <div
                            key={ex.id}
                            className={`p-4 cursor-pointer hover:bg-muted/60 ${selectedExercise?.id === ex.id ? 'bg-muted' : ''}`}
                            onClick={() => setSelectedExercise(ex)}
                          >
                            <div className="font-medium text-sm">{ex.name}</div>
                            <div className="text-xs text-muted-foreground">{ex.category} - {ex.difficulty}</div>
                          </div>
                        ))
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
            <div className="md:col-span-2">
              {selectedExercise ? (
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle>{selectedExercise.name}</CardTitle>
                    <CardDescription>
                      {selectedExercise.category} | {selectedExercise.difficulty}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="mb-2 text-sm">{selectedExercise.description}</div>
                  </CardContent>
                </Card>
              ) : (
                <div className="flex items-center justify-center h-full border rounded-lg p-8 text-center bg-muted/30">
                  <div>
                    <ListChecks className="h-10 w-10 text-muted-foreground mx-auto mb-3" />
                    <h3 className="text-lg font-medium mb-1">Selecione um Exercício</h3>
                    <p className="text-muted-foreground">
                      Clique em um exercício à esquerda para ver detalhes
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
