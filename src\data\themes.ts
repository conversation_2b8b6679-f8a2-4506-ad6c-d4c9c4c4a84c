
export interface TeamTheme {
  id: string;
  name: string;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    text: string;
  };
}

export const teamThemes: Record<string, TeamTheme> = {
  "blue": { 
    id: "blue",
    name: "<PERSON><PERSON><PERSON>", 
    colors: {
      primary: "#1e40af", 
      secondary: "#60a5fa", 
      background: "#ffffff", 
      text: "#1f2937" 
    }
  },
  "red": { 
    id: "red",
    name: "<PERSON><PERSON><PERSON><PERSON>", 
    colors: {
      primary: "#b91c1c", 
      secondary: "#f87171", 
      background: "#ffffff", 
      text: "#1f2937" 
    }
  },
  "green": { 
    id: "green",
    name: "<PERSON>", 
    colors: {
      primary: "#15803d", 
      secondary: "#86efac", 
      background: "#ffffff", 
      text: "#1f2937" 
    }
  },
  "purple": { 
    id: "purple",
    name: "Roxo", 
    colors: {
      primary: "#7e22ce", 
      secondary: "#c084fc", 
      background: "#ffffff", 
      text: "#1f2937" 
    }
  }
};
