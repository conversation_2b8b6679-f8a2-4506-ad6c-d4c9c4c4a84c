import { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Upload, X, FileText } from "lucide-react";

interface FileUploaderProps {
  id: string;
  accept?: string;
  maxSize?: number; // in MB
  onFileSelect: (file: File | null) => void;
  currentFile?: File | null;
  disabled?: boolean;
}

export function FileUploader({
  id,
  accept,
  maxSize = 5, // Default 5MB
  onFileSelect,
  currentFile,
  disabled = false,
}: FileUploaderProps) {
  const [error, setError] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) {
      return;
    }

    const file = files[0];
    validateAndSetFile(file);
  };

  const validateAndSetFile = (file: File) => {
    setError(null);

    // Check file size
    if (maxSize && file.size > maxSize * 1024 * 1024) {
      setError(`O arquivo excede o tamanho máximo de ${maxSize}MB`);
      return;
    }

    // Check file type if accept is provided
    if (accept) {
      const acceptedTypes = accept.split(",").map(type => type.trim());
      const fileType = file.type;
      const fileExtension = `.${file.name.split(".").pop()}`;

      const isAccepted = acceptedTypes.some(type => {
        if (type.startsWith(".")) {
          // Extension check
          return fileExtension.toLowerCase() === type.toLowerCase();
        } else if (type.includes("*")) {
          // Wildcard MIME type check
          const typePattern = type.replace("*", "");
          return fileType.startsWith(typePattern);
        } else {
          // Exact MIME type check
          return fileType === type;
        }
      });

      if (!isAccepted) {
        setError(`Tipo de arquivo não suportado. Tipos aceitos: ${accept}`);
        return;
      }
    }

    onFileSelect(file);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (!files || files.length === 0) {
      return;
    }

    validateAndSetFile(files[0]);
  };

  const handleRemoveFile = () => {
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
    onFileSelect(null);
    setError(null);
  };

  return (
    <div className="space-y-2">
      {!currentFile ? (
        <div
          className={`border-2 border-dashed rounded-md p-4 text-center ${
            isDragging ? "border-primary bg-primary/5" : "border-muted"
          } ${disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}`}
          onDragOver={!disabled ? handleDragOver : undefined}
          onDragLeave={!disabled ? handleDragLeave : undefined}
          onDrop={!disabled ? handleDrop : undefined}
          onClick={() => !disabled && fileInputRef.current?.click()}
        >
          <input
            id={id}
            type="file"
            ref={fileInputRef}
            className="hidden"
            accept={accept}
            onChange={handleFileChange}
            disabled={disabled}
          />
          <div className="flex flex-col items-center justify-center gap-2 py-4">
            <Upload className="h-8 w-8 text-muted-foreground" />
            <div className="space-y-1">
              <p className="text-sm font-medium">
                Arraste e solte ou clique para selecionar
              </p>
              <p className="text-xs text-muted-foreground">
                Tamanho máximo: {maxSize}MB
                {accept && ` • Formatos: ${accept}`}
              </p>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex items-center justify-between border rounded-md p-3">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-muted-foreground" />
            <div className="text-sm truncate max-w-[200px]">
              {currentFile.name}
            </div>
          </div>
          <Button
            type="button"
            variant="ghost"
            size="icon"
            onClick={handleRemoveFile}
            disabled={disabled}
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Remover arquivo</span>
          </Button>
        </div>
      )}
      {error && <p className="text-xs text-destructive">{error}</p>}
    </div>
  );
}
