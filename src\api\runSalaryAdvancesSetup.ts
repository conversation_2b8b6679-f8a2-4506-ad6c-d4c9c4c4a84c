import { supabase } from "@/integrations/supabase/client";
import fs from 'fs';
import path from 'path';

/**
 * Executa o script SQL para criar a tabela de adiantamentos de salário
 * @returns true se o script foi executado com sucesso
 */
export async function runSalaryAdvancesSetup(): Promise<boolean> {
  try {
    console.log("Executando script de configuração de adiantamentos de salário...");

    // Ler o arquivo SQL
    const sqlPath = path.join(process.cwd(), 'sql', 'salary-advances.sql');
    const sqlContent = fs.readFileSync(sqlPath, 'utf8');

    // Executar o script SQL
    const { error } = await supabase.rpc('exec_sql', { sql: sqlContent });

    if (error) {
      console.error("Erro ao executar script SQL:", error);
      return false;
    }

    console.log("Script de configuração de adiantamentos de salário executado com sucesso!");
    return true;
  } catch (error) {
    console.error("Erro ao executar script de configuração:", error);
    return false;
  }
}
