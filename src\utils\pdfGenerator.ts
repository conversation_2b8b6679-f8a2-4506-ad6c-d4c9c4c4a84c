import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { ClubInfo } from '@/api/api';
import autoTable from 'jspdf-autotable';
import { generateClothingReport } from "./clothingReportGeneratorNew";
import { getClubPrimaryColorRgb } from '@/utils/themeUtils';

/**
 * Gera um PDF a partir de um elemento HTML com suporte a múltiplas páginas
 * @param element Elemento HTML a ser convertido em PDF
 * @param filename Nome do arquivo PDF
 * @param options Opções adicionais
 * @param headerElement Elemento HTML opcional para ser usado como cabeçalho em todas as páginas
 */
export async function generatePDF(
  element: HTMLElement,
  filename: string,
  options: {
    format?: 'a4' | 'letter' | 'legal';
    orientation?: 'portrait' | 'landscape';
    margin?: number;
    headerHeight?: number; // Altura do cabeçalho em mm
  } = {},
  headerElement?: HTMLElement
): Promise<void> {
  try {
    const { format = 'a4', orientation = 'portrait', margin = 10, headerHeight = 0 } = options;

    // Criar o PDF
    const pdf = new jsPDF({
      format: format,
      orientation: orientation,
      unit: 'mm',
    });

    // Dimensões do PDF
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = pdf.internal.pageSize.getHeight();

    // Capturar o cabeçalho como uma imagem separada, se fornecido
    let headerCanvas;
    let headerHeightPx = 0;

    if (headerElement) {
      headerCanvas = await html2canvas(headerElement, {
        scale: 3, // Aumentar qualidade
        useCORS: true,
        logging: false,
        backgroundColor: 'white',
      });

      // Calcular a altura do cabeçalho no PDF
      const headerWidthRatio = (pdfWidth - margin * 2) / headerCanvas.width;
      headerHeightPx = headerCanvas.height * headerWidthRatio;
    }

    // Capturar o elemento principal como uma imagem
    // Remover o cabeçalho do elemento principal para evitar duplicação
    const headerInElement = element.querySelector('.report-header');
    let headerDisplay = '';
    if (headerInElement && headerElement) {
      headerDisplay = headerInElement.style.display;
      headerInElement.style.display = 'none';
    }

    const canvas = await html2canvas(element, {
      scale: 3, // Aumentar qualidade
      useCORS: true,
      logging: false,
      backgroundColor: 'white',
    });

    // Restaurar a exibição do cabeçalho no elemento principal
    if (headerInElement && headerElement) {
      headerInElement.style.display = headerDisplay;
    }

    // Dimensões da imagem capturada
    const imgWidth = canvas.width;
    const imgHeight = canvas.height;

    // Calcular a escala para ajustar a imagem à largura do PDF (menos margens)
    const widthRatio = (pdfWidth - margin * 2) / imgWidth;

    // Calcular as dimensões finais
    const finalWidth = imgWidth * widthRatio;
    const finalHeight = imgHeight * widthRatio;

    // Calcular a posição horizontal para centralizar
    const x = margin;

    // Se o conteúdo for maior que a altura da página, dividir em múltiplas páginas
    if (finalHeight > (pdfHeight - margin * 2 - headerHeightPx)) {
      // Altura disponível para conteúdo em cada página (menos margens e cabeçalho)
      const availableHeight = pdfHeight - margin * 2 - headerHeightPx;

      // Calcular quantas páginas serão necessárias
      const totalPages = Math.ceil(finalHeight / availableHeight);

      // Para cada página
      for (let i = 0; i < totalPages; i++) {
        // Adicionar nova página, exceto para a primeira
        if (i > 0) {
          pdf.addPage();
        }

        // Adicionar o cabeçalho, se fornecido
        if (headerCanvas) {
          const headerImgData = headerCanvas.toDataURL('image/png');
          pdf.addImage(
            headerImgData,
            'PNG',
            x,
            margin,
            finalWidth,
            headerHeightPx
          );
        }

        // Calcular a posição vertical de início para esta página
        const yStart = i * availableHeight / widthRatio;
        const yHeight = Math.min(availableHeight / widthRatio, imgHeight - yStart);

        // Adicionar a parte correspondente da imagem
        const imgData = canvas.toDataURL('image/png');
        pdf.addImage(
          imgData,
          'PNG',
          x,
          margin + headerHeightPx,
          finalWidth,
          yHeight * widthRatio,
          '',
          'FAST',
          0,
          yStart,
          imgWidth,
          yHeight
        );

        // Adicionar número da página
        pdf.setFontSize(8);
        pdf.setTextColor(100, 100, 100);
        pdf.text(
          `Página ${i + 1} de ${totalPages}`,
          pdfWidth / 2,
          pdfHeight - margin / 2,
          { align: 'center' }
        );
      }
    } else {
      // Se couber em uma única página, adicionar a imagem inteira
      // Adicionar o cabeçalho, se fornecido
      if (headerCanvas) {
        const headerImgData = headerCanvas.toDataURL('image/png');
        pdf.addImage(
          headerImgData,
          'PNG',
          x,
          margin,
          finalWidth,
          headerHeightPx
        );
      }

      // Adicionar o conteúdo principal
      const imgData = canvas.toDataURL('image/png');
      pdf.addImage(imgData, 'PNG', x, margin + headerHeightPx, finalWidth, finalHeight);
    }

    // Salvar o PDF
    pdf.save(filename);
  } catch (error) {
    console.error('Erro ao gerar PDF:', error);
    throw new Error('Não foi possível gerar o PDF');
  }
}

/**
 * Interface para jsPDF com suporte a autoTable
 */
interface jsPDFWithAutoTable extends jsPDF {
  autoTable: typeof autoTable;
  lastAutoTable: {
    finalY: number;
  };
  setFont(fontName?: string, fontStyle?: string): jsPDF;
  setLineDashPattern(pattern: number[], patternOffset: number): jsPDF;
}

/**
 * Gera um relatório de jogadores em PDF
 * @param players Lista de jogadores
 * @param clubInfo Informações do clube
 * @param filename Nome do arquivo PDF
 */
export async function generatePlayerReport(
  players: any[],
  clubInfo: ClubInfo,
  filename: string = 'relatorio-jogadores.pdf'
): Promise<void> {
  // Criar um novo documento PDF
  const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

  // Configurações de página
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const margin = 10;

  // Adicionar título
  const title = 'Relatório de Jogadores';
  doc.setFontSize(18);
  doc.text(title, pageWidth / 2, 20, { align: 'center' });

  // Adicionar informações do clube
  doc.setFontSize(12);
  doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, margin, 30);

  if (clubInfo.address) {
    doc.text(`Endereço: ${clubInfo.address}`, margin, 35);
  }

  if (clubInfo.phone) {
    doc.text(`Telefone: ${clubInfo.phone}`, margin, 40);
  }

  if (clubInfo.zip_code) {
    doc.text(`CEP: ${clubInfo.zip_code}`, margin, 45);
  }

  // Tentar adicionar o logo do clube, se disponível
  if (clubInfo.logo_url) {
    try {
      const img = new Image();

      // Criar uma promessa para lidar com o carregamento da imagem
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            // Calcular proporção para manter as proporções
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;

            // Adicionar a imagem ao PDF (alinhada à direita)
            doc.addImage(img, 'PNG', pageWidth - margin - imgWidth, 10, imgWidth, imgHeight);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });

      // Iniciar o carregamento da imagem
      img.src = clubInfo.logo_url;

      // Aguardar o carregamento e processamento da imagem
      await loadImage;
    } catch (logoError) {
      console.error("Erro ao adicionar logo ao PDF:", logoError);
    }
  }

  // Adicionar data do relatório (após o logo para garantir que fique acima)
  const currentDate = new Date().toLocaleDateString('pt-BR');
  doc.setFontSize(10);
  doc.text(`Data: ${currentDate}`, pageWidth - margin, 30, { align: 'right' });

  // Posição Y inicial para o conteúdo
  let yPosition = 50;

  // Ordenar jogadores por nome
  const sortedPlayers = [...players].sort((a, b) => a.name.localeCompare(b.name));

  // Preparar dados para a tabela
  const tableData = sortedPlayers.map(player => [
    player.name || '-',
    player.position || '-',
    player.birthdate ? new Date(player.birthdate).toLocaleDateString('pt-BR') : '-',
    player.weight ? `${player.weight} kg` : '-',
    player.height ? `${player.height} cm` : '-',
    player.cpf_number || '-',
    player.registration_number || '-'
  ]);

  // Adicionar a tabela ao PDF
  autoTable(doc, {
    startY: yPosition,
    head: [['Nome', 'Posição', 'Data Nasc.', 'Peso', 'Altura', 'CPF', 'Cadastro']],
    body: tableData,
    theme: 'striped',
    headStyles: { fillColor: [0, 87, 183], textColor: [255, 255, 255] },
    margin: { left: margin, right: margin },
    columnStyles: {
      0: { cellWidth: 'auto' }, // Nome
      1: { cellWidth: 'auto' }, // Posição
      2: { cellWidth: 'auto' }, // Data Nasc.
      3: { cellWidth: 'auto' }, // Peso
      4: { cellWidth: 'auto' }, // Altura
      5: { cellWidth: 'auto' }, // CPF
      6: { cellWidth: 'auto' }  // Cadastro
    },
    didDrawPage: (data) => {
      // Adicionar cabeçalho em cada página
      if (data.pageNumber > 1) {
        doc.setFontSize(10);
        doc.text(title, pageWidth / 2, 10, { align: 'center' });
        doc.setFontSize(8);
        doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, margin, 15);
      }
    }
  });

  // Adicionar rodapé
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.text(
      `© ${new Date().getFullYear()} ${clubInfo.name} - Todos os direitos reservados`,
      pageWidth / 2,
      pageHeight - 10,
      { align: 'center' }
    );
    doc.text(
      `Página ${i} de ${pageCount}`,
      pageWidth - margin,
      pageHeight - 10,
      { align: 'right' }
    );
  }

  // Salvar o PDF
  doc.save(filename);
}

/**
 * Gera um relatório de jogadores por categoria em PDF
 * @param playersByCategory Jogadores agrupados por categoria
 * @param clubInfo Informações do clube
 * @param filename Nome do arquivo PDF
 */
export async function generatePlayersByCategoryReport(
  playersByCategory: Record<string, any[]>,
  clubInfo: ClubInfo,
  filename: string = 'relatorio-jogadores-por-categoria.pdf'
): Promise<void> {
  // Criar um novo documento PDF
  const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

  // Configurações de página
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const margin = 10;

  // Adicionar título
  const title = 'Relatório de Jogadores por Categoria';
  doc.setFontSize(18);
  doc.text(title, pageWidth / 2, 20, { align: 'center' });

  // Adicionar informações do clube
  doc.setFontSize(12);
  doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, margin, 30);

  if (clubInfo.address) {
    doc.text(`Endereço: ${clubInfo.address}`, margin, 35);
  }

  if (clubInfo.phone) {
    doc.text(`Telefone: ${clubInfo.phone}`, margin, 40);
  }

  if (clubInfo.zip_code) {
    doc.text(`CEP: ${clubInfo.zip_code}`, margin, 45);
  }

  // Tentar adicionar o logo do clube, se disponível
  if (clubInfo.logo_url) {
    try {
      const img = new Image();

      // Criar uma promessa para lidar com o carregamento da imagem
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            // Calcular proporção para manter as proporções
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;

            // Adicionar a imagem ao PDF (alinhada à direita)
            doc.addImage(img, 'PNG', pageWidth - margin - imgWidth, 10, imgWidth, imgHeight);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });

      // Iniciar o carregamento da imagem
      img.src = clubInfo.logo_url;

      // Aguardar o carregamento e processamento da imagem
      await loadImage;
    } catch (logoError) {
      console.error("Erro ao adicionar logo ao PDF:", logoError);
    }
  }

  // Adicionar data do relatório (após o logo para garantir que fique acima)
  const currentDate = new Date().toLocaleDateString('pt-BR');
  doc.setFontSize(10);
  doc.text(`Data: ${currentDate}`, pageWidth - margin, 30, { align: 'right' });

  // Posição Y inicial para o conteúdo
  let yPosition = 50;

  // Para cada categoria, mostrar os jogadores
  const categories = Object.keys(playersByCategory).sort();

  if (categories.length === 0) {
    doc.setFontSize(12);
    doc.setTextColor(100, 100, 100);
    doc.text('Nenhuma categoria encontrada.', margin, yPosition);
  } else {
    categories.forEach((category, index) => {
      const players = playersByCategory[category];

      if (players.length === 0) return;

      // Título da categoria
      doc.setFontSize(14);
      doc.setTextColor(0, 0, 0);
      doc.setFillColor(242, 242, 242);
      doc.rect(margin, yPosition, pageWidth - 2 * margin, 8, 'F');
      doc.text(`Categoria: ${category}`, margin + 2, yPosition + 5);
      yPosition += 12;

      // Ordenar jogadores por nome
      const sortedPlayers = [...players].sort((a, b) => a.name.localeCompare(b.name));

      // Preparar dados para a tabela
      const tableData = sortedPlayers.map(player => [
        player.name || '-',
        player.position || '-',
        player.birthdate ? new Date(player.birthdate).toLocaleDateString('pt-BR') : '-',
        player.weight ? `${player.weight} kg` : '-',
        player.height ? `${player.height} cm` : '-',
        player.cpf_number || '-',
        player.registration_number || '-'
      ]);

      // Adicionar a tabela ao PDF
      autoTable(doc, {
        startY: yPosition,
        head: [['Nome', 'Posição', 'Data Nasc.', 'Peso', 'Altura', 'CPF', 'Cadastro']],
        body: tableData,
        theme: 'striped',
        headStyles: { fillColor: [0, 87, 183], textColor: [255, 255, 255] },
        margin: { left: margin, right: margin },
        columnStyles: {
          0: { cellWidth: 'auto' }, // Nome
          1: { cellWidth: 'auto' }, // Posição
          2: { cellWidth: 'auto' }, // Data Nasc.
          3: { cellWidth: 'auto' }, // Peso
          4: { cellWidth: 'auto' }, // Altura
          5: { cellWidth: 'auto' }, // CPF
          6: { cellWidth: 'auto' }  // Cadastro
        },
        didDrawPage: (data) => {
          // Adicionar cabeçalho em cada página
          if (data.pageNumber > 1) {
            doc.setFontSize(10);
            doc.text(title, pageWidth / 2, 10, { align: 'center' });
            doc.setFontSize(8);
            doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, margin, 15);
          }
        }
      });

      // Atualizar a posição Y para o próximo conteúdo
      const docWithTable = doc as jsPDFWithAutoTable;
      yPosition = docWithTable.lastAutoTable.finalY + 15;

      // Adicionar linha divisória entre categorias (exceto a última)
      if (index < categories.length - 1) {
        doc.setDrawColor(200, 200, 200);
        doc.setLineDashPattern([3, 3], 0);
        doc.line(margin, yPosition, pageWidth - margin, yPosition);
        doc.setLineDashPattern([], 0); // Resetar para linha sólida
        yPosition += 10;
      }
    });
  }

  // Adicionar rodapé
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.text(
      `© ${new Date().getFullYear()} ${clubInfo.name} - Todos os direitos reservados`,
      pageWidth / 2,
      pageHeight - 10,
      { align: 'center' }
    );
    doc.text(
      `Página ${i} de ${pageCount}`,
      pageWidth - margin,
      pageHeight - 10,
      { align: 'right' }
    );
  }

  // Salvar o PDF
  doc.save(filename);
}

/**
 * Gera um relatório de Cardápio Semanal em PDF
 * @param weeklyMenu Dados do cardápio semanal
 * @param clubInfo Informações do clube
 * @param filename Nome do arquivo PDF
 */
export async function generateWeeklyMenuReport(
  weeklyMenu: {
    week: string;
    meals: {
      day: string;
      breakfast: string;
      lunch: string;
      snack: string;
      dinner: string;
    }[];
  },
  clubInfo: ClubInfo,
  filename: string = 'cardapio-semanal.pdf'
): Promise<void> {
  // Criar um novo documento PDF
  const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

  // Configurações de página
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const margin = 10;

  // Adicionar título
  const title = 'Cardápio Semanal';
  doc.setFontSize(18);
  doc.text(title, pageWidth / 2, 20, { align: 'center' });

  // Adicionar informações do clube
  doc.setFontSize(12);
  doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, margin, 30);

  // Adicionar semana
  doc.setFontSize(12);
  doc.text(`Semana: ${weeklyMenu.week || 'Atual'}`, margin, 35);

  // Tentar adicionar o logo do clube, se disponível
  if (clubInfo.logo_url) {
    try {
      const img = new Image();

      // Criar uma promessa para lidar com o carregamento da imagem
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            // Calcular proporção para manter as proporções
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;

            // Adicionar a imagem ao PDF (alinhada à direita)
            doc.addImage(img, 'PNG', pageWidth - margin - imgWidth, 10, imgWidth, imgHeight);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });

      // Iniciar o carregamento da imagem
      img.src = clubInfo.logo_url;

      // Aguardar o carregamento e processamento da imagem
      await loadImage;
    } catch (logoError) {
      console.error("Erro ao adicionar logo ao PDF:", logoError);
    }
  }

  // Adicionar data do relatório (após o logo para garantir que fique acima)
  const currentDate = new Date().toLocaleDateString('pt-BR');
  doc.setFontSize(10);
  doc.text(`Data: ${currentDate}`, pageWidth - margin, 30, { align: 'right' });

  // Posição Y inicial para o conteúdo
  let yPosition = 45;

  // Preparar dados para a tabela
  const tableData = weeklyMenu.meals.map(meal => [
    meal.day,
    meal.breakfast,
    meal.lunch,
    meal.snack,
    meal.dinner
  ]);

  // Adicionar a tabela ao PDF
  autoTable(doc, {
    startY: yPosition,
    head: [['Dia', 'Café da Manhã', 'Almoço', 'Lanche', 'Jantar']],
    body: tableData,
    theme: 'grid',
    headStyles: {
      fillColor: getClubPrimaryColorRgb(),
      textColor: [255, 255, 255],
      halign: 'center'
    },
    styles: {
      overflow: 'linebreak',
      cellPadding: 5
    },
    columnStyles: {
      0: { cellWidth: 25, halign: 'center', fontStyle: 'bold' }, // Dia
      1: { cellWidth: 'auto' }, // Café da Manhã
      2: { cellWidth: 'auto' }, // Almoço
      3: { cellWidth: 'auto' }, // Lanche
      4: { cellWidth: 'auto' }  // Jantar
    },
    didDrawPage: (data) => {
      // Adicionar cabeçalho em cada página
      if (data.pageNumber > 1) {
        doc.setFontSize(10);
        doc.text(title, pageWidth / 2, 10, { align: 'center' });
        doc.setFontSize(8);
        doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, margin, 15);
        doc.text(`Semana: ${weeklyMenu.week || 'Atual'}`, margin, 20);
      }
    }
  });

  // Adicionar rodapé
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.text(
      `© ${new Date().getFullYear()} ${clubInfo.name} - Todos os direitos reservados`,
      pageWidth / 2,
      pageHeight - 10,
      { align: 'center' }
    );
    doc.text(
      `Página ${i} de ${pageCount}`,
      pageWidth - margin,
      pageHeight - 10,
      { align: 'right' }
    );
  }

  // Salvar o PDF
  doc.save(filename);
}

/**
 * @param clothingData Dados de rouparia por categoria
 * @param clubInfo Informações do clube
 * @param filename Nome do arquivo PDF
 */
export async function generateClothingControlReport(
  clothingData: {
    category: string;
    players: {
      id: string;
      name: string;
      birthdate?: string;
      shirt_size?: string;
      shorts_size?: string;
      sock_size?: string;
      shoes_size?: string;
      training_shirt_size?: string;
      training_shorts_size?: string;
      jacket_size?: string;
      pants_size?: string;
    }[];
  }[],
  clubInfo: ClubInfo,
  filename: string = 'controle-rouparia.pdf'
): Promise<void> {
  // Criar um novo documento PDF
  const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

  // Configurações de página
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const margin = 10;

  // Adicionar título
  doc.setFontSize(18);
  doc.text(title, pageWidth / 2, 20, { align: 'center' });

  // Adicionar informações do clube
  doc.setFontSize(12);
  doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, margin, 30);

  // Tentar adicionar o logo do clube, se disponível
  if (clubInfo.logo_url) {
    try {
      const img = new Image();

      // Criar uma promessa para lidar com o carregamento da imagem
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            // Calcular proporção para manter as proporções
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;

            // Adicionar a imagem ao PDF (alinhada à direita)
            doc.addImage(img, 'PNG', pageWidth - margin - imgWidth, 10, imgWidth, imgHeight);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });

      // Iniciar o carregamento da imagem
      img.src = clubInfo.logo_url;

      // Aguardar o carregamento e processamento da imagem
      await loadImage;
    } catch (logoError) {
      console.error("Erro ao adicionar logo ao PDF:", logoError);
    }
  }

  // Adicionar data do relatório (após o logo para garantir que fique acima)
  const currentDate = new Date().toLocaleDateString('pt-BR');
  doc.setFontSize(10);
  doc.text(`Data: ${currentDate}`, pageWidth - margin, 30, { align: 'right' });

  // Posição Y inicial para o conteúdo
  let yPosition = 45;

  // Para cada categoria, mostrar os jogadores e seus tamanhos
  clothingData.forEach((categoryData, index) => {
    // Título da categoria
    doc.setFontSize(14);
    doc.setTextColor(0, 0, 0);
    doc.setFillColor(242, 242, 242);
    doc.rect(margin, yPosition, pageWidth - 2 * margin, 8, 'F');
    doc.text(`Categoria: ${categoryData.category}`, margin + 2, yPosition + 5);
    yPosition += 12;

    // Ordenar jogadores por nome
    const sortedPlayers = [...categoryData.players].sort((a, b) => a.name.localeCompare(b.name));

    // Preparar dados para a tabela
    const tableData = sortedPlayers.map(player => [
      player.name || '-',
      player.birthdate ? new Date(player.birthdate).toLocaleDateString('pt-BR') : '-',
      player.shirt_size || '-',
      player.shorts_size || '-',
      player.sock_size || '-',
      player.shoes_size || '-',
      player.training_shirt_size || '-',
      player.training_shorts_size || '-',
      player.jacket_size || '-',
      player.pants_size || '-'
    ]);

    // Adicionar a tabela ao PDF
    autoTable(doc, {
      startY: yPosition,
      head: [['Nome', 'Data Nasc.', 'Camisa', 'Shorts', 'Meias', 'Chuteira', 'Camisa Treino', 'Shorts Treino', 'Jaqueta', 'Calça']],
      body: tableData,
      theme: 'striped',
      headStyles: { fillColor: getClubPrimaryColorRgb(), textColor: [255, 255, 255] },
      margin: { left: margin, right: margin },
      styles: {
        fontSize: 8,
        cellPadding: 3
      },
      columnStyles: {
        0: { cellWidth: 'auto' }, // Nome
        1: { cellWidth: 'auto' }, // Data Nasc.
        2: { cellWidth: 15, halign: 'center' }, // Camisa
        3: { cellWidth: 15, halign: 'center' }, // Shorts
        4: { cellWidth: 15, halign: 'center' }, // Meias
        5: { cellWidth: 15, halign: 'center' }, // Chuteira
        6: { cellWidth: 15, halign: 'center' }, // Camisa Treino
        7: { cellWidth: 15, halign: 'center' }, // Shorts Treino
        8: { cellWidth: 15, halign: 'center' }, // Jaqueta
        9: { cellWidth: 15, halign: 'center' }  // Calça
      },
      didDrawPage: (data) => {
        // Adicionar cabeçalho em cada página
        if (data.pageNumber > 1) {
          doc.setFontSize(10);
          doc.text(title, pageWidth / 2, 10, { align: 'center' });
          doc.setFontSize(8);
          doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, margin, 15);
          doc.text(`Data: ${currentDate}`, pageWidth - margin, 15, { align: 'right' });
        }
      }
    });

    // Atualizar a posição Y para o próximo conteúdo
    const docWithTable = doc as jsPDFWithAutoTable;
    yPosition = docWithTable.lastAutoTable.finalY + 15;

    // Adicionar linha divisória entre categorias (exceto a última)
    if (index < clothingData.length - 1) {
      doc.setDrawColor(200, 200, 200);
      doc.setLineDashPattern([3, 3], 0);
      doc.line(margin, yPosition, pageWidth - margin, yPosition);
      doc.setLineDashPattern([], 0); // Resetar para linha sólida
      yPosition += 10;
    }
  });

  // Adicionar rodapé
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.text(
      `© ${new Date().getFullYear()} ${clubInfo.name} - Todos os direitos reservados`,
      pageWidth / 2,
      pageHeight - 10,
      { align: 'center' }
    );
    doc.text(
      `Página ${i} de ${pageCount}`,
      pageWidth - margin,
      pageHeight - 10,
      { align: 'right' }
    );
  }

  // Salvar o PDF
  doc.save(filename);
}

/**
 * Gera um relatório de alojamentos em PDF
 * @param accommodations Lista de alojamentos
 * @param clubInfo Informações do clube
 * @param playersByCategory Jogadores sem alojamento agrupados por categoria
 * @param filename Nome do arquivo PDF
 */
export async function generateAccommodationReport(
  accommodations: any[],
  clubInfo: ClubInfo,
  playersByCategory: Record<string, any[]> = {},
  filename: string = 'relatorio-alojamentos.pdf'
): Promise<void> {
  // Criar um novo documento PDF
  const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

  // Configurações de página
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const margin = 10;

  // Adicionar título
  const title = 'Relatório de Alojamentos';
  doc.setFontSize(18);
  doc.text(title, pageWidth / 2, 20, { align: 'center' });

  // Adicionar informações do clube
  doc.setFontSize(12);
  doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, margin, 30);

  if (clubInfo.address) {
    doc.text(`Endereço: ${clubInfo.address}`, margin, 35);
  }

  if (clubInfo.phone) {
    doc.text(`Telefone: ${clubInfo.phone}`, margin, 40);
  }

  if (clubInfo.zip_code) {
    doc.text(`CEP: ${clubInfo.zip_code}`, margin, 45);
  }

  // Tentar adicionar o logo do clube, se disponível
  if (clubInfo.logo_url) {
    try {
      const img = new Image();

      // Criar uma promessa para lidar com o carregamento da imagem
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            // Calcular proporção para manter as proporções
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;

            // Adicionar a imagem ao PDF (alinhada à direita)
            doc.addImage(img, 'PNG', pageWidth - margin - imgWidth, 10, imgWidth, imgHeight);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });

      // Iniciar o carregamento da imagem
      img.src = clubInfo.logo_url;

      // Aguardar o carregamento e processamento da imagem
      await loadImage;
    } catch (logoError) {
      console.error("Erro ao adicionar logo ao PDF:", logoError);
    }
  }

  // Adicionar data do relatório (após o logo para garantir que fique acima)
  const currentDate = new Date().toLocaleDateString('pt-BR');
  doc.setFontSize(10);
  doc.text(`Data: ${currentDate}`, pageWidth - margin, 30, { align: 'right' });

  // Posição Y inicial para o conteúdo
  let yPosition = 50;

  // Seção de jogadores sem alojamento
  if (playersByCategory && Object.keys(playersByCategory).length > 0) {
    // Título da seção
    doc.setFontSize(14);
    doc.setTextColor(183, 28, 28); // Cor vermelha para destaque
    doc.text('Jogadores sem Alojamento', margin, yPosition);
    yPosition += 8;

    // Para cada categoria, mostrar os jogadores
    Object.entries(playersByCategory).forEach(([category, players]) => {
      if (players.length === 0) return;

      // Título da categoria
      doc.setFontSize(12);
      doc.setTextColor(0, 0, 0); // Voltar para cor preta
      doc.text(`Categoria: ${category}`, margin, yPosition);
      yPosition += 6;

      // Preparar dados para a tabela
      const tableData = players.map((player: any) => [
        player.name || 'Jogador sem nome',
        player.nickname || '-',
        player.birthdate ? new Date(player.birthdate).toLocaleDateString('pt-BR') : '-',
        player.category || '-'
      ]);

      // Adicionar a tabela ao PDF
      autoTable(doc, {
        startY: yPosition,
        head: [['Nome', 'Apelido', 'Data Nascimento', 'Categoria']],
        body: tableData,
        theme: 'striped',
        headStyles: { fillColor: [0, 87, 183], textColor: [255, 255, 255] },
        margin: { left: margin, right: margin },
        didDrawPage: (data) => {
          // Adicionar cabeçalho em cada página
          if (data.pageNumber > 1) {
            doc.setFontSize(10);
            doc.text(title, pageWidth / 2, 10, { align: 'center' });
            doc.setFontSize(8);
            doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, margin, 15);
            doc.text(`Data: ${currentDate}`, pageWidth - margin, 15, { align: 'right' });
          }
        }
      });

      // Atualizar a posição Y para o próximo conteúdo
      const docWithTable = doc as jsPDFWithAutoTable;
      yPosition = docWithTable.lastAutoTable.finalY + 10;
    });

    // Adicionar linha divisória
    doc.setDrawColor(200, 200, 200);
    doc.line(margin, yPosition, pageWidth - margin, yPosition);
    yPosition += 10;
  }

  // Conteúdo para cada alojamento
  accommodations.forEach((accommodation, index) => {
    // Título do alojamento
    doc.setFontSize(14);
    doc.setTextColor(0, 0, 0);
    doc.setFillColor(242, 242, 242);
    doc.rect(margin, yPosition, pageWidth - 2 * margin, 8, 'F');
    doc.text(accommodation.name, margin + 2, yPosition + 5);
    yPosition += 10;

    // Informações do alojamento
    doc.setFontSize(10);

    // Endereço
    if (accommodation.address) {
      doc.text(`Endereço: ${accommodation.address}`, margin, yPosition);
      yPosition += 5;
    }

    // Capacidade
    doc.text(`Capacidade: ${accommodation.capacity || '-'} pessoas`, margin, yPosition);
    yPosition += 5;

    // Responsável
    if (accommodation.manager) {
      doc.text(`Responsável: ${accommodation.manager}`, margin, yPosition);
      yPosition += 5;
    }

    // Contato
    if (accommodation.contact) {
      doc.text(`Contato: ${accommodation.contact}`, margin, yPosition);
      yPosition += 5;
    }

    yPosition += 5;

    // Tabela de hóspedes (jogadores e colaboradores)
    const hasPlayers = accommodation.players && accommodation.players.length > 0;
    const hasCollaborators = accommodation.collaborators && accommodation.collaborators.length > 0;

    if (hasPlayers || hasCollaborators) {
      // Título da seção de hóspedes
      doc.setFontSize(12);
      doc.text('Hóspedes Alojados', margin, yPosition);
      yPosition += 6;

      // Preparar dados para a tabela
      let tableData: any[] = [];

      // Agrupar hóspedes por quarto (para hotéis)
      if (accommodation.type === 'hotel') {
        // Criar um mapa de quartos e seus hóspedes
        const roomsMap = new Map();

        // Adicionar jogadores ao mapa
        if (accommodation.players) {
          accommodation.players.forEach((player: any) => {
            const roomKey = player.room || 'Sem quarto';
            if (!roomsMap.has(roomKey)) {
              roomsMap.set(roomKey, []);
            }
            roomsMap.get(roomKey).push({
              ...player,
              type: 'Jogador'
            });
          });
        }

        // Adicionar colaboradores ao mapa
        if (accommodation.collaborators) {
          accommodation.collaborators.forEach((collaborator: any) => {
            const roomKey = collaborator.room_number || 'Sem quarto';
            if (!roomsMap.has(roomKey)) {
              roomsMap.set(roomKey, []);
            }
            roomsMap.get(roomKey).push({
              name: collaborator.collaborator_name || collaborator.collaborators?.full_name,
              nickname: '-',
              birthdate: null,
              category: collaborator.collaborator_role || collaborator.collaborators?.role || '-',
              since: collaborator.check_in_date,
              type: 'Colaborador'
            });
          });
        }

        // Ordenar os quartos
        const sortedRooms = Array.from(roomsMap.keys()).sort();

        // Para cada quarto, adicionar os hóspedes
        sortedRooms.forEach(roomNumber => {
          const guestsInRoom = roomsMap.get(roomNumber);

          // Adicionar uma linha de cabeçalho para o quarto
          if (roomNumber !== 'Sem quarto') {
            tableData.push([
              {
                content: `Quarto ${roomNumber} (${guestsInRoom.length} hóspedes)`,
                colSpan: 6,
                styles: {
                  fillColor: [233, 236, 239],
                  fontStyle: 'bold',
                  fontSize: 10
                }
              }
            ]);
          }

          // Adicionar os hóspedes do quarto
          guestsInRoom.forEach((guest: any) => {
            tableData.push([
              guest.name || 'Hóspede sem nome',
              guest.nickname || '-',
              guest.birthdate ? new Date(guest.birthdate).toLocaleDateString('pt-BR') : '-',
              guest.category || '-',
              guest.since ? new Date(guest.since).toLocaleDateString('pt-BR') : '-',
              guest.type || 'Jogador'
            ]);
          });
        });
      } else {
        // Para apartamentos, listar todos os hóspedes normalmente
        const allGuests = [];

        // Adicionar jogadores
        if (accommodation.players) {
          accommodation.players.forEach((player: any) => {
            allGuests.push({
              name: player.name || 'Jogador sem nome',
              nickname: player.nickname || '-',
              birthdate: player.birthdate,
              category: player.category || '-',
              since: player.since,
              type: 'Jogador'
            });
          });
        }

        // Adicionar colaboradores
        if (accommodation.collaborators) {
          accommodation.collaborators.forEach((collaborator: any) => {
            allGuests.push({
              name: collaborator.collaborator_name || collaborator.collaborators?.full_name || 'Colaborador sem nome',
              nickname: '-',
              birthdate: null,
              category: collaborator.collaborator_role || collaborator.collaborators?.role || '-',
              since: collaborator.check_in_date,
              type: 'Colaborador'
            });
          });
        }

        // Ordenar por nome
        allGuests.sort((a, b) => a.name.localeCompare(b.name));

        tableData = allGuests.map((guest: any) => [
          guest.name,
          guest.nickname,
          guest.birthdate ? new Date(guest.birthdate).toLocaleDateString('pt-BR') : '-',
          guest.category,
          guest.since ? new Date(guest.since).toLocaleDateString('pt-BR') : '-',
          guest.type
        ]);
      }

      // Adicionar a tabela ao PDF
      autoTable(doc, {
        startY: yPosition,
        head: [['Nome', 'Apelido', 'Data Nasc.', 'Categoria/Função', 'Check-in', 'Tipo']],
        body: tableData,
        theme: 'striped',
        headStyles: { fillColor: [0, 87, 183], textColor: [255, 255, 255] },
        margin: { left: margin, right: margin },
        columnStyles: {
          0: { cellWidth: 'auto' },
          1: { cellWidth: 'auto' },
          2: { cellWidth: 'auto' },
          3: { cellWidth: 'auto' },
          4: { cellWidth: 'auto' },
          5: { cellWidth: 'auto' }
        },
        didParseCell: function(data) {
          // Aplicar estilos personalizados para células com conteúdo de objeto
          if (data.cell.raw && typeof data.cell.raw === 'object' && data.cell.raw.content) {
            // Aplicar estilos personalizados da célula
            if (data.cell.raw.styles) {
              Object.assign(data.cell.styles, data.cell.raw.styles);
            }

            // Aplicar mesclagem de células
            if (data.cell.raw.colSpan) {
              data.cell.colSpan = data.cell.raw.colSpan;
            }

            // Definir o conteúdo da célula
            data.cell.text = [data.cell.raw.content];
          }
        },
        didDrawPage: (data) => {
          // Adicionar cabeçalho em cada página
          if (data.pageNumber > 1) {
            doc.setFontSize(10);
            doc.text(title, pageWidth / 2, 10, { align: 'center' });
            doc.setFontSize(8);
            doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, margin, 15);
            doc.text(`Data: ${currentDate}`, pageWidth - margin, 15, { align: 'right' });
          }
        }
      });

      // Atualizar a posição Y para o próximo conteúdo
      const docWithTable = doc as jsPDFWithAutoTable;
      yPosition = docWithTable.lastAutoTable.finalY + 15;
    } else {
      // Mensagem de nenhum hóspede alojado
      doc.setFontSize(10);
      doc.setTextColor(100, 100, 100);
      // Use setFont with italic style instead of setFontStyle
      doc.setFont(undefined, 'italic');
      doc.text('Nenhum hóspede alojado.', margin, yPosition);
      yPosition += 10;
    }

    // Adicionar linha divisória entre alojamentos (exceto o último)
    if (index < accommodations.length - 1) {
      doc.setDrawColor(200, 200, 200);
      doc.setLineDashPattern([3, 3], 0);
      doc.line(margin, yPosition, pageWidth - margin, yPosition);
      doc.setLineDashPattern([], 0); // Resetar para linha sólida
      yPosition += 10;
    }
  });

  // Adicionar rodapé
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.text(
      `© ${new Date().getFullYear()} ${clubInfo.name} - Todos os direitos reservados`,
      pageWidth / 2,
      pageHeight - 10,
      { align: 'center' }
    );
    doc.text(
      `Página ${i} de ${pageCount}`,
      pageWidth - margin,
      pageHeight - 10,
      { align: 'right' }
    );
  }

  // Salvar o PDF
  doc.save(filename);
}

// Exportar a nova função de relatório de rouparia
export { generateClothingReport };