import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { createPlayerWithAccount } from "@/api/api";
import { validateCPF } from "@/api/external";

interface PlayerAccountFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  playerId: string;
  playerName: string;
  playerEmail?: string;
  onSuccess?: () => void;
}

export function PlayerAccountForm({
  open,
  onOpenChange,
  playerId,
  playerName,
  playerEmail = "",
  onSuccess,
}: PlayerAccountFormProps) {
  const clubId = useCurrentClubId();
  const [email, setEmail] = useState(playerEmail);
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [sendInvite, setSendInvite] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Função para criar conta de jogador
  const handleCreateAccount = async () => {
    // Validar campos
    if (!email.trim()) {
      setError("Email é obrigatório");
      return;
    }

    if (!sendInvite) {
      if (!password.trim()) {
        setError("Senha é obrigatória");
        return;
      }

      if (password.length < 6) {
        setError("A senha deve ter pelo menos 6 caracteres");
        return;
      }

      if (password !== confirmPassword) {
        setError("As senhas não coincidem");
        return;
      }
    }

    try {
      setLoading(true);
      setError(null);

      await createPlayerWithAccount(
        clubId,
        playerId,
        email,
        sendInvite ? undefined : password
      );

      toast({
        title: "Sucesso",
        description: sendInvite
          ? "Conta criada e credenciais enviadas por email"
          : "Conta criada com sucesso",
      });

      onOpenChange(false);

      if (onSuccess) {
        onSuccess();
      }
    } catch (err: any) {
      console.error("Erro ao criar conta de jogador:", err);
      setError(err.message || "Erro ao criar conta de jogador");
      toast({
        title: "Erro",
        description: err.message || "Erro ao criar conta de jogador",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Criar Conta para {playerName}</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email*</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Email do jogador"
              required
            />
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="sendInvite"
              checked={sendInvite}
              onCheckedChange={(checked) => setSendInvite(checked as boolean)}
            />
            <Label htmlFor="sendInvite" className="cursor-pointer">
              Gerar senha aleatória e enviar por email (recomendado)
            </Label>
          </div>

          {!sendInvite && (
            <>
              <div className="space-y-2">
                <Label htmlFor="password">Senha*</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Senha"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirmar Senha*</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="Confirmar senha"
                  required
                />
              </div>
            </>
          )}

          {error && <p className="text-red-500 text-sm">{error}</p>}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleCreateAccount} disabled={loading}>
            {loading ? "Criando..." : "Criar Conta"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
