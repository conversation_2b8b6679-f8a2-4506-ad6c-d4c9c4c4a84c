import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { YouthPlayerDialog } from "@/components/modals/YouthPlayerDialog";
import { useYouthPlayersStore } from "@/store/useYouthPlayersStore";
import { Badge } from "@/components/ui/badge";
import { useCurrentClubId } from "@/context/ClubContext";
import { useToast } from "@/hooks/use-toast";
import type { YouthPlayer } from "@/api/api";

const positionMap: Record<string, string> = {
  "goleiro": "GOL",
  "zagueiro": "ZAG",
  "lateral-direito": "LD",
  "lateral-esquerdo": "LE",
  "volante": "VOL",
  "meia": "MEI",
  "atacante": "ATA",
  "extremo-direito": "PD",
  "extremo-esquerdo": "PE"
};

const statusColors: Record<string, string> = {
  "ativo": "text-green-700 bg-green-100",
  "lesionado": "text-red-700 bg-red-100",
  "emprestado": "text-amber-700 bg-amber-100",
  "suspenso": "text-purple-700 bg-purple-100"
};

export default function BaseJuvenil() {
  const clubId = useCurrentClubId();
  const [searchQuery, setSearchQuery] = useState("");
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedPlayer, setSelectedPlayer] = useState<YouthPlayer | null>(null);
  const { youthPlayers, loading, error, fetchYouthPlayers, deleteYouthPlayer } = useYouthPlayersStore();
  const navigate = useNavigate();
  const { toast } = useToast();

  useEffect(() => {
    fetchYouthPlayers(clubId);
  }, [clubId]);

  useEffect(() => {
    if (error) {
      toast({
        title: "Erro",
        description: error,
        variant: "destructive"
      });
    }
  }, [error]);

  const filteredPlayers = youthPlayers.filter((player) => {
    const query = searchQuery.toLowerCase();
    return (
      player.name.toLowerCase().includes(query) ||
      player.position.toLowerCase().includes(query) ||
      (player.nationality ? player.nationality.toLowerCase().includes(query) : false)
    );
  });

  const handleAddPlayer = () => {
    setSelectedPlayer(null);
    setOpenDialog(true);
  };

  const handleEditPlayer = (player: YouthPlayer) => {
    setSelectedPlayer(player);
    setOpenDialog(true);
  };

  const handleDeletePlayer = async (player: YouthPlayer) => {
    if (window.confirm(`Tem certeza que deseja remover ${player.name} da base?`)) {
      try {
        await deleteYouthPlayer(clubId, player.id);
        toast({
          title: "Jogador removido",
          description: `${player.name} foi removido da base com sucesso.`,
          variant: "default"
        });
      } catch (err) {
        toast({
          title: "Erro ao remover jogador",
          description: error || "Ocorreu um erro ao remover o jogador.",
          variant: "destructive"
        });
      }
    }
  };

  const groupPlayersByCategory = () => {
    const u15 = filteredPlayers.filter(p => p.age < 15);
    const u17 = filteredPlayers.filter(p => p.age >= 15 && p.age < 17);
    const u20 = filteredPlayers.filter(p => p.age >= 17 && p.age <= 20);
    return { u15, u17, u20 };
  };

  const { u15, u17, u20 } = groupPlayersByCategory();

  const renderPlayerTable = (players: YouthPlayer[], categoryTitle: string) => (
    <Card className="mb-8">
      <CardHeader>
        <CardTitle className="text-xl">{categoryTitle}</CardTitle>
      </CardHeader>
      <CardContent>
        {players.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">#</TableHead>
                <TableHead>Nome</TableHead>
                <TableHead className="hidden md:table-cell">Posição</TableHead>
                <TableHead className="hidden md:table-cell">Idade</TableHead>
                <TableHead className="hidden sm:table-cell">Status</TableHead>
                <TableHead className="w-24 text-right">Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {players.map((player) => (
                <TableRow key={player.id}>
                  <TableCell className="font-medium">{player.number}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {player.image ? (
                        <div className="h-8 w-8 rounded-full overflow-hidden">
                          <img
                            src={player.image}
                            alt={player.name}
                            className="h-full w-full object-cover"
                          />
                        </div>
                      ) : (
                        <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center">
                          <span className="text-xs font-medium">{player.name.charAt(0)}</span>
                        </div>
                      )}
                      <span>{player.name}</span>
                    </div>
                  </TableCell>
                  <TableCell className="hidden md:table-cell">
                    {positionMap[player.position] || player.position}
                  </TableCell>
                  <TableCell className="hidden md:table-cell">{player.age}</TableCell>
                  <TableCell className="hidden sm:table-cell">
                    <Badge className={statusColors[player.status] || "bg-gray-100 text-gray-700"}>
                      {player.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditPlayer(player)}
                      >
                        Editar
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-red-500 hover:text-red-700 hover:bg-red-50"
                        onClick={() => handleDeletePlayer(player)}
                      >
                        Excluir
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            Nenhum jogador encontrado nesta categoria
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <div>
      <div className="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Base Juvenil</h1>
          <p className="text-muted-foreground">
            Gerencie os jogadores das categorias de base
          </p>
        </div>
        <Button onClick={handleAddPlayer}>Adicionar Jogador</Button>
      </div>

      <div className="mb-6">
        <Input
          placeholder="Buscar jogador por nome, posição ou nacionalidade..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-lg"
        />
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-team-blue"></div>
        </div>
      ) : (
        <>
          {renderPlayerTable(u20, "Sub-20")}
          {renderPlayerTable(u17, "Sub-17")}
          {renderPlayerTable(u15, "Sub-15")}

          {filteredPlayers.length === 0 && (
            <div className="text-center py-12">
              <h3 className="text-lg font-medium mb-2">Nenhum jogador encontrado</h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery
                  ? "Tente com outros termos de busca ou adicione novos jogadores."
                  : "Adicione jogadores à base juvenil para começar."}
              </p>
              <Button onClick={handleAddPlayer}>Adicionar Jogador</Button>
            </div>
          )}
        </>
      )}

      <YouthPlayerDialog
        open={openDialog}
        onOpenChange={setOpenDialog}
        clubId={clubId}
        player={selectedPlayer}
      />
    </div>
  );
}
