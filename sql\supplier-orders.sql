-- Create supplier_orders table for managing orders from suppliers
CREATE TABLE IF NOT EXISTS supplier_orders (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  supplier_id INTEGER REFERENCES suppliers(id) NOT NULL,
  description TEXT NOT NULL,
  purchase_date DATE NOT NULL DEFAULT CURRENT_DATE,
  amount NUMERIC NOT NULL CHECK (amount > 0),
  financial_account_id INTEGER REFERENCES financial_accounts(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_supplier_orders_club_id ON supplier_orders(club_id);
CREATE INDEX IF NOT EXISTS idx_supplier_orders_supplier_id ON supplier_orders(supplier_id);
CREATE INDEX IF NOT EXISTS idx_supplier_orders_financial_account_id ON supplier_orders(financial_account_id);

-- Create a view that joins supplier orders with supplier information
CREATE OR REPLACE VIEW supplier_orders_view AS
SELECT 
  so.*,
  s.company_name as supplier_name,
  fa.description as account_description,
  fa.status as account_status,
  fa.due_date as account_due_date
FROM 
  supplier_orders so
LEFT JOIN 
  suppliers s ON so.supplier_id = s.id
LEFT JOIN 
  financial_accounts fa ON so.financial_account_id = fa.id;

-- Add a trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_supplier_orders_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_supplier_orders_updated_at
BEFORE UPDATE ON supplier_orders
FOR EACH ROW
EXECUTE FUNCTION update_supplier_orders_updated_at();
